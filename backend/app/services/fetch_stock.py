"""
股票数据拉取服务
"""
import akshare as ak
import pandas as pd
from typing import List, Optional, Dict
from datetime import datetime, date
from sqlalchemy import text
from shared.config.db import engine

class StockDataFetcher:
    """股票数据拉取器"""

    def __init__(self):
        self.engine = engine

    def fetch_and_store_stock(self, symbol: str, start_date: Optional[str] = None,
                             end_date: Optional[str] = None) -> Dict:
        """
        拉取并存储股票数据

        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            操作结果
        """
        print(f"正在获取 {symbol} 的数据...")

        try:
            # AkShare 要用不带前缀的 symbol
            raw_code = symbol.replace("sh", "").replace("sz", "")

            # 获取股票历史数据
            df = ak.stock_zh_a_hist(
                symbol=raw_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"
            )

            if df.empty:
                return {"success": False, "message": f"{symbol} 没有返回任何数据"}

            # 数据预处理
            df = self._preprocess_data(df, symbol)

            # 存储到数据库
            result = self._store_to_database(df, symbol)

            return result

        except Exception as e:
            error_msg = f"获取 {symbol} 数据出错：{e}"
            print(error_msg)
            return {"success": False, "message": error_msg}

    def fetch_stock_info(self, symbol: str) -> Dict:
        """
        获取股票基础信息

        Args:
            symbol: 股票代码

        Returns:
            股票信息
        """
        try:
            raw_code = symbol.replace("sh", "").replace("sz", "")

            # 获取股票基本信息
            stock_info = ak.stock_individual_info_em(symbol=raw_code)

            if stock_info.empty:
                return {"success": False, "message": f"未找到 {symbol} 的基础信息"}

            # 解析股票信息
            info_dict = {}
            for _, row in stock_info.iterrows():
                info_dict[row['item']] = row['value']

            # 存储股票基础信息
            self._store_stock_info(symbol, info_dict)

            return {"success": True, "data": info_dict}

        except Exception as e:
            error_msg = f"获取 {symbol} 基础信息出错：{e}"
            print(error_msg)
            return {"success": False, "message": error_msg}

    def fetch_all_stock_list(self) -> Dict:
        """
        获取所有A股股票列表

        Returns:
            股票列表
        """
        try:
            # 获取沪深A股股票列表
            sh_stocks = ak.stock_info_sh_name_code()
            sz_stocks = ak.stock_info_sz_name_code()

            # 处理上海股票
            sh_stocks['market'] = 'SH'
            sh_stocks['code'] = 'sh' + sh_stocks['证券代码'].astype(str)

            # 处理深圳股票
            sz_stocks['market'] = 'SZ'
            sz_stocks['code'] = 'sz' + sz_stocks['证券代码'].astype(str)

            # 合并数据
            all_stocks = pd.concat([
                sh_stocks[['code', '证券简称', 'market']].rename(columns={'证券简称': 'name'}),
                sz_stocks[['code', '证券简称', 'market']].rename(columns={'证券简称': 'name'})
            ], ignore_index=True)

            # 存储到数据库
            self._store_stock_list(all_stocks)

            return {"success": True, "count": len(all_stocks), "data": all_stocks.to_dict('records')}

        except Exception as e:
            error_msg = f"获取股票列表出错：{e}"
            print(error_msg)
            return {"success": False, "message": error_msg}

    def _preprocess_data(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        数据预处理

        Args:
            df: 原始数据
            symbol: 股票代码

        Returns:
            处理后的数据
        """
        # 标准化列名
        column_mapping = {
            '日期': '日期',
            '开盘': '开盘',
            '收盘': '收盘',
            '最高': '最高',
            '最低': '最低',
            '成交量': '成交量',
            '成交额': '成交额',
            '振幅': '振幅',
            '涨跌幅': '涨跌幅',
            '涨跌额': '涨跌额',
            '换手率': '换手率'
        }

        # 重命名列
        df = df.rename(columns=column_mapping)

        # 添加股票代码
        df['code'] = symbol

        # 转换日期格式
        df['日期'] = pd.to_datetime(df['日期'])

        # 数据类型转换
        numeric_columns = ['开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 去除重复数据
        df = df.drop_duplicates(subset=['code', '日期'])

        return df

    def _store_to_database(self, df: pd.DataFrame, symbol: str) -> Dict:
        """
        存储数据到数据库

        Args:
            df: 数据DataFrame
            symbol: 股票代码

        Returns:
            存储结果
        """
        try:
            # 使用 ON DUPLICATE KEY UPDATE 处理重复数据
            df.to_sql(name="stock_daily", con=self.engine, index=False, if_exists="append")

            return {
                "success": True,
                "message": f"{symbol} 数据写入成功，共 {len(df)} 行",
                "count": len(df)
            }

        except Exception as e:
            return {"success": False, "message": f"数据存储失败: {e}"}

    def _store_stock_info(self, symbol: str, info_dict: Dict):
        """
        存储股票基础信息

        Args:
            symbol: 股票代码
            info_dict: 股票信息字典
        """
        try:
            market = symbol[:2].upper()

            sql = """
                INSERT INTO stock_info (code, name, market, industry, list_date, status)
                VALUES (:code, :name, :market, :industry, :list_date, 'active')
                ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                industry = VALUES(industry),
                list_date = VALUES(list_date),
                updated_at = CURRENT_TIMESTAMP
            """

            params = {
                "code": symbol,
                "name": info_dict.get("股票简称", ""),
                "market": market,
                "industry": info_dict.get("所属行业", ""),
                "list_date": info_dict.get("上市时间", None)
            }

            with self.engine.connect() as conn:
                conn.execute(text(sql), params)
                conn.commit()

        except Exception as e:
            print(f"存储股票信息失败: {e}")

    def _store_stock_list(self, df: pd.DataFrame):
        """
        存储股票列表

        Args:
            df: 股票列表DataFrame
        """
        try:
            df.to_sql(name="stock_info", con=self.engine, index=False, if_exists="append")
            print(f"✅ 股票列表存储成功，共 {len(df)} 只股票")

        except Exception as e:
            print(f"❌ 股票列表存储失败: {e}")

# 创建全局实例
stock_fetcher = StockDataFetcher()
