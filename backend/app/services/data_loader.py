"""
股票数据加载服务
"""
import pandas as pd
from typing import Optional, List
from datetime import datetime, date
from sqlalchemy import text
from shared.config.db import engine

class DataLoader:
    """股票数据加载器"""

    def __init__(self):
        self.engine = engine

    def load_stock_data(self, stock_code: str, start_date: Optional[date] = None,
                       end_date: Optional[date] = None) -> pd.DataFrame:
        """
        加载股票数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            股票数据DataFrame
        """
        sql = """
            SELECT 日期, 开盘, 收盘, 最高, 最低, 成交量, 成交额, 振幅, 涨跌幅, 涨跌额, 换手率
            FROM stock_daily
            WHERE code = :stock_code
        """

        params = {"stock_code": stock_code}

        if start_date:
            sql += " AND 日期 >= :start_date"
            params["start_date"] = start_date

        if end_date:
            sql += " AND 日期 <= :end_date"
            params["end_date"] = end_date

        sql += " ORDER BY 日期 ASC"

        df = pd.read_sql(text(sql), self.engine, params=params)
        return df

    def get_stock_list(self, market: Optional[str] = None) -> List[dict]:
        """
        获取股票列表

        Args:
            market: 市场代码 (SH/SZ)

        Returns:
            股票列表
        """
        sql = "SELECT code, name, market, industry FROM stock_info WHERE status = 'active'"
        params = {}

        if market:
            sql += " AND market = :market"
            params["market"] = market

        sql += " ORDER BY code"

        df = pd.read_sql(text(sql), self.engine, params=params)
        return df.to_dict('records')

    def get_date_range(self, stock_code: str) -> dict:
        """
        获取股票数据的日期范围

        Args:
            stock_code: 股票代码

        Returns:
            日期范围信息
        """
        sql = """
            SELECT MIN(日期) as min_date, MAX(日期) as max_date, COUNT(*) as total_days
            FROM stock_daily
            WHERE code = :stock_code
        """

        df = pd.read_sql(text(sql), self.engine, params={"stock_code": stock_code})
        if not df.empty:
            min_date = df.iloc[0]["min_date"]
            max_date = df.iloc[0]["max_date"]
            total_days = df.iloc[0]["total_days"]

            return {
                "min_date": min_date.isoformat() if min_date else None,
                "max_date": max_date.isoformat() if max_date else None,
                "total_days": int(total_days) if total_days else 0
            }
        return {"min_date": None, "max_date": None, "total_days": 0}

# 创建全局实例
data_loader = DataLoader()
