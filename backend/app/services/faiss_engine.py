"""
FAISS向量检索引擎
"""
import faiss
import numpy as np
import pandas as pd
import os
import pickle
from typing import Tuple, List, Dict, Optional
from datetime import datetime, date
from backend.app.core.config import settings

class FaissEngine:
    """FAISS向量检索引擎"""

    def __init__(self):
        self.index_dir = settings.faiss_index_dir
        self.index_type = settings.faiss_index_type
        self.indexes = {}  # 缓存已加载的索引

        # 确保索引目录存在
        os.makedirs(self.index_dir, exist_ok=True)

    def build_index(self, vectors: np.ndarray, index_name: str = "default",
                   save_to_disk: bool = True) -> faiss.Index:
        """
        构建FAISS索引

        Args:
            vectors: 特征向量数组
            index_name: 索引名称
            save_to_disk: 是否保存到磁盘

        Returns:
            FAISS索引对象
        """
        if len(vectors) == 0:
            raise ValueError("向量数组为空")

        dim = vectors.shape[1]

        # 根据数据量选择索引类型
        if len(vectors) < 10000:
            index = faiss.IndexFlatL2(dim)
        else:
            # 对于大数据量使用IVF索引
            nlist = min(int(np.sqrt(len(vectors))), 1000)
            quantizer = faiss.IndexFlatL2(dim)
            index = faiss.IndexIVFFlat(quantizer, dim, nlist)
            index.train(vectors)

        index.add(vectors)

        # 缓存索引
        self.indexes[index_name] = index

        # 保存到磁盘
        if save_to_disk:
            self._save_index(index, index_name, vectors.shape)

        return index

    def load_index(self, index_name: str) -> Optional[faiss.Index]:
        """
        从磁盘加载索引

        Args:
            index_name: 索引名称

        Returns:
            FAISS索引对象
        """
        if index_name in self.indexes:
            return self.indexes[index_name]

        index_path = os.path.join(self.index_dir, f"{index_name}.index")
        if os.path.exists(index_path):
            try:
                index = faiss.read_index(index_path)
                self.indexes[index_name] = index
                return index
            except Exception as e:
                print(f"加载索引失败: {e}")
                return None

        return None

    def query_top_k(self, query_vector: np.ndarray, k: int = 10,
                   index_name: str = "default") -> Tuple[np.ndarray, np.ndarray]:
        """
        查询最相似的K个向量

        Args:
            query_vector: 查询向量
            k: 返回结果数量
            index_name: 索引名称

        Returns:
            (距离数组, 索引数组)
        """
        index = self.indexes.get(index_name) or self.load_index(index_name)
        if index is None:
            raise ValueError(f"索引 {index_name} 不存在")

        if query_vector.ndim == 1:
            query_vector = query_vector.reshape(1, -1)

        distances, indices = index.search(query_vector, k)
        return distances, indices

    def batch_query(self, query_vectors: np.ndarray, k: int = 10,
                   index_name: str = "default") -> Tuple[np.ndarray, np.ndarray]:
        """
        批量查询

        Args:
            query_vectors: 查询向量数组
            k: 每个查询返回的结果数量
            index_name: 索引名称

        Returns:
            (距离数组, 索引数组)
        """
        index = self.indexes.get(index_name) or self.load_index(index_name)
        if index is None:
            raise ValueError(f"索引 {index_name} 不存在")

        distances, indices = index.search(query_vectors, k)
        return distances, indices

    def _save_index(self, index: faiss.Index, index_name: str, shape: Tuple[int, int]):
        """
        保存索引到磁盘

        Args:
            index: FAISS索引对象
            index_name: 索引名称
            shape: 向量数组形状
        """
        try:
            # 保存索引文件
            index_path = os.path.join(self.index_dir, f"{index_name}.index")
            faiss.write_index(index, index_path)

            # 保存元数据
            meta_path = os.path.join(self.index_dir, f"{index_name}.meta")
            metadata = {
                "index_name": index_name,
                "total_vectors": shape[0],
                "feature_dim": shape[1],
                "index_type": self.index_type,
                "build_time": datetime.now().isoformat(),
                "index_path": index_path
            }

            with open(meta_path, 'wb') as f:
                pickle.dump(metadata, f)

            print(f"✅ 索引 {index_name} 保存成功")

        except Exception as e:
            print(f"❌ 保存索引失败: {e}")

    def get_index_info(self, index_name: str) -> Optional[Dict]:
        """
        获取索引信息

        Args:
            index_name: 索引名称

        Returns:
            索引信息字典
        """
        meta_path = os.path.join(self.index_dir, f"{index_name}.meta")
        if os.path.exists(meta_path):
            try:
                with open(meta_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                print(f"读取索引元数据失败: {e}")
        return None

    def list_indexes(self) -> List[str]:
        """
        列出所有可用的索引

        Returns:
            索引名称列表
        """
        indexes = []
        if os.path.exists(self.index_dir):
            for file in os.listdir(self.index_dir):
                if file.endswith('.index'):
                    indexes.append(file[:-6])  # 去掉.index后缀
        return indexes

    def delete_index(self, index_name: str) -> bool:
        """
        删除索引

        Args:
            index_name: 索引名称

        Returns:
            是否删除成功
        """
        try:
            # 从内存中删除
            if index_name in self.indexes:
                del self.indexes[index_name]

            # 从磁盘删除
            index_path = os.path.join(self.index_dir, f"{index_name}.index")
            meta_path = os.path.join(self.index_dir, f"{index_name}.meta")

            if os.path.exists(index_path):
                os.remove(index_path)
            if os.path.exists(meta_path):
                os.remove(meta_path)

            print(f"✅ 索引 {index_name} 删除成功")
            return True

        except Exception as e:
            print(f"❌ 删除索引失败: {e}")
            return False

# 创建全局实例
faiss_engine = FaissEngine()
