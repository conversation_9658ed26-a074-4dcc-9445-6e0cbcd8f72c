"""
K线特征提取引擎
"""
import numpy as np
import pandas as pd
import hashlib
import json
from typing import Tuple, List, Dict, Optional
from datetime import date
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from backend.app.core.config import settings

class FeatureEngine:
    """K线特征提取引擎"""

    def __init__(self):
        self.normalization_method = settings.feature_normalization
        self.scaler = None

    def extract_kline_features(self, df: pd.DataFrame, window_size: int = 5,
                              normalize: bool = True) -> Tuple[np.ndarray, List[int], List[date]]:
        """
        提取K线特征向量

        Args:
            df: 股票数据DataFrame
            window_size: 窗口大小
            normalize: 是否标准化

        Returns:
            (特征向量数组, 索引列表, 日期列表)
        """
        if len(df) < window_size:
            return np.array([]), [], []

        vectors = []
        indices = []
        dates = []

        for i in range(len(df) - window_size + 1):
            segment = df.iloc[i:i + window_size]
            features = self._extract_segment_features(segment)

            if features is not None:
                vectors.append(features)
                indices.append(i)
                dates.append(segment.iloc[0]["日期"])

        if not vectors:
            return np.array([]), [], []

        vectors = np.array(vectors, dtype='float32')

        # 标准化处理
        if normalize and len(vectors) > 0:
            vectors = self._normalize_features(vectors)

        return vectors, indices, dates

    def _extract_segment_features(self, segment: pd.DataFrame) -> Optional[List[float]]:
        """
        提取单个片段的特征

        Args:
            segment: K线片段数据

        Returns:
            特征列表
        """
        try:
            features = []
            prev_close = None

            for j in range(len(segment)):
                row = segment.iloc[j]

                # 基础价格特征
                open_price = float(row["开盘"])
                close_price = float(row["收盘"])
                high_price = float(row["最高"])
                low_price = float(row["最低"])
                volume = float(row["成交量"])

                if j == 0:
                    prev_close = open_price

                # 1. 涨跌幅
                price_change = (close_price - prev_close) / prev_close if prev_close > 0 else 0

                # 2. 振幅 (最高最低价差/前收盘价)
                amplitude = (high_price - low_price) / prev_close if prev_close > 0 else 0

                # 3. 实体长度 (开盘收盘价差/前收盘价)
                body_length = abs(close_price - open_price) / prev_close if prev_close > 0 else 0

                # 4. 上影线长度
                upper_shadow = (high_price - max(open_price, close_price)) / prev_close if prev_close > 0 else 0

                # 5. 下影线长度
                lower_shadow = (min(open_price, close_price) - low_price) / prev_close if prev_close > 0 else 0

                # 6. 成交量变化率 (对数变换)
                volume_ratio = np.log1p(volume / 1000000)  # 转换为百万股并取对数

                features.extend([
                    price_change, amplitude, body_length,
                    upper_shadow, lower_shadow, volume_ratio
                ])

                prev_close = close_price

            return features

        except Exception as e:
            print(f"特征提取错误: {e}")
            return None

    def _normalize_features(self, vectors: np.ndarray) -> np.ndarray:
        """
        标准化特征向量

        Args:
            vectors: 原始特征向量

        Returns:
            标准化后的特征向量
        """
        if self.normalization_method == "standard":
            if self.scaler is None:
                self.scaler = StandardScaler()
            return self.scaler.fit_transform(vectors)
        elif self.normalization_method == "minmax":
            if self.scaler is None:
                self.scaler = MinMaxScaler()
            return self.scaler.fit_transform(vectors)
        else:
            return vectors

    def calculate_feature_hash(self, features: List[float]) -> str:
        """
        计算特征向量的哈希值

        Args:
            features: 特征向量

        Returns:
            哈希值
        """
        feature_str = json.dumps(features, sort_keys=True)
        return hashlib.sha256(feature_str.encode()).hexdigest()

    def get_feature_dimension(self, window_size: int) -> int:
        """
        获取特征维度

        Args:
            window_size: 窗口大小

        Returns:
            特征维度
        """
        return window_size * 6  # 每个时间点6个特征

# 创建全局实例
feature_engine = FeatureEngine()
