"""
应用配置模块
"""
import os
from typing import Optional
from pydantic import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "股票数据分析API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    mysql_user: str = "root"
    mysql_password: str = "SD0916sd!"
    mysql_host: str = "localhost"
    mysql_port: str = "3306"
    mysql_db: str = "stock"
    
    # API配置
    api_prefix: str = "/api/v1"
    host: str = "0.0.0.0"
    port: int = 8000
    
    # CORS配置
    cors_origins: list = ["http://localhost:3000", "http://localhost:8501"]
    
    # 特征工程配置
    default_window_size: int = 5
    default_top_k: int = 10
    feature_normalization: str = "standard"
    
    # FAISS配置
    faiss_index_dir: str = "data/faiss_indexes"
    faiss_index_type: str = "IndexFlatL2"
    
    # 日志配置
    log_level: str = "INFO"
    log_dir: str = "logs"
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}@{self.mysql_host}:{self.mysql_port}/{self.mysql_db}?charset=utf8mb4"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 创建全局配置实例
settings = Settings()
