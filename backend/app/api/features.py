"""
特征工程API路由
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List
from datetime import date
from pydantic import BaseModel
import numpy as np
import json

from backend.app.services.data_loader import data_loader
from backend.app.services.feature_engine import feature_engine
from backend.app.services.faiss_engine import faiss_engine

router = APIRouter(prefix="/features", tags=["特征工程"])

class FeatureExtractionRequest(BaseModel):
    """特征提取请求模型"""
    stock_code: str
    window_size: int = 5
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    normalize: bool = True

class FeatureResponse(BaseModel):
    """特征提取响应模型"""
    stock_code: str
    window_size: int
    feature_count: int
    feature_dimension: int
    features: List[dict]

@router.post("/extract", summary="提取K线特征")
async def extract_features(request: FeatureExtractionRequest):
    """提取指定股票的K线特征向量"""
    try:
        # 加载股票数据
        start_date_obj = date.fromisoformat(request.start_date) if request.start_date else None
        end_date_obj = date.fromisoformat(request.end_date) if request.end_date else None
        
        df = data_loader.load_stock_data(request.stock_code, start_date_obj, end_date_obj)
        
        if df.empty:
            raise HTTPException(status_code=404, detail=f"未找到股票 {request.stock_code} 的数据")
        
        # 提取特征
        vectors, indices, dates = feature_engine.extract_kline_features(
            df, request.window_size, request.normalize
        )
        
        if len(vectors) == 0:
            raise HTTPException(status_code=400, detail="数据不足，无法提取特征")
        
        # 构建响应数据
        features = []
        for i, (vector, idx, date_val) in enumerate(zip(vectors, indices, dates)):
            features.append({
                "index": i,
                "data_index": idx,
                "start_date": date_val.isoformat(),
                "feature_vector": vector.tolist(),
                "feature_hash": feature_engine.calculate_feature_hash(vector.tolist())
            })
        
        return FeatureResponse(
            stock_code=request.stock_code,
            window_size=request.window_size,
            feature_count=len(features),
            feature_dimension=len(vectors[0]),
            features=features
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/build-index", summary="构建FAISS索引")
async def build_faiss_index(
    stock_codes: List[str],
    window_size: int = Query(5, description="窗口大小"),
    index_name: str = Query("default", description="索引名称")
):
    """为指定股票构建FAISS索引"""
    try:
        all_vectors = []
        all_metadata = []
        
        for stock_code in stock_codes:
            # 加载股票数据
            df = data_loader.load_stock_data(stock_code)
            
            if df.empty:
                continue
            
            # 提取特征
            vectors, indices, dates = feature_engine.extract_kline_features(df, window_size)
            
            if len(vectors) == 0:
                continue
            
            # 添加到总向量集合
            all_vectors.append(vectors)
            
            # 记录元数据
            for i, (idx, date_val) in enumerate(zip(indices, dates)):
                all_metadata.append({
                    "stock_code": stock_code,
                    "vector_index": len(all_metadata),
                    "data_index": idx,
                    "start_date": date_val.isoformat(),
                    "window_size": window_size
                })
        
        if not all_vectors:
            raise HTTPException(status_code=400, detail="没有可用的特征向量")
        
        # 合并所有向量
        combined_vectors = np.vstack(all_vectors)
        
        # 构建索引
        index = faiss_engine.build_index(combined_vectors, index_name)
        
        # 保存元数据
        metadata_path = f"data/faiss_indexes/{index_name}_metadata.json"
        import os
        os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(all_metadata, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "index_name": index_name,
            "total_vectors": len(combined_vectors),
            "feature_dimension": combined_vectors.shape[1],
            "stock_count": len(stock_codes),
            "window_size": window_size
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/indexes", summary="获取索引列表")
async def get_index_list():
    """获取所有可用的FAISS索引"""
    try:
        indexes = faiss_engine.list_indexes()
        index_info = []
        
        for index_name in indexes:
            info = faiss_engine.get_index_info(index_name)
            if info:
                index_info.append(info)
        
        return {"success": True, "indexes": index_info}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/indexes/{index_name}", summary="删除索引")
async def delete_index(index_name: str):
    """删除指定的FAISS索引"""
    try:
        success = faiss_engine.delete_index(index_name)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"索引 {index_name} 不存在或删除失败")
        
        return {"success": True, "message": f"索引 {index_name} 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dimension", summary="获取特征维度")
async def get_feature_dimension(window_size: int = Query(5, description="窗口大小")):
    """获取指定窗口大小的特征维度"""
    try:
        dimension = feature_engine.get_feature_dimension(window_size)
        return {
            "success": True,
            "window_size": window_size,
            "feature_dimension": dimension,
            "features_per_day": 6
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
