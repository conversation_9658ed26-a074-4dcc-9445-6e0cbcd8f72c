"""
数据管理API路由
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List
from datetime import date
from pydantic import BaseModel

from backend.app.services.data_loader import data_loader
from backend.app.services.fetch_stock import stock_fetcher

router = APIRouter(prefix="/data", tags=["数据管理"])

class StockDataResponse(BaseModel):
    """股票数据响应模型"""
    code: str
    data: List[dict]
    total_count: int
    date_range: dict

class FetchRequest(BaseModel):
    """数据拉取请求模型"""
    symbol: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None

@router.get("/stocks", summary="获取股票列表")
async def get_stock_list(market: Optional[str] = Query(None, description="市场代码(SH/SZ)")):
    """获取股票列表"""
    try:
        stocks = data_loader.get_stock_list(market)
        return {"success": True, "data": stocks, "count": len(stocks)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stocks/{stock_code}", summary="获取股票数据")
async def get_stock_data(
    stock_code: str,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取指定股票的历史数据"""
    try:
        # 获取股票数据
        df = data_loader.load_stock_data(stock_code, start_date, end_date)

        if df.empty:
            raise HTTPException(status_code=404, detail=f"未找到股票 {stock_code} 的数据")

        # 获取日期范围信息
        date_range = data_loader.get_date_range(stock_code)

        # 简化数据处理，只返回前10条记录用于测试
        sample_data = df.head(10)

        # 手动转换数据类型
        records = []
        for _, row in sample_data.iterrows():
            record = {}
            for col in sample_data.columns:
                value = row[col]
                if pd.isna(value):
                    record[col] = None
                elif isinstance(value, (pd.Timestamp, pd.Timestamp)):
                    record[col] = value.isoformat()
                else:
                    record[col] = str(value)  # 转换为字符串避免序列化问题
            records.append(record)

        return {
            "success": True,
            "code": stock_code,
            "data": records,
            "total_count": len(df),
            "sample_count": len(records),
            "date_range": date_range
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/fetch", summary="拉取股票数据")
async def fetch_stock_data(request: FetchRequest):
    """从AkShare拉取股票数据"""
    try:
        result = stock_fetcher.fetch_and_store_stock(
            request.symbol, 
            request.start_date, 
            request.end_date
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/fetch/info/{stock_code}", summary="拉取股票基础信息")
async def fetch_stock_info(stock_code: str):
    """拉取股票基础信息"""
    try:
        result = stock_fetcher.fetch_stock_info(stock_code)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/fetch/all-stocks", summary="拉取所有股票列表")
async def fetch_all_stocks():
    """拉取所有A股股票列表"""
    try:
        result = stock_fetcher.fetch_all_stock_list()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test/{stock_code}", summary="测试股票数据")
async def test_stock_data(stock_code: str):
    """测试获取股票数据"""
    try:
        from sqlalchemy import text
        from shared.config.db import engine

        with engine.connect() as conn:
            # 获取股票基本信息
            stock_info = conn.execute(text("""
                SELECT code, name, market, industry
                FROM stock_info
                WHERE code = :code
            """), {"code": stock_code}).fetchone()

            if not stock_info:
                raise HTTPException(status_code=404, detail=f"未找到股票 {stock_code}")

            # 获取数据统计
            data_count = conn.execute(text("""
                SELECT COUNT(*) as count, MIN(日期) as min_date, MAX(日期) as max_date
                FROM stock_daily
                WHERE code = :code
            """), {"code": stock_code}).fetchone()

            # 获取最近5条数据
            recent_data = conn.execute(text("""
                SELECT 日期, 开盘, 收盘, 最高, 最低, 成交量
                FROM stock_daily
                WHERE code = :code
                ORDER BY 日期 DESC
                LIMIT 5
            """), {"code": stock_code}).fetchall()

        return {
            "success": True,
            "stock_info": {
                "code": stock_info[0],
                "name": stock_info[1],
                "market": stock_info[2],
                "industry": stock_info[3]
            },
            "data_stats": {
                "count": int(data_count[0]) if data_count[0] else 0,
                "min_date": data_count[1].isoformat() if data_count[1] else None,
                "max_date": data_count[2].isoformat() if data_count[2] else None
            },
            "recent_data": [
                {
                    "date": row[0].isoformat(),
                    "open": float(row[1]),
                    "close": float(row[2]),
                    "high": float(row[3]),
                    "low": float(row[4]),
                    "volume": int(row[5])
                }
                for row in recent_data
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", summary="获取数据统计信息")
async def get_data_stats():
    """获取数据库统计信息"""
    try:
        from sqlalchemy import text
        from shared.config.db import engine

        with engine.connect() as conn:
            # 统计股票数量
            stock_count = conn.execute(text("SELECT COUNT(*) FROM stock_info")).scalar()

            # 统计日线数据量
            daily_count = conn.execute(text("SELECT COUNT(*) FROM stock_daily")).scalar()

            # 统计特征向量数量
            feature_count = conn.execute(text("SELECT COUNT(*) FROM kline_features")).scalar()

            # 获取最新数据日期
            latest_date = conn.execute(text("SELECT MAX(日期) FROM stock_daily")).scalar()

        return {
            "success": True,
            "stats": {
                "stock_count": int(stock_count) if stock_count else 0,
                "daily_data_count": int(daily_count) if daily_count else 0,
                "feature_count": int(feature_count) if feature_count else 0,
                "latest_date": latest_date.isoformat() if latest_date else None
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
