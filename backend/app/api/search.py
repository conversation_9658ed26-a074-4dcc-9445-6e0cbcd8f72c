"""
相似性搜索API路由
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List
from datetime import date
from pydantic import BaseModel
import numpy as np
import json
import uuid

from backend.app.services.data_loader import data_loader
from backend.app.services.feature_engine import feature_engine
from backend.app.services.faiss_engine import faiss_engine

router = APIRouter(prefix="/search", tags=["相似性搜索"])

class SearchRequest(BaseModel):
    """搜索请求模型"""
    query_stock_code: str
    query_start_date: str
    query_end_date: str
    window_size: int = 5
    top_k: int = 10
    index_name: str = "default"

class SearchResult(BaseModel):
    """搜索结果模型"""
    stock_code: str
    start_date: str
    end_date: str
    similarity_score: float
    distance: float
    data_segment: List[dict]

class SearchResponse(BaseModel):
    """搜索响应模型"""
    query_id: str
    query_info: dict
    results: List[SearchResult]
    total_results: int

@router.post("/similar", summary="相似走势搜索")
async def search_similar_patterns(request: SearchRequest):
    """搜索相似的K线走势模式"""
    try:
        # 生成查询ID
        query_id = str(uuid.uuid4())
        
        # 加载查询股票数据
        try:
            start_date_obj = date.fromisoformat(request.query_start_date.split('T')[0])
            end_date_obj = date.fromisoformat(request.query_end_date.split('T')[0])
        except ValueError:
            start_date_obj = date.fromisoformat(request.query_start_date)
            end_date_obj = date.fromisoformat(request.query_end_date)
        
        query_df = data_loader.load_stock_data(
            request.query_stock_code, 
            start_date_obj, 
            end_date_obj
        )
        
        if query_df.empty:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到股票 {request.query_stock_code} 在指定日期范围的数据"
            )
        
        # 提取查询特征
        query_vectors, query_indices, query_dates = feature_engine.extract_kline_features(
            query_df, request.window_size
        )
        
        if len(query_vectors) == 0:
            raise HTTPException(status_code=400, detail="查询数据不足，无法提取特征")
        
        # 使用第一个特征向量作为查询向量
        query_vector = query_vectors[0]
        
        # 执行相似性搜索
        distances, indices = faiss_engine.query_top_k(
            query_vector, request.top_k, request.index_name
        )
        
        # 加载索引元数据
        metadata_path = f"data/faiss_indexes/{request.index_name}_metadata.json"
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail=f"索引 {request.index_name} 的元数据不存在")
        
        # 构建搜索结果
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx >= len(metadata):
                continue
                
            meta = metadata[idx]
            
            # 加载匹配的股票数据段
            match_start_date = date.fromisoformat(meta["start_date"])
            match_end_date = date.fromisoformat(meta["start_date"])  # 需要计算结束日期
            
            # 计算实际结束日期
            from datetime import timedelta
            try:
                if isinstance(match_start_date, str):
                    match_start_date = date.fromisoformat(match_start_date.split('T')[0])
                match_end_date = match_start_date + timedelta(days=request.window_size - 1)
            except Exception as e:
                print(f"日期处理错误: {e}")
                match_end_date = match_start_date
            
            match_df = data_loader.load_stock_data(
                meta["stock_code"], 
                match_start_date, 
                match_end_date
            )
            
            if not match_df.empty:
                # 计算相似度分数 (距离越小，相似度越高)
                similarity_score = 1.0 / (1.0 + distance)
                
                result = SearchResult(
                    stock_code=meta["stock_code"],
                    start_date=meta["start_date"],
                    end_date=match_end_date.isoformat(),
                    similarity_score=similarity_score,
                    distance=float(distance),
                    data_segment=match_df.to_dict('records')
                )
                results.append(result)
        
        # 构建查询信息
        query_info = {
            "stock_code": request.query_stock_code,
            "start_date": request.query_start_date,
            "end_date": request.query_end_date,
            "window_size": request.window_size,
            "top_k": request.top_k,
            "index_name": request.index_name,
            "query_data": query_df.to_dict('records')
        }
        
        # 保存查询历史
        await _save_query_history(query_id, request, query_vector, results)
        
        return SearchResponse(
            query_id=query_id,
            query_info=query_info,
            results=results,
            total_results=len(results)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", summary="获取搜索历史")
async def get_search_history(limit: int = Query(10, description="返回数量限制")):
    """获取搜索历史记录"""
    try:
        from sqlalchemy import text
        from shared.config.db import engine
        
        sql = """
            SELECT query_id, query_code, query_start_date, query_end_date, 
                   window_size, top_k, query_time
            FROM query_history
            ORDER BY query_time DESC
            LIMIT :limit
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(sql), {"limit": limit})
            history = [dict(row._mapping) for row in result]
        
        return {"success": True, "history": history}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history/{query_id}", summary="获取搜索详情")
async def get_search_detail(query_id: str):
    """获取指定查询的详细结果"""
    try:
        from sqlalchemy import text
        from shared.config.db import engine
        
        sql = """
            SELECT query_id, query_code, query_start_date, query_end_date,
                   window_size, top_k, query_vector, results, query_time
            FROM query_history
            WHERE query_id = :query_id
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(sql), {"query_id": query_id}).fetchone()
        
        if not result:
            raise HTTPException(status_code=404, detail="查询记录不存在")
        
        # 解析JSON数据
        query_data = dict(result._mapping)
        query_data["query_vector"] = json.loads(query_data["query_vector"])
        query_data["results"] = json.loads(query_data["results"])
        
        return {"success": True, "data": query_data}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def _save_query_history(query_id: str, request: SearchRequest, 
                            query_vector: np.ndarray, results: List[SearchResult]):
    """保存查询历史"""
    try:
        from sqlalchemy import text
        from shared.config.db import engine
        
        # 转换结果为可序列化格式
        results_data = []
        for result in results:
            results_data.append({
                "stock_code": result.stock_code,
                "start_date": result.start_date,
                "end_date": result.end_date,
                "similarity_score": result.similarity_score,
                "distance": result.distance
            })
        
        sql = """
            INSERT INTO query_history (
                query_id, query_code, query_start_date, query_end_date,
                window_size, top_k, query_vector, results
            ) VALUES (
                :query_id, :query_code, :query_start_date, :query_end_date,
                :window_size, :top_k, :query_vector, :results
            )
        """
        
        params = {
            "query_id": query_id,
            "query_code": request.query_stock_code,
            "query_start_date": request.query_start_date,
            "query_end_date": request.query_end_date,
            "window_size": request.window_size,
            "top_k": request.top_k,
            "query_vector": json.dumps(query_vector.tolist()),
            "results": json.dumps(results_data)
        }
        
        with engine.connect() as conn:
            conn.execute(text(sql), params)
            conn.commit()
            
    except Exception as e:
        print(f"保存查询历史失败: {e}")
