"""
股票相关数据模型
"""
from sqlalchemy import Column, Integer, String, Date, DECIMAL, BigInteger, Text, Enum, TIMESTAMP, JSON
from sqlalchemy.sql import func
from shared.config.db import Base

class StockInfo(Base):
    """股票基础信息模型"""
    __tablename__ = "stock_info"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), nullable=False, unique=True, comment="股票代码")
    name = Column(String(100), nullable=False, comment="股票名称")
    market = Column(String(10), nullable=False, comment="市场(SH/SZ)")
    industry = Column(String(100), comment="行业分类")
    list_date = Column(Date, comment="上市日期")
    status = Column(Enum('active', 'delisted', 'suspended'), default='active', comment="状态")
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp())

class StockDaily(Base):
    """股票日线数据模型"""
    __tablename__ = "stock_daily"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    code = Column(String(20), nullable=False, comment="股票代码")
    日期 = Column(Date, nullable=False, comment="交易日期")
    开盘 = Column(DECIMAL(10, 3), nullable=False, comment="开盘价")
    收盘 = Column(DECIMAL(10, 3), nullable=False, comment="收盘价")
    最高 = Column(DECIMAL(10, 3), nullable=False, comment="最高价")
    最低 = Column(DECIMAL(10, 3), nullable=False, comment="最低价")
    成交量 = Column(BigInteger, nullable=False, comment="成交量")
    成交额 = Column(DECIMAL(15, 2), comment="成交额")
    振幅 = Column(DECIMAL(8, 4), comment="振幅")
    涨跌幅 = Column(DECIMAL(8, 4), comment="涨跌幅")
    涨跌额 = Column(DECIMAL(10, 3), comment="涨跌额")
    换手率 = Column(DECIMAL(8, 4), comment="换手率")
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp())

class KlineFeatures(Base):
    """K线特征向量模型"""
    __tablename__ = "kline_features"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    code = Column(String(20), nullable=False, comment="股票代码")
    start_date = Column(Date, nullable=False, comment="片段起始日期")
    end_date = Column(Date, nullable=False, comment="片段结束日期")
    window_size = Column(Integer, nullable=False, comment="窗口大小")
    feature_vector = Column(JSON, nullable=False, comment="特征向量(JSON格式)")
    feature_hash = Column(String(64), nullable=False, comment="特征向量哈希值")
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp())

class FaissIndexMeta(Base):
    """FAISS索引元数据模型"""
    __tablename__ = "faiss_index_meta"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    index_name = Column(String(100), nullable=False, unique=True, comment="索引名称")
    window_size = Column(Integer, nullable=False, comment="窗口大小")
    feature_dim = Column(Integer, nullable=False, comment="特征维度")
    total_vectors = Column(Integer, nullable=False, comment="向量总数")
    index_type = Column(String(50), nullable=False, comment="索引类型")
    index_path = Column(String(500), nullable=False, comment="索引文件路径")
    build_time = Column(TIMESTAMP, nullable=False, comment="构建时间")
    status = Column(Enum('building', 'ready', 'error'), default='building', comment="状态")
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp())

class QueryHistory(Base):
    """查询历史模型"""
    __tablename__ = "query_history"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    query_id = Column(String(36), nullable=False, unique=True, comment="查询ID")
    query_code = Column(String(20), nullable=False, comment="查询股票代码")
    query_start_date = Column(Date, nullable=False, comment="查询起始日期")
    query_end_date = Column(Date, nullable=False, comment="查询结束日期")
    window_size = Column(Integer, nullable=False, comment="窗口大小")
    top_k = Column(Integer, nullable=False, comment="返回结果数量")
    query_vector = Column(JSON, nullable=False, comment="查询向量")
    results = Column(JSON, nullable=False, comment="查询结果")
    query_time = Column(TIMESTAMP, default=func.current_timestamp(), comment="查询时间")

class SystemConfig(Base):
    """系统配置模型"""
    __tablename__ = "system_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), nullable=False, unique=True, comment="配置键")
    config_value = Column(Text, nullable=False, comment="配置值")
    config_type = Column(Enum('string', 'int', 'float', 'bool', 'json'), default='string', comment="配置类型")
    description = Column(Text, comment="配置描述")
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    updated_at = Column(TIMESTAMP, default=func.current_timestamp(), onupdate=func.current_timestamp())
