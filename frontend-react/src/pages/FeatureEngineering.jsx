import React, { useState, useEffect, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  Select,
  Slider,
  Switch,
  DatePicker,
  Table,
  message,
  Typography,
  Space,
  Tag,
  Statistic,
  List,
  Popconfirm,
  Alert,
} from 'antd';
import {
  SettingOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { featureAPI, stockAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FeatureEngineering = () => {
  const [form] = Form.useForm();
  const [indexForm] = Form.useForm();
  const [stockList, setStockList] = useState([]);
  const [features, setFeatures] = useState(null);
  const [indexes, setIndexes] = useState([]);
  const [extractLoading, setExtractLoading] = useState(false);
  const [indexLoading, setIndexLoading] = useState(false);
  const [indexesLoading, setIndexesLoading] = useState(false);

  // 加载股票列表
  const loadStockList = useCallback(async () => {
    try {
      const response = await stockAPI.getStockList();
      if (response.data.success) {
        setStockList(response.data.data);
      }
    } catch (error) {
      console.error('加载股票列表失败:', error);
    }
  }, []);

  // 加载索引列表
  const loadIndexes = useCallback(async () => {
    setIndexesLoading(true);
    try {
      const response = await featureAPI.getIndexes();
      if (response.data.success) {
        setIndexes(response.data.indexes);
      } else {
        message.error('加载索引列表失败');
      }
    } catch (error) {
      console.error('加载索引列表失败:', error);
      message.error('网络请求失败');
    } finally {
      setIndexesLoading(false);
    }
  }, []);

  // 提取特征
  const handleExtractFeatures = async (values) => {
    setExtractLoading(true);
    try {
      const data = {
        stock_code: values.stockCode,
        window_size: values.windowSize,
        start_date: values.dateRange[0].format('YYYY-MM-DD'),
        end_date: values.dateRange[1].format('YYYY-MM-DD'),
        normalize: values.normalize,
      };

      const response = await featureAPI.extractFeatures(data);
      if (response.data.success) {
        setFeatures(response.data);
        message.success(`✅ 提取了 ${response.data.feature_count} 个特征向量`);
      } else {
        message.error('特征提取失败');
      }
    } catch (error) {
      console.error('特征提取失败:', error);
      message.error('特征提取失败');
    } finally {
      setExtractLoading(false);
    }
  };

  // 构建索引
  const handleBuildIndex = async (values) => {
    setIndexLoading(true);
    try {
      const stockCodes = values.stockCodes
        .split('\n')
        .map(code => code.trim())
        .filter(code => code);

      const response = await featureAPI.buildIndex(
        stockCodes,
        values.windowSize,
        values.indexName
      );

      if (response.data.success) {
        message.success(`✅ 索引构建成功！包含 ${response.data.total_vectors} 个向量`);
        loadIndexes(); // 刷新索引列表
        indexForm.resetFields();
      } else {
        message.error('索引构建失败');
      }
    } catch (error) {
      console.error('索引构建失败:', error);
      message.error('索引构建失败');
    } finally {
      setIndexLoading(false);
    }
  };

  // 删除索引
  const handleDeleteIndex = async (indexName) => {
    try {
      const response = await featureAPI.deleteIndex(indexName);
      if (response.data.success) {
        message.success('✅ 索引删除成功');
        loadIndexes(); // 刷新索引列表
      } else {
        message.error('索引删除失败');
      }
    } catch (error) {
      console.error('索引删除失败:', error);
      message.error('索引删除失败');
    }
  };

  // 初始化加载
  useEffect(() => {
    loadStockList();
    loadIndexes();
  }, [loadStockList, loadIndexes]);

  // 特征表格列定义
  const featureColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '特征哈希',
      dataIndex: 'feature_hash',
      key: 'feature_hash',
      render: (hash) => (
        <Text code style={{ fontSize: '12px' }}>
          {hash?.slice(0, 16)}...
        </Text>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 特征提取 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <SettingOutlined />
                K线特征提取
              </Space>
            }
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleExtractFeatures}
              initialValues={{
                windowSize: 5,
                normalize: true,
                dateRange: [dayjs().subtract(6, 'month'), dayjs()],
              }}
            >
              <Form.Item
                name="stockCode"
                label="选择股票"
                rules={[{ required: true, message: '请选择股票' }]}
              >
                <Select
                  placeholder="选择股票"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {stockList.map(stock => (
                    <Option key={stock.code} value={stock.code}>
                      {stock.code} - {stock.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="windowSize"
                label="窗口大小"
                tooltip="K线片段的长度（天数）"
              >
                <Slider
                  min={3}
                  max={20}
                  marks={{
                    3: '3天',
                    5: '5天',
                    10: '10天',
                    20: '20天',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="dateRange"
                label="日期范围"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="normalize"
                label="特征标准化"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={extractLoading}
                  icon={<PlayCircleOutlined />}
                  block
                >
                  提取特征
                </Button>
              </Form.Item>
            </Form>

            {/* 特征提取结果 */}
            {features && (
              <div style={{ marginTop: '16px' }}>
                <Alert
                  message="特征提取成功"
                  description={
                    <div>
                      <p><strong>股票代码:</strong> {features.stock_code}</p>
                      <p><strong>窗口大小:</strong> {features.window_size} 天</p>
                      <p><strong>特征数量:</strong> {features.feature_count} 个</p>
                      <p><strong>特征维度:</strong> {features.feature_dimension} 维</p>
                    </div>
                  }
                  type="success"
                  showIcon
                />

                {features.features && features.features.length > 0 && (
                  <div style={{ marginTop: '16px' }}>
                    <Title level={5}>特征数据预览</Title>
                    <Table
                      dataSource={features.features.slice(0, 10)}
                      columns={featureColumns}
                      pagination={false}
                      size="small"
                      rowKey="index"
                    />
                  </div>
                )}
              </div>
            )}
          </Card>
        </Col>

        {/* 索引管理 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                FAISS索引管理
              </Space>
            }
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadIndexes}
                loading={indexesLoading}
                size="small"
              >
                刷新
              </Button>
            }
          >
            {/* 构建新索引 */}
            <Title level={5}>🔨 构建新索引</Title>
            <Form
              form={indexForm}
              layout="vertical"
              onFinish={handleBuildIndex}
              initialValues={{
                indexName: 'my_index',
                windowSize: 5,
                stockCodes: stockList.slice(0, 3).map(s => s.code).join('\n'),
              }}
            >
              <Form.Item
                name="indexName"
                label="索引名称"
                rules={[{ required: true, message: '请输入索引名称' }]}
              >
                <Input placeholder="输入索引名称" />
              </Form.Item>

              <Form.Item
                name="windowSize"
                label="窗口大小"
              >
                <Slider
                  min={3}
                  max={20}
                  marks={{
                    3: '3',
                    5: '5',
                    10: '10',
                    20: '20',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="stockCodes"
                label="股票代码列表"
                rules={[{ required: true, message: '请输入股票代码' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="每行一个股票代码，例如：&#10;sh600519&#10;sz000001&#10;sh600036"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={indexLoading}
                  icon={<PlayCircleOutlined />}
                  block
                >
                  构建索引
                </Button>
              </Form.Item>
            </Form>

            {/* 现有索引列表 */}
            <div style={{ marginTop: '24px' }}>
              <Title level={5}>📋 现有索引</Title>
              <List
                loading={indexesLoading}
                dataSource={indexes}
                renderItem={(index) => (
                  <List.Item
                    actions={[
                      <Popconfirm
                        title="确定要删除这个索引吗？"
                        onConfirm={() => handleDeleteIndex(index.index_name)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          size="small"
                        >
                          删除
                        </Button>
                      </Popconfirm>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text strong>{index.index_name}</Text>
                          <Tag color="blue">{index.window_size}天窗口</Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <Row gutter={16}>
                            <Col span={8}>
                              <Statistic
                                title="向量数量"
                                value={index.total_vectors}
                                valueStyle={{ fontSize: '14px' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Statistic
                                title="特征维度"
                                value={index.feature_dim}
                                valueStyle={{ fontSize: '14px' }}
                              />
                            </Col>
                            <Col span={8}>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {dayjs(index.build_time).format('MM-DD HH:mm')}
                              </Text>
                            </Col>
                          </Row>
                        </div>
                      }
                    />
                  </List.Item>
                )}
                locale={{ emptyText: '暂无索引，请先构建索引' }}
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default FeatureEngineering;
