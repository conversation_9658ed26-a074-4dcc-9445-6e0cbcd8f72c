import React, { useState, useEffect, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  List,
  Button,
  Select,
  Spin,
  message,
  Typography,
  Space,
  Tag,
  Statistic,
} from 'antd';
import {
  ReloadOutlined,
  StockOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import StableChart from '../components/StableChart';
import { stockAPI } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

const DataManagement = () => {
  const [stockList, setStockList] = useState([]);
  const [selectedStock, setSelectedStock] = useState(null);
  const [stockData, setStockData] = useState(null);
  const [marketFilter, setMarketFilter] = useState('all');
  const [loading, setLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [stats, setStats] = useState(null);

  // 加载股票列表
  const loadStockList = useCallback(async () => {
    setLoading(true);
    try {
      const response = await stockAPI.getStockList(marketFilter === 'all' ? null : marketFilter);
      if (response.data.success) {
        setStockList(response.data.data);
        message.success(`加载了 ${response.data.data.length} 只股票`);
      } else {
        message.error('加载股票列表失败');
      }
    } catch (error) {
      console.error('加载股票列表失败:', error);
      message.error('网络请求失败');
    } finally {
      setLoading(false);
    }
  }, [marketFilter]);

  // 加载系统统计
  const loadStats = async () => {
    try {
      const response = await stockAPI.getDataStats();
      if (response.data.success) {
        setStats(response.data.stats);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 选择股票
  const handleStockSelect = async (stock) => {
    setSelectedStock(stock);
    setChartLoading(true);
    setStockData(null);

    try {
      const response = await stockAPI.getStockDetail(stock.code);
      if (response.data.success) {
        setStockData(response.data);
        message.success(`加载 ${stock.name} 数据成功`);
      } else {
        message.error(`加载 ${stock.name} 数据失败`);
      }
    } catch (error) {
      console.error('加载股票数据失败:', error);
      message.error('加载股票数据失败');
    } finally {
      setChartLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadStockList();
    loadStats();
  }, []);

  // 市场筛选变化
  useEffect(() => {
    loadStockList();
  }, [marketFilter]);

  // 筛选股票列表
  const filteredStocks = stockList.filter(stock => {
    if (marketFilter === 'all') return true;
    return stock.market === marketFilter;
  });

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 左侧股票列表 */}
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                股票列表
              </Space>
            }
            extra={
              <Space>
                <Select
                  value={marketFilter}
                  onChange={setMarketFilter}
                  style={{ width: 100 }}
                >
                  <Option value="all">全部</Option>
                  <Option value="SH">上海</Option>
                  <Option value="SZ">深圳</Option>
                </Select>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadStockList}
                  loading={loading}
                  size="small"
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Spin spinning={loading}>
              <List
                dataSource={filteredStocks}
                renderItem={(stock) => (
                  <List.Item
                    onClick={() => handleStockSelect(stock)}
                    style={{
                      cursor: 'pointer',
                      backgroundColor: selectedStock?.code === stock.code ? '#f0f8ff' : 'transparent',
                      borderRadius: '4px',
                      padding: '8px',
                      margin: '4px 0',
                      border: selectedStock?.code === stock.code ? '1px solid #1890ff' : '1px solid transparent',
                    }}
                    className="stock-list-item"
                  >
                    <List.Item.Meta
                      avatar={<StockOutlined style={{ color: '#1890ff' }} />}
                      title={
                        <Space>
                          <Text strong>{stock.code}</Text>
                          <Tag color={stock.market === 'SH' ? 'red' : 'green'}>
                            {stock.market}
                          </Tag>
                        </Space>
                      }
                      description={stock.name}
                    />
                  </List.Item>
                )}
                locale={{ emptyText: '暂无股票数据' }}
              />
            </Spin>
          </Card>
        </Col>

        {/* 右侧详情和图表 */}
        <Col xs={24} lg={16}>
          {selectedStock ? (
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 股票信息卡片 */}
              {stockData && (
                <Card>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Statistic title="股票代码" value={stockData.stock_info.code} />
                    </Col>
                    <Col span={6}>
                      <Statistic title="股票名称" value={stockData.stock_info.name} />
                    </Col>
                    <Col span={6}>
                      <Statistic title="所属市场" value={stockData.stock_info.market} />
                    </Col>
                    <Col span={6}>
                      <Statistic title="数据量" value={stockData.data_stats.count} suffix="条" />
                    </Col>
                  </Row>
                </Card>
              )}

              {/* K线图表 */}
              <StableChart
                data={stockData?.recent_data || []}
                title={selectedStock ? `${selectedStock.name} (${selectedStock.code}) K线图` : 'K线图'}
                height={600}
                loading={chartLoading}
              />
            </Space>
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '60px 0' }}>
                <StockOutlined style={{ fontSize: '64px', color: '#ccc', marginBottom: '16px' }} />
                <Title level={4} type="secondary">请选择股票查看详情</Title>
                <Text type="secondary">点击左侧股票列表中的任意股票查看K线图表</Text>
                
                {/* 系统统计 */}
                {stats && (
                  <div style={{ marginTop: '40px' }}>
                    <Title level={5}>系统概览</Title>
                    <Row gutter={16} justify="center">
                      <Col span={6}>
                        <Statistic title="股票数量" value={stats.stock_count} />
                      </Col>
                      <Col span={6}>
                        <Statistic title="数据量" value={stats.daily_data_count} formatter={(value) => `${(value / 10000).toFixed(1)}万`} />
                      </Col>
                      <Col span={6}>
                        <Statistic title="特征向量" value={stats.feature_count} />
                      </Col>
                      <Col span={6}>
                        <Statistic title="最新数据" value={stats.latest_date?.slice(0, 10) || 'N/A'} />
                      </Col>
                    </Row>
                  </div>
                )}
              </div>
            </Card>
          )}
        </Col>
      </Row>

      <style jsx>{`
        .stock-list-item:hover {
          background-color: #f5f5f5 !important;
        }
      `}</style>
    </div>
  );
};

export default DataManagement;
