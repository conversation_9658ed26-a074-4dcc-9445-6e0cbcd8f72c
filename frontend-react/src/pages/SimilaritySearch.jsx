import React, { useState, useEffect, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  Select,
  Slider,
  DatePicker,
  Table,
  message,
  Typography,
  Space,
  Tag,
  Statistic,
  List,
  Alert,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  HistoryOutlined,
  ReloadOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { searchAPI, stockAPI, featureAPI } from '../services/api';
import LightweightChart from '../components/LightweightChart';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const SimilaritySearch = () => {
  const [form] = Form.useForm();
  const [stockList, setStockList] = useState([]);
  const [indexes, setIndexes] = useState([]);
  const [searchResults, setSearchResults] = useState(null);
  const [searchHistory, setSearchHistory] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  // 加载股票列表
  const loadStockList = useCallback(async () => {
    try {
      const response = await stockAPI.getStockList();
      if (response.data.success) {
        setStockList(response.data.data);
      }
    } catch (error) {
      console.error('加载股票列表失败:', error);
    }
  }, []);

  // 加载索引列表
  const loadIndexes = useCallback(async () => {
    try {
      const response = await featureAPI.getIndexes();
      if (response.data.success) {
        setIndexes(response.data.indexes);
      }
    } catch (error) {
      console.error('加载索引列表失败:', error);
    }
  }, []);

  // 加载搜索历史
  const loadSearchHistory = useCallback(async () => {
    setHistoryLoading(true);
    try {
      const response = await searchAPI.getSearchHistory(10);
      if (response.data.success) {
        setSearchHistory(response.data.history);
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error);
    } finally {
      setHistoryLoading(false);
    }
  }, []);

  // 执行相似性搜索
  const handleSearch = async (values) => {
    setSearchLoading(true);
    try {
      const data = {
        query_stock_code: values.stockCode,
        query_start_date: values.dateRange[0].format('YYYY-MM-DD'),
        query_end_date: values.dateRange[1].format('YYYY-MM-DD'),
        window_size: values.windowSize,
        top_k: values.topK,
        index_name: values.indexName,
      };

      const response = await searchAPI.searchSimilar(data);
      if (response.data.success) {
        setSearchResults(response.data);
        message.success(`✅ 搜索完成！找到 ${response.data.total_results} 个相似结果`);
        loadSearchHistory(); // 刷新搜索历史
      } else {
        message.error('搜索失败');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败');
    } finally {
      setSearchLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadStockList();
    loadIndexes();
    loadSearchHistory();
  }, [loadStockList, loadIndexes, loadSearchHistory]);

  // 搜索结果表格列定义
  const resultColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: '股票代码',
      dataIndex: 'stock_code',
      key: 'stock_code',
      render: (code) => <Text strong>{code}</Text>,
    },
    {
      title: '匹配日期',
      key: 'date_range',
      render: (record) => (
        <Text>
          {dayjs(record.start_date).format('MM-DD')} ~ {dayjs(record.end_date).format('MM-DD')}
        </Text>
      ),
    },
    {
      title: '相似度',
      dataIndex: 'similarity_score',
      key: 'similarity_score',
      render: (score) => (
        <Tag color={score > 0.8 ? 'green' : score > 0.6 ? 'orange' : 'blue'}>
          {(score * 100).toFixed(2)}%
        </Tag>
      ),
    },
    {
      title: '距离',
      dataIndex: 'distance',
      key: 'distance',
      render: (distance) => <Text type="secondary">{distance.toFixed(4)}</Text>,
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 搜索表单 */}
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <SearchOutlined />
                相似性搜索
              </Space>
            }
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSearch}
              initialValues={{
                windowSize: 5,
                topK: 10,
                dateRange: [dayjs().subtract(1, 'month'), dayjs().subtract(15, 'day')],
              }}
            >
              <Form.Item
                name="stockCode"
                label="查询股票"
                rules={[{ required: true, message: '请选择股票' }]}
              >
                <Select
                  placeholder="选择股票"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {stockList.map(stock => (
                    <Option key={stock.code} value={stock.code}>
                      {stock.code} - {stock.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="dateRange"
                label="查询日期范围"
                rules={[{ required: true, message: '请选择日期范围' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="windowSize"
                label="搜索窗口大小"
                tooltip="匹配的K线长度"
              >
                <Slider
                  min={3}
                  max={20}
                  marks={{
                    3: '3天',
                    5: '5天',
                    10: '10天',
                    20: '20天',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="topK"
                label="返回结果数量"
              >
                <Slider
                  min={5}
                  max={50}
                  marks={{
                    5: '5',
                    10: '10',
                    20: '20',
                    50: '50',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="indexName"
                label="使用索引"
                rules={[{ required: true, message: '请选择索引' }]}
              >
                <Select placeholder="选择FAISS索引">
                  {indexes.map(index => (
                    <Option key={index.index_name} value={index.index_name}>
                      {index.index_name} ({index.total_vectors}个向量)
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={searchLoading}
                  icon={<RocketOutlined />}
                  block
                  size="large"
                >
                  开始搜索
                </Button>
              </Form.Item>
            </Form>
          </Card>

          {/* 搜索历史 */}
          <Card
            title={
              <Space>
                <HistoryOutlined />
                搜索历史
              </Space>
            }
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadSearchHistory}
                loading={historyLoading}
                size="small"
              >
                刷新
              </Button>
            }
            style={{ marginTop: '16px' }}
          >
            <List
              loading={historyLoading}
              dataSource={searchHistory}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{item.query_stock_code}</Text>
                        <Tag color="blue">{item.window_size}天</Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {dayjs(item.query_time).format('MM-DD HH:mm')}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.total_results} 个结果
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无搜索历史' }}
            />
          </Card>
        </Col>

        {/* 搜索结果 */}
        <Col xs={24} lg={16}>
          {searchResults ? (
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 搜索信息 */}
              <Card>
                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic title="查询ID" value={searchResults.query_id?.slice(0, 8)} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="查询股票" value={searchResults.query_info?.stock_code} />
                  </Col>
                  <Col span={6}>
                    <Statistic title="结果数量" value={searchResults.total_results} />
                  </Col>
                  <Col span={6}>
                    <Statistic 
                      title="搜索时间" 
                      value={dayjs(searchResults.search_time).format('HH:mm:ss')} 
                    />
                  </Col>
                </Row>
              </Card>

              {/* 搜索结果表格 */}
              <Card title="🎯 相似走势排行榜">
                <Table
                  dataSource={searchResults.results}
                  columns={resultColumns}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: false,
                    showQuickJumper: true,
                  }}
                  rowKey={(record, index) => `${record.stock_code}_${index}`}
                />
              </Card>

              {/* K线图对比 */}
              {searchResults.query_data && (
                <Card title="📊 查询走势图">
                  <LightweightChart
                    data={searchResults.query_data}
                    title={`查询走势: ${searchResults.query_info?.stock_code}`}
                    height={400}
                  />
                </Card>
              )}
            </Space>
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '60px 0' }}>
                <SearchOutlined style={{ fontSize: '64px', color: '#ccc', marginBottom: '16px' }} />
                <Title level={4} type="secondary">开始相似性搜索</Title>
                <Text type="secondary">
                  👈 在左侧设置搜索参数，找到与指定股票走势相似的K线模式
                </Text>
                
                <Divider />
                
                <div style={{ textAlign: 'left', maxWidth: '500px', margin: '0 auto' }}>
                  <Title level={5}>🔍 搜索说明</Title>
                  <ul style={{ color: '#666' }}>
                    <li><strong>查询股票</strong>: 选择要分析的股票</li>
                    <li><strong>日期范围</strong>: 指定K线片段的时间范围</li>
                    <li><strong>窗口大小</strong>: 匹配的K线长度（建议5-10天）</li>
                    <li><strong>结果数量</strong>: 返回最相似的N个结果</li>
                    <li><strong>使用索引</strong>: 选择预构建的FAISS索引</li>
                  </ul>
                </div>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default SimilaritySearch;
