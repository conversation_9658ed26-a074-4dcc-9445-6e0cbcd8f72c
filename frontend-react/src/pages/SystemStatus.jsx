import React, { useState, useEffect, useCallback } from 'react';
import {
  Row,
  Col,
  Card,
  Button,
  message,
  Typography,
  Space,
  Statistic,
  Alert,
  Progress,
  Tag,
  Descriptions,
  Table,
} from 'antd';
import {
  InfoCircleOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { systemAPI, stockAPI, featureAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const SystemStatus = () => {
  const [healthStatus, setHealthStatus] = useState(null);
  const [systemStats, setSystemStats] = useState(null);
  const [indexes, setIndexes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [healthLoading, setHealthLoading] = useState(false);

  // 健康检查
  const checkHealth = useCallback(async () => {
    setHealthLoading(true);
    try {
      const response = await systemAPI.healthCheck();
      if (response.status === 200) {
        setHealthStatus(response.data);
        message.success('✅ 系统健康检查通过');
      } else {
        setHealthStatus({ status: 'error', message: '健康检查失败' });
        message.error('❌ 系统健康检查失败');
      }
    } catch (error) {
      console.error('健康检查失败:', error);
      setHealthStatus({ status: 'error', message: error.message });
      message.error('❌ 无法连接到后端服务');
    } finally {
      setHealthLoading(false);
    }
  }, []);

  // 加载系统统计
  const loadSystemStats = useCallback(async () => {
    try {
      const response = await stockAPI.getDataStats();
      if (response.data.success) {
        setSystemStats(response.data.stats);
      }
    } catch (error) {
      console.error('加载系统统计失败:', error);
    }
  }, []);

  // 加载索引信息
  const loadIndexes = useCallback(async () => {
    try {
      const response = await featureAPI.getIndexes();
      if (response.data.success) {
        setIndexes(response.data.indexes);
      }
    } catch (error) {
      console.error('加载索引信息失败:', error);
    }
  }, []);

  // 刷新所有数据
  const refreshAll = async () => {
    setLoading(true);
    try {
      await Promise.all([
        checkHealth(),
        loadSystemStats(),
        loadIndexes(),
      ]);
      message.success('✅ 数据刷新完成');
    } catch (error) {
      message.error('❌ 数据刷新失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    refreshAll();
  }, []);

  // 索引表格列定义
  const indexColumns = [
    {
      title: '索引名称',
      dataIndex: 'index_name',
      key: 'index_name',
      render: (name) => <Text strong>{name}</Text>,
    },
    {
      title: '向量数量',
      dataIndex: 'total_vectors',
      key: 'total_vectors',
      render: (count) => count.toLocaleString(),
    },
    {
      title: '特征维度',
      dataIndex: 'feature_dim',
      key: 'feature_dim',
    },
    {
      title: '窗口大小',
      dataIndex: 'window_size',
      key: 'window_size',
      render: (size) => `${size} 天`,
    },
    {
      title: '构建时间',
      dataIndex: 'build_time',
      key: 'build_time',
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '状态',
      key: 'status',
      render: () => <Tag color="green">正常</Tag>,
    },
  ];

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 系统健康状态 */}
        <Col span={24}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                系统健康状态
              </Space>
            }
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={refreshAll}
                loading={loading}
              >
                刷新状态
              </Button>
            }
          >
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8}>
                <Card size="small">
                  <Statistic
                    title="API服务状态"
                    value={healthStatus?.status === 'healthy' ? '正常' : '异常'}
                    prefix={
                      healthStatus?.status === 'healthy' ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      )
                    }
                    valueStyle={{
                      color: healthStatus?.status === 'healthy' ? '#52c41a' : '#ff4d4f',
                    }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card size="small">
                  <Statistic
                    title="数据库连接"
                    value={healthStatus?.database === 'connected' ? '已连接' : '断开'}
                    prefix={
                      healthStatus?.database === 'connected' ? (
                        <DatabaseOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      )
                    }
                    valueStyle={{
                      color: healthStatus?.database === 'connected' ? '#52c41a' : '#ff4d4f',
                    }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card size="small">
                  <Button
                    type="primary"
                    icon={<CloudServerOutlined />}
                    onClick={checkHealth}
                    loading={healthLoading}
                    block
                  >
                    健康检查
                  </Button>
                </Card>
              </Col>
            </Row>

            {healthStatus && healthStatus.status !== 'healthy' && (
              <Alert
                message="系统异常"
                description={healthStatus.message || '系统运行异常，请检查服务状态'}
                type="error"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>
        </Col>

        {/* 数据统计 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                数据统计
              </Space>
            }
          >
            {systemStats ? (
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="股票数量"
                      value={systemStats.stock_count}
                      suffix="只"
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="日线数据"
                      value={systemStats.daily_data_count}
                      formatter={(value) => `${(value / 10000).toFixed(1)}万`}
                      suffix="条"
                    />
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="特征向量"
                      value={systemStats.feature_count}
                      suffix="个"
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="最新数据"
                      value={systemStats.latest_date?.slice(0, 10) || 'N/A'}
                    />
                  </Col>
                </Row>

                {/* 数据完整性进度 */}
                <div>
                  <Text strong>数据完整性</Text>
                  <Progress
                    percent={Math.min(100, (systemStats.daily_data_count / 30000) * 100)}
                    status="active"
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    基于预期数据量评估
                  </Text>
                </div>
              </Space>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">加载统计数据中...</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* 系统信息 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <RocketOutlined />
                系统信息
              </Space>
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="系统版本">v1.0.0</Descriptions.Item>
              <Descriptions.Item label="前端技术">React + Ant Design</Descriptions.Item>
              <Descriptions.Item label="后端技术">FastAPI + Python</Descriptions.Item>
              <Descriptions.Item label="数据库">MySQL 8.0</Descriptions.Item>
              <Descriptions.Item label="图表引擎">TradingView Lightweight Charts</Descriptions.Item>
              <Descriptions.Item label="向量检索">FAISS</Descriptions.Item>
              <Descriptions.Item label="数据源">AkShare</Descriptions.Item>
              <Descriptions.Item label="部署时间">
                {dayjs().format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            <Alert
              message="系统运行正常"
              description="所有核心功能模块运行稳定，数据更新及时"
              type="success"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </Card>
        </Col>

        {/* 索引状态 */}
        <Col span={24}>
          <Card
            title={
              <Space>
                <DatabaseOutlined />
                FAISS索引状态
              </Space>
            }
          >
            {indexes.length > 0 ? (
              <Table
                dataSource={indexes}
                columns={indexColumns}
                pagination={false}
                rowKey="index_name"
                size="small"
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无索引数据</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemStatus;
