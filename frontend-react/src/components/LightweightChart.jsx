import React, { useEffect, useRef, useState } from 'react';
import { createChart } from 'lightweight-charts';
import { Card, Spin, Alert } from 'antd';

const LightweightChart = ({
  data,
  title = 'K线图',
  height = 500,
  loading = false,
  error = null
}) => {
  const chartContainerRef = useRef();
  const chart = useRef();
  const candlestickSeries = useRef();
  const volumeSeries = useRef();

  // 初始化图表
  useEffect(() => {
    if (!chartContainerRef.current) return;

    try {
      // 创建图表
      chart.current = createChart(chartContainerRef.current, {
        layout: {
          backgroundColor: '#ffffff',
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          timeVisible: true,
          secondsVisible: false,
        },
        width: chartContainerRef.current.clientWidth,
        height: height - 100,
      });

      // 创建K线系列 - 使用新版本API
      candlestickSeries.current = chart.current.addSeries('Candlestick', {
        upColor: '#ef4444',
        downColor: '#22c55e',
        borderDownColor: '#22c55e',
        borderUpColor: '#ef4444',
        wickDownColor: '#22c55e',
        wickUpColor: '#ef4444',
      });

      // 创建成交量系列 - 使用新版本API
      volumeSeries.current = chart.current.addSeries('Histogram', {
        color: '#26a69a',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: 'volume',
        scaleMargins: {
          top: 0.7,
          bottom: 0,
        },
      });

      // 处理窗口大小变化
      const handleResize = () => {
        if (chart.current && chartContainerRef.current) {
          chart.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chart.current) {
          chart.current.remove();
        }
      };
    } catch (error) {
      console.error('图表初始化失败:', error);
    }
  }, [height]);

  // 更新数据
  useEffect(() => {
    if (!chart.current || !candlestickSeries.current || !volumeSeries.current || !data || !Array.isArray(data) || data.length === 0) {
      return;
    }

    try {
      // 转换数据格式
      const candlestickData = [];
      const volumeData = [];

      data.forEach(item => {
        // 处理日期格式
        let timeValue;
        const dateStr = item.date || item.日期;
        if (dateStr) {
          const date = new Date(dateStr);
          timeValue = Math.floor(date.getTime() / 1000);
        } else {
          return; // 跳过没有日期的数据
        }

        // K线数据
        const open = parseFloat(item.open || item.开盘);
        const high = parseFloat(item.high || item.最高);
        const low = parseFloat(item.low || item.最低);
        const close = parseFloat(item.close || item.收盘);
        const volume = parseFloat(item.volume || item.成交量);

        if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {
          candlestickData.push({
            time: timeValue,
            open: open,
            high: high,
            low: low,
            close: close,
          });
        }

        if (!isNaN(volume)) {
          volumeData.push({
            time: timeValue,
            value: volume,
            color: close >= open ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)',
          });
        }
      });

      // 按时间排序
      candlestickData.sort((a, b) => a.time - b.time);
      volumeData.sort((a, b) => a.time - b.time);

      // 设置数据
      if (candlestickData.length > 0) {
        candlestickSeries.current.setData(candlestickData);
      }

      if (volumeData.length > 0) {
        volumeSeries.current.setData(volumeData);
      }

      // 自适应视图
      setTimeout(() => {
        if (chart.current) {
          chart.current.timeScale().fitContent();
        }
      }, 100);

    } catch (error) {
      console.error('图表数据处理错误:', error);
    }
  }, [data]);

  if (error) {
    return (
      <Card title={title}>
        <Alert
          message="图表加载失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  return (
    <Card 
      title={title}
      style={{ width: '100%' }}
      bodyStyle={{ padding: '12px' }}
    >
      <Spin spinning={loading} tip="加载图表数据...">
        <div
          ref={chartContainerRef}
          style={{
            width: '100%',
            height: height - 100,
            position: 'relative',
          }}
        />
        {!loading && (!data || data.length === 0) && (
          <div
            style={{
              height: height - 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999',
              fontSize: '16px',
            }}
          >
            暂无数据
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default LightweightChart;
