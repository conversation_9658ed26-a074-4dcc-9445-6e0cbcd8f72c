import React, { useEffect, useRef, useState } from 'react';
import { createChart } from 'lightweight-charts';
import { Card, Spin } from 'antd';

const StableChart = ({ 
  data, 
  title = 'K线图', 
  height = 500,
  loading = false 
}) => {
  const chartContainerRef = useRef();
  const chartInstanceRef = useRef(null);
  const [chartError, setChartError] = useState(null);

  // 清理图表的安全方法
  const safeRemoveChart = () => {
    if (chartInstanceRef.current) {
      try {
        chartInstanceRef.current.remove();
      } catch (error) {
        console.warn('清理图表时出现警告:', error.message);
      } finally {
        chartInstanceRef.current = null;
      }
    }
  };

  // 创建图表
  const createChartInstance = () => {
    if (!chartContainerRef.current) return null;

    try {
      const chart = createChart(chartContainerRef.current, {
        layout: {
          backgroundColor: '#ffffff',
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          timeVisible: true,
          secondsVisible: false,
        },
        width: chartContainerRef.current.clientWidth,
        height: height - 100,
      });

      return chart;
    } catch (error) {
      console.error('创建图表失败:', error);
      setChartError(error.message);
      return null;
    }
  };

  // 处理数据更新
  useEffect(() => {
    if (loading || !data || !Array.isArray(data) || data.length === 0) {
      return;
    }

    // 清理现有图表
    safeRemoveChart();
    setChartError(null);

    // 创建新图表
    const chart = createChartInstance();
    if (!chart) return;

    chartInstanceRef.current = chart;

    try {
      // 创建K线系列 - 使用新版本API
      const candlestickSeries = chart.addSeries('Candlestick', {
        upColor: '#ef4444',
        downColor: '#22c55e',
        borderDownColor: '#22c55e',
        borderUpColor: '#ef4444',
        wickDownColor: '#22c55e',
        wickUpColor: '#ef4444',
      });

      // 处理数据
      const candlestickData = [];
      
      data.forEach(item => {
        try {
          const dateStr = item.date || item.日期;
          if (!dateStr) return;

          const date = new Date(dateStr);
          if (isNaN(date.getTime())) return;

          const timeValue = Math.floor(date.getTime() / 1000);
          const open = parseFloat(item.open || item.开盘);
          const high = parseFloat(item.high || item.最高);
          const low = parseFloat(item.low || item.最低);
          const close = parseFloat(item.close || item.收盘);

          if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {
            candlestickData.push({
              time: timeValue,
              open: open,
              high: high,
              low: low,
              close: close,
            });
          }
        } catch (error) {
          console.warn('处理数据项失败:', error);
        }
      });

      // 排序并设置数据
      if (candlestickData.length > 0) {
        candlestickData.sort((a, b) => a.time - b.time);
        candlestickSeries.setData(candlestickData);
        
        // 延迟自适应视图
        setTimeout(() => {
          if (chartInstanceRef.current) {
            try {
              chart.timeScale().fitContent();
            } catch (error) {
              console.warn('自适应视图失败:', error);
            }
          }
        }, 200);
      }

      // 窗口大小变化处理
      const handleResize = () => {
        if (chartInstanceRef.current && chartContainerRef.current) {
          try {
            chart.applyOptions({
              width: chartContainerRef.current.clientWidth,
            });
          } catch (error) {
            console.warn('调整图表大小失败:', error);
          }
        }
      };

      window.addEventListener('resize', handleResize);

      // 返回清理函数
      return () => {
        window.removeEventListener('resize', handleResize);
        safeRemoveChart();
      };

    } catch (error) {
      console.error('设置图表数据失败:', error);
      setChartError(error.message);
      safeRemoveChart();
    }
  }, [data, height, loading]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      safeRemoveChart();
    };
  }, []);

  if (chartError) {
    return (
      <Card title={title} style={{ width: '100%' }}>
        <div
          style={{
            height: height - 100,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#ff4d4f',
            fontSize: '16px',
          }}
        >
          图表加载失败: {chartError}
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title={title}
      style={{ width: '100%' }}
      bodyStyle={{ padding: '12px' }}
    >
      <Spin spinning={loading} tip="加载图表数据...">
        <div
          ref={chartContainerRef}
          style={{
            width: '100%',
            height: height - 100,
            position: 'relative',
          }}
        />
        {!loading && (!data || data.length === 0) && (
          <div
            style={{
              height: height - 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999',
              fontSize: '16px',
            }}
          >
            暂无数据
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default StableChart;
