import React, { useEffect, useRef } from 'react';
import { createChart } from 'lightweight-charts';
import { Card, Spin } from 'antd';

const SimpleChart = ({ 
  data, 
  title = 'K线图', 
  height = 500,
  loading = false 
}) => {
  const chartContainerRef = useRef();
  const chartRef = useRef();

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // 清理之前的图表
    if (chartRef.current) {
      chartRef.current.remove();
    }

    try {
      // 创建图表
      const chart = createChart(chartContainerRef.current, {
        layout: {
          backgroundColor: '#ffffff',
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          timeVisible: true,
          secondsVisible: false,
        },
        width: chartContainerRef.current.clientWidth,
        height: height - 100,
      });

      chartRef.current = chart;

      // 创建K线系列
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#ef4444',
        downColor: '#22c55e',
        borderDownColor: '#22c55e',
        borderUpColor: '#ef4444',
        wickDownColor: '#22c55e',
        wickUpColor: '#ef4444',
      });

      // 如果有数据，设置数据
      if (data && Array.isArray(data) && data.length > 0) {
        const candlestickData = data.map(item => {
          const dateStr = item.date || item.日期;
          const date = new Date(dateStr);
          const timeValue = Math.floor(date.getTime() / 1000);

          return {
            time: timeValue,
            open: parseFloat(item.open || item.开盘),
            high: parseFloat(item.high || item.最高),
            low: parseFloat(item.low || item.最低),
            close: parseFloat(item.close || item.收盘),
          };
        }).filter(item => 
          !isNaN(item.open) && !isNaN(item.high) && !isNaN(item.low) && !isNaN(item.close)
        ).sort((a, b) => a.time - b.time);

        if (candlestickData.length > 0) {
          candlestickSeries.setData(candlestickData);
          
          // 自适应视图
          setTimeout(() => {
            chart.timeScale().fitContent();
          }, 100);
        }
      }

      // 处理窗口大小变化
      const handleResize = () => {
        if (chart && chartContainerRef.current) {
          chart.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chart) {
          chart.remove();
        }
      };
    } catch (error) {
      console.error('图表创建失败:', error);
    }
  }, [data, height]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (chartRef.current) {
        chartRef.current.remove();
      }
    };
  }, []);

  return (
    <Card 
      title={title}
      style={{ width: '100%' }}
      bodyStyle={{ padding: '12px' }}
    >
      <Spin spinning={loading} tip="加载图表数据...">
        <div
          ref={chartContainerRef}
          style={{
            width: '100%',
            height: height - 100,
            position: 'relative',
          }}
        />
        {!loading && (!data || data.length === 0) && (
          <div
            style={{
              height: height - 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999',
              fontSize: '16px',
            }}
          >
            暂无数据
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default SimpleChart;
