import React, { useEffect, useRef } from 'react';
import { createChart } from 'lightweight-charts';
import { Card } from 'antd';

const TestChart = ({ data, title = '测试K线图' }) => {
  const chartContainerRef = useRef();

  useEffect(() => {
    if (!chartContainerRef.current) return;

    console.log('开始创建测试图表...');

    try {
      // 创建图表
      const chart = createChart(chartContainerRef.current, {
        width: 800,
        height: 400,
        layout: {
          backgroundColor: '#ffffff',
          textColor: '#333',
        },
      });

      console.log('图表创建成功:', chart);
      console.log('图表方法列表:', Object.getOwnPropertyNames(chart));
      console.log('图表原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(chart)));

      // 使用新版本API创建K线系列
      let candlestickSeries = null;

      try {
        console.log('使用 addSeries 方法创建K线图');
        candlestickSeries = chart.addSeries('Candlestick', {
          upColor: '#ef4444',
          downColor: '#22c55e',
          borderDownColor: '#22c55e',
          borderUpColor: '#ef4444',
          wickDownColor: '#22c55e',
          wickUpColor: '#ef4444',
        });
        console.log('K线系列创建成功:', candlestickSeries);
      } catch (error) {
        console.error('创建K线系列失败:', error);
      }

      if (candlestickSeries && data && data.length > 0) {
        console.log('设置测试数据...');
        
        // 创建测试数据
        const testData = [
          { time: '2023-01-01', open: 100, high: 110, low: 95, close: 105 },
          { time: '2023-01-02', open: 105, high: 115, low: 100, close: 108 },
          { time: '2023-01-03', open: 108, high: 112, low: 103, close: 107 },
        ];

        candlestickSeries.setData(testData);
        console.log('测试数据设置成功');
      }

      return () => {
        console.log('清理测试图表');
        chart.remove();
      };

    } catch (error) {
      console.error('测试图表创建失败:', error);
      console.error('错误堆栈:', error.stack);
    }
  }, [data]);

  return (
    <Card title={title}>
      <div
        ref={chartContainerRef}
        style={{
          width: '100%',
          height: '400px',
        }}
      />
      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        请查看浏览器控制台获取详细的API调试信息
      </div>
    </Card>
  );
};

export default TestChart;
