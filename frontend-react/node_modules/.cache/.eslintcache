[{"/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js": "1", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js": "2", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js": "3", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx": "4", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx": "5", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx": "6", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js": "7", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx": "8", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx": "9", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx": "10", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx": "11", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx": "12", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/TestChart.jsx": "13"}, {"size": 535, "mtime": 1752760976319, "results": "14", "hashOfConfig": "15"}, {"size": 1080, "mtime": 1752761758398, "results": "16", "hashOfConfig": "15"}, {"size": 362, "mtime": 1752760976321, "results": "17", "hashOfConfig": "15"}, {"size": 8500, "mtime": 1752763131977, "results": "18", "hashOfConfig": "15"}, {"size": 3897, "mtime": 1752761466308, "results": "19", "hashOfConfig": "15"}, {"size": 5451, "mtime": 1752763101799, "results": "20", "hashOfConfig": "15"}, {"size": 2903, "mtime": 1752761135061, "results": "21", "hashOfConfig": "15"}, {"size": 13709, "mtime": 1752761644629, "results": "22", "hashOfConfig": "15"}, {"size": 12515, "mtime": 1752761697944, "results": "23", "hashOfConfig": "15"}, {"size": 10963, "mtime": 1752761743681, "results": "24", "hashOfConfig": "15"}, {"size": 4817, "mtime": 1752762363612, "results": "25", "hashOfConfig": "15"}, {"size": 5828, "mtime": 1752763061156, "results": "26", "hashOfConfig": "15"}, {"size": 2536, "mtime": 1752763043387, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o9oqu7", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx", ["67", "68"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx", ["69"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx", ["70", "71"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx", ["72", "73"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx", ["74"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/TestChart.jsx", [], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 94, "column": 6, "nodeType": "77", "endLine": 94, "endColumn": 8, "suggestions": "78"}, {"ruleId": "75", "severity": 1, "message": "76", "line": 99, "column": 6, "nodeType": "77", "endLine": 99, "endColumn": 20, "suggestions": "79"}, {"ruleId": "80", "severity": 1, "message": "81", "line": 1, "column": 36, "nodeType": "82", "messageId": "83", "endLine": 1, "endColumn": 44}, {"ruleId": "80", "severity": 1, "message": "84", "line": 7, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 7, "endColumn": 8}, {"ruleId": "80", "severity": 1, "message": "85", "line": 19, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 19, "endColumn": 8}, {"ruleId": "80", "severity": 1, "message": "86", "line": 29, "column": 9, "nodeType": "82", "messageId": "83", "endLine": 29, "endColumn": 14}, {"ruleId": "75", "severity": 1, "message": "87", "line": 103, "column": 6, "nodeType": "77", "endLine": 103, "endColumn": 8, "suggestions": "88"}, {"ruleId": "75", "severity": 1, "message": "89", "line": 166, "column": 6, "nodeType": "77", "endLine": 166, "endColumn": 29, "suggestions": "90"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStockList'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], ["92"], "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'Input' is defined but never used.", "'Alert' is defined but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'refreshAll'. Either include it or remove the dependency array.", ["93"], "React Hook useEffect has a missing dependency: 'createChartInstance'. Either include it or remove the dependency array.", ["94"], {"desc": "95", "fix": "96"}, {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "101", "fix": "102"}, "Update the dependencies array to be: [loadStockList]", {"range": "103", "text": "104"}, "Update the dependencies array to be: [loadStockList, marketFilter]", {"range": "105", "text": "106"}, "Update the dependencies array to be: [refreshAll]", {"range": "107", "text": "108"}, "Update the dependencies array to be: [createChartInstance, data, height, loading]", {"range": "109", "text": "110"}, [2367, 2369], "[loadStockList]", [2431, 2445], "[loadStockList, marketFilter]", [2434, 2436], "[refreshAll]", [4213, 4236], "[createChartInstance, data, height, loading]"]