[{"/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js": "1", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js": "2", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js": "3", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx": "4", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx": "5", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx": "6", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js": "7", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx": "8", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx": "9", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx": "10", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx": "11", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx": "12"}, {"size": 535, "mtime": 1752760976319, "results": "13", "hashOfConfig": "14"}, {"size": 1080, "mtime": 1752761758398, "results": "15", "hashOfConfig": "14"}, {"size": 362, "mtime": 1752760976321, "results": "16", "hashOfConfig": "14"}, {"size": 8143, "mtime": 1752762426238, "results": "17", "hashOfConfig": "14"}, {"size": 3897, "mtime": 1752761466308, "results": "18", "hashOfConfig": "14"}, {"size": 5401, "mtime": 1752762048930, "results": "19", "hashOfConfig": "14"}, {"size": 2903, "mtime": 1752761135061, "results": "20", "hashOfConfig": "14"}, {"size": 13709, "mtime": 1752761644629, "results": "21", "hashOfConfig": "14"}, {"size": 12515, "mtime": 1752761697944, "results": "22", "hashOfConfig": "14"}, {"size": 10963, "mtime": 1752761743681, "results": "23", "hashOfConfig": "14"}, {"size": 4817, "mtime": 1752762363612, "results": "24", "hashOfConfig": "14"}, {"size": 5803, "mtime": 1752762400062, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o9oqu7", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx", ["62", "63"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx", ["64"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx", ["65", "66"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx", ["67", "68"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx", ["69"], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 93, "column": 6, "nodeType": "72", "endLine": 93, "endColumn": 8, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "71", "line": 98, "column": 6, "nodeType": "72", "endLine": 98, "endColumn": 20, "suggestions": "74"}, {"ruleId": "75", "severity": 1, "message": "76", "line": 1, "column": 36, "nodeType": "77", "messageId": "78", "endLine": 1, "endColumn": 44}, {"ruleId": "75", "severity": 1, "message": "79", "line": 7, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 7, "endColumn": 8}, {"ruleId": "75", "severity": 1, "message": "80", "line": 19, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 19, "endColumn": 8}, {"ruleId": "75", "severity": 1, "message": "81", "line": 29, "column": 9, "nodeType": "77", "messageId": "78", "endLine": 29, "endColumn": 14}, {"ruleId": "70", "severity": 1, "message": "82", "line": 103, "column": 6, "nodeType": "72", "endLine": 103, "endColumn": 8, "suggestions": "83"}, {"ruleId": "70", "severity": 1, "message": "84", "line": 166, "column": 6, "nodeType": "72", "endLine": 166, "endColumn": 29, "suggestions": "85"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStockList'. Either include it or remove the dependency array.", "ArrayExpression", ["86"], ["87"], "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'Input' is defined but never used.", "'Alert' is defined but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'refreshAll'. Either include it or remove the dependency array.", ["88"], "React Hook useEffect has a missing dependency: 'createChartInstance'. Either include it or remove the dependency array.", ["89"], {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, "Update the dependencies array to be: [loadStockList]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [loadStockList, marketFilter]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [refreshAll]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [createChartInstance, data, height, loading]", {"range": "104", "text": "105"}, [2318, 2320], "[loadStockList]", [2382, 2396], "[loadStockList, marketFilter]", [2434, 2436], "[refreshAll]", [4198, 4221], "[createChartInstance, data, height, loading]"]