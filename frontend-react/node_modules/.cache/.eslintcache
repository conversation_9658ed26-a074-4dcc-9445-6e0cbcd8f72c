[{"/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js": "1", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js": "2", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js": "3", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx": "4", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx": "5", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx": "6", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js": "7", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx": "8", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx": "9", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx": "10", "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx": "11"}, {"size": 535, "mtime": 1752760976319, "results": "12", "hashOfConfig": "13"}, {"size": 1080, "mtime": 1752761758398, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1752760976321, "results": "15", "hashOfConfig": "13"}, {"size": 8143, "mtime": 1752762102589, "results": "16", "hashOfConfig": "13"}, {"size": 3897, "mtime": 1752761466308, "results": "17", "hashOfConfig": "13"}, {"size": 5401, "mtime": 1752762048930, "results": "18", "hashOfConfig": "13"}, {"size": 2903, "mtime": 1752761135061, "results": "19", "hashOfConfig": "13"}, {"size": 13709, "mtime": 1752761644629, "results": "20", "hashOfConfig": "13"}, {"size": 12515, "mtime": 1752761697944, "results": "21", "hashOfConfig": "13"}, {"size": 10963, "mtime": 1752761743681, "results": "22", "hashOfConfig": "13"}, {"size": 3936, "mtime": 1752762076682, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o9oqu7", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/stock_data_project/frontend-react/src/index.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/reportWebVitals.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx", ["57", "58"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx", ["59"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/services/api.js", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx", [], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx", ["60", "61"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx", ["62", "63"], [], "/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx", [], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 93, "column": 6, "nodeType": "66", "endLine": 93, "endColumn": 8, "suggestions": "67"}, {"ruleId": "64", "severity": 1, "message": "65", "line": 98, "column": 6, "nodeType": "66", "endLine": 98, "endColumn": 20, "suggestions": "68"}, {"ruleId": "69", "severity": 1, "message": "70", "line": 1, "column": 36, "nodeType": "71", "messageId": "72", "endLine": 1, "endColumn": 44}, {"ruleId": "69", "severity": 1, "message": "73", "line": 7, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 7, "endColumn": 8}, {"ruleId": "69", "severity": 1, "message": "74", "line": 19, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 19, "endColumn": 8}, {"ruleId": "69", "severity": 1, "message": "75", "line": 29, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 29, "endColumn": 14}, {"ruleId": "64", "severity": 1, "message": "76", "line": 103, "column": 6, "nodeType": "66", "endLine": 103, "endColumn": 8, "suggestions": "77"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStockList'. Either include it or remove the dependency array.", "ArrayExpression", ["78"], ["79"], "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'Input' is defined but never used.", "'Alert' is defined but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'refreshAll'. Either include it or remove the dependency array.", ["80"], {"desc": "81", "fix": "82"}, {"desc": "83", "fix": "84"}, {"desc": "85", "fix": "86"}, "Update the dependencies array to be: [loadStockList]", {"range": "87", "text": "88"}, "Update the dependencies array to be: [loadStockList, marketFilter]", {"range": "89", "text": "90"}, "Update the dependencies array to be: [refreshAll]", {"range": "91", "text": "92"}, [2318, 2320], "[loadStockList]", [2382, 2396], "[loadStockList, marketFilter]", [2434, 2436], "[refreshAll]"]