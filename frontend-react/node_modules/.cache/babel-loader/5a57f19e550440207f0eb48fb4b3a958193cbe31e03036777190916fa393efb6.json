{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chartRef = useRef();\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 清理之前的图表\n    if (chartRef.current) {\n      chartRef.current.remove();\n    }\n    try {\n      // 创建图表\n      const chart = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333'\n        },\n        grid: {\n          vertLines: {\n            color: '#f0f0f0'\n          },\n          horzLines: {\n            color: '#f0f0f0'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc'\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100\n      });\n      chartRef.current = chart;\n\n      // 创建K线系列\n      const candlestickSeries = chart.addCandlestickSeries({\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444'\n      });\n\n      // 如果有数据，设置数据\n      if (data && Array.isArray(data) && data.length > 0) {\n        const candlestickData = data.map(item => {\n          const dateStr = item.date || item.日期;\n          const date = new Date(dateStr);\n          const timeValue = Math.floor(date.getTime() / 1000);\n          return {\n            time: timeValue,\n            open: parseFloat(item.open || item.开盘),\n            high: parseFloat(item.high || item.最高),\n            low: parseFloat(item.low || item.最低),\n            close: parseFloat(item.close || item.收盘)\n          };\n        }).filter(item => !isNaN(item.open) && !isNaN(item.high) && !isNaN(item.low) && !isNaN(item.close)).sort((a, b) => a.time - b.time);\n        if (candlestickData.length > 0) {\n          candlestickSeries.setData(candlestickData);\n\n          // 自适应视图\n          setTimeout(() => {\n            chart.timeScale().fitContent();\n          }, 100);\n        }\n      }\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart && chartContainerRef.current) {\n          chart.applyOptions({\n            width: chartContainerRef.current.clientWidth\n          });\n        }\n      };\n      window.addEventListener('resize', handleResize);\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart) {\n          chart.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表创建失败:', error);\n    }\n  }, [data, height]);\n\n  // 清理函数\n  useEffect(() => {\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.remove();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleChart, \"wx9h7l4vFkWzdQmBt2rnrmi4j8Q=\");\n_c = SimpleChart;\nexport default SimpleChart;\nvar _c;\n$RefreshReg$(_c, \"SimpleChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "createChart", "Card", "Spin", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "data", "title", "height", "loading", "_s", "chartContainerRef", "chartRef", "current", "remove", "chart", "layout", "backgroundColor", "textColor", "grid", "vertLines", "color", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "Array", "isArray", "length", "candlestickData", "map", "item", "dateStr", "date", "日期", "Date", "timeValue", "Math", "floor", "getTime", "time", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "filter", "isNaN", "sort", "a", "b", "setData", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "handleResize", "applyOptions", "window", "addEventListener", "removeEventListener", "error", "console", "style", "bodyStyle", "padding", "children", "spinning", "tip", "ref", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/SimpleChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin } from 'antd';\n\nconst SimpleChart = ({ \n  data, \n  title = 'K线图', \n  height = 500,\n  loading = false \n}) => {\n  const chartContainerRef = useRef();\n  const chartRef = useRef();\n\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 清理之前的图表\n    if (chartRef.current) {\n      chartRef.current.remove();\n    }\n\n    try {\n      // 创建图表\n      const chart = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333',\n        },\n        grid: {\n          vertLines: { color: '#f0f0f0' },\n          horzLines: { color: '#f0f0f0' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc',\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100,\n      });\n\n      chartRef.current = chart;\n\n      // 创建K线系列\n      const candlestickSeries = chart.addCandlestickSeries({\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444',\n      });\n\n      // 如果有数据，设置数据\n      if (data && Array.isArray(data) && data.length > 0) {\n        const candlestickData = data.map(item => {\n          const dateStr = item.date || item.日期;\n          const date = new Date(dateStr);\n          const timeValue = Math.floor(date.getTime() / 1000);\n\n          return {\n            time: timeValue,\n            open: parseFloat(item.open || item.开盘),\n            high: parseFloat(item.high || item.最高),\n            low: parseFloat(item.low || item.最低),\n            close: parseFloat(item.close || item.收盘),\n          };\n        }).filter(item => \n          !isNaN(item.open) && !isNaN(item.high) && !isNaN(item.low) && !isNaN(item.close)\n        ).sort((a, b) => a.time - b.time);\n\n        if (candlestickData.length > 0) {\n          candlestickSeries.setData(candlestickData);\n          \n          // 自适应视图\n          setTimeout(() => {\n            chart.timeScale().fitContent();\n          }, 100);\n        }\n      }\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart && chartContainerRef.current) {\n          chart.applyOptions({\n            width: chartContainerRef.current.clientWidth,\n          });\n        }\n      };\n\n      window.addEventListener('resize', handleResize);\n\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart) {\n          chart.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表创建失败:', error);\n    }\n  }, [data, height]);\n\n  // 清理函数\n  useEffect(() => {\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.remove();\n      }\n    };\n  }, []);\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default SimpleChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGZ,MAAM,CAAC,CAAC;EAClC,MAAMa,QAAQ,GAAGb,MAAM,CAAC,CAAC;EAEzBD,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,iBAAiB,CAACE,OAAO,EAAE;;IAEhC;IACA,IAAID,QAAQ,CAACC,OAAO,EAAE;MACpBD,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC;IAC3B;IAEA,IAAI;MACF;MACA,MAAMC,KAAK,GAAGf,WAAW,CAACW,iBAAiB,CAACE,OAAO,EAAE;QACnDG,MAAM,EAAE;UACNC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAC;UAC/BC,SAAS,EAAE;YAAED,KAAK,EAAE;UAAU;QAChC,CAAC;QACDE,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB,CAAC;QACDC,KAAK,EAAEnB,iBAAiB,CAACE,OAAO,CAACkB,WAAW;QAC5CvB,MAAM,EAAEA,MAAM,GAAG;MACnB,CAAC,CAAC;MAEFI,QAAQ,CAACC,OAAO,GAAGE,KAAK;;MAExB;MACA,MAAMiB,iBAAiB,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC;QACnDC,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,eAAe,EAAE,SAAS;QAC1BC,aAAa,EAAE,SAAS;QACxBC,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,IAAIjC,IAAI,IAAIkC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,IAAIA,IAAI,CAACoC,MAAM,GAAG,CAAC,EAAE;QAClD,MAAMC,eAAe,GAAGrC,IAAI,CAACsC,GAAG,CAACC,IAAI,IAAI;UACvC,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,EAAE;UACpC,MAAMD,IAAI,GAAG,IAAIE,IAAI,CAACH,OAAO,CAAC;UAC9B,MAAMI,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACM,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UAEnD,OAAO;YACLC,IAAI,EAAEJ,SAAS;YACfK,IAAI,EAAEC,UAAU,CAACX,IAAI,CAACU,IAAI,IAAIV,IAAI,CAACY,EAAE,CAAC;YACtCC,IAAI,EAAEF,UAAU,CAACX,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACc,EAAE,CAAC;YACtCC,GAAG,EAAEJ,UAAU,CAACX,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACgB,EAAE,CAAC;YACpCC,KAAK,EAAEN,UAAU,CAACX,IAAI,CAACiB,KAAK,IAAIjB,IAAI,CAACkB,EAAE;UACzC,CAAC;QACH,CAAC,CAAC,CAACC,MAAM,CAACnB,IAAI,IACZ,CAACoB,KAAK,CAACpB,IAAI,CAACU,IAAI,CAAC,IAAI,CAACU,KAAK,CAACpB,IAAI,CAACa,IAAI,CAAC,IAAI,CAACO,KAAK,CAACpB,IAAI,CAACe,GAAG,CAAC,IAAI,CAACK,KAAK,CAACpB,IAAI,CAACiB,KAAK,CACjF,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACb,IAAI,GAAGc,CAAC,CAACd,IAAI,CAAC;QAEjC,IAAIX,eAAe,CAACD,MAAM,GAAG,CAAC,EAAE;UAC9BV,iBAAiB,CAACqC,OAAO,CAAC1B,eAAe,CAAC;;UAE1C;UACA2B,UAAU,CAAC,MAAM;YACfvD,KAAK,CAACY,SAAS,CAAC,CAAC,CAAC4C,UAAU,CAAC,CAAC;UAChC,CAAC,EAAE,GAAG,CAAC;QACT;MACF;;MAEA;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAIzD,KAAK,IAAIJ,iBAAiB,CAACE,OAAO,EAAE;UACtCE,KAAK,CAAC0D,YAAY,CAAC;YACjB3C,KAAK,EAAEnB,iBAAiB,CAACE,OAAO,CAACkB;UACnC,CAAC,CAAC;QACJ;MACF,CAAC;MAED2C,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MAE/C,OAAO,MAAM;QACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;QAClD,IAAIzD,KAAK,EAAE;UACTA,KAAK,CAACD,MAAM,CAAC,CAAC;QAChB;MACF,CAAC;IACH,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC,EAAE,CAACvE,IAAI,EAAEE,MAAM,CAAC,CAAC;;EAElB;EACAV,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIc,QAAQ,CAACC,OAAO,EAAE;QACpBD,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEV,OAAA,CAACH,IAAI;IACHM,KAAK,EAAEA,KAAM;IACbwE,KAAK,EAAE;MAAEjD,KAAK,EAAE;IAAO,CAAE;IACzBkD,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,eAE/B9E,OAAA,CAACF,IAAI;MAACiF,QAAQ,EAAE1E,OAAQ;MAAC2E,GAAG,EAAC,yCAAW;MAAAF,QAAA,gBACtC9E,OAAA;QACEiF,GAAG,EAAE1E,iBAAkB;QACvBoE,KAAK,EAAE;UACLjD,KAAK,EAAE,MAAM;UACbtB,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB8E,QAAQ,EAAE;QACZ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACjF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACoC,MAAM,KAAK,CAAC,CAAC,iBACvCtC,OAAA;QACE2E,KAAK,EAAE;UACLvE,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpBmF,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBxE,KAAK,EAAE,MAAM;UACbyE,QAAQ,EAAE;QACZ,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAChF,EAAA,CAlJIL,WAAW;AAAA0F,EAAA,GAAX1F,WAAW;AAoJjB,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}