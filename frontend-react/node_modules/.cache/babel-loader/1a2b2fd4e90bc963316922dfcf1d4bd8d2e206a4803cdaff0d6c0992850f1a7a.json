{"ast": null, "code": "const genSorterStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    fontSizeIcon,\n    headerIconColor,\n    headerIconHoverColor\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-thead th${componentCls}-column-has-sorters`]: {\n        outline: 'none',\n        cursor: 'pointer',\n        // why left 0s? Avoid column header move with transition when left is changed\n        // https://github.com/ant-design/ant-design/issues/50588\n        transition: `all ${token.motionDurationSlow}, left 0s`,\n        '&:hover': {\n          background: token.tableHeaderSortHoverBg,\n          '&::before': {\n            backgroundColor: 'transparent !important'\n          }\n        },\n        '&:focus-visible': {\n          color: token.colorPrimary\n        },\n        // https://github.com/ant-design/ant-design/issues/30969\n        [`\n          &${componentCls}-cell-fix-left:hover,\n          &${componentCls}-cell-fix-right:hover\n        `]: {\n          background: token.tableFixedHeaderSortActiveBg\n        }\n      },\n      [`${componentCls}-thead th${componentCls}-column-sort`]: {\n        background: token.tableHeaderSortBg,\n        '&::before': {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`td${componentCls}-column-sort`]: {\n        background: token.tableBodySortBg\n      },\n      [`${componentCls}-column-title`]: {\n        position: 'relative',\n        zIndex: 1,\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-column-sorters`]: {\n        display: 'flex',\n        flex: 'auto',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          height: '100%',\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-column-sorters-tooltip-target-sorter`]: {\n        '&::after': {\n          content: 'none'\n        }\n      },\n      [`${componentCls}-column-sorter`]: {\n        marginInlineStart: marginXXS,\n        color: headerIconColor,\n        fontSize: 0,\n        transition: `color ${token.motionDurationSlow}`,\n        '&-inner': {\n          display: 'inline-flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        '&-up, &-down': {\n          fontSize: fontSizeIcon,\n          '&.active': {\n            color: token.colorPrimary\n          }\n        },\n        [`${componentCls}-column-sorter-up + ${componentCls}-column-sorter-down`]: {\n          marginTop: '-0.3em'\n        }\n      },\n      [`${componentCls}-column-sorters:hover ${componentCls}-column-sorter`]: {\n        color: headerIconHoverColor\n      }\n    }\n  };\n};\nexport default genSorterStyle;", "map": {"version": 3, "names": ["genSorterStyle", "token", "componentCls", "marginXXS", "fontSizeIcon", "headerIconColor", "headerIconHoverColor", "outline", "cursor", "transition", "motionDurationSlow", "background", "tableHeaderSortHoverBg", "backgroundColor", "color", "colorPrimary", "tableFixedHeaderSortActiveBg", "tableHeaderSortBg", "tableBodySortBg", "position", "zIndex", "flex", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "justifyContent", "inset", "width", "height", "content", "marginInlineStart", "fontSize", "flexDirection", "marginTop"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/es/table/style/sorter.js"], "sourcesContent": ["const genSorterStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    fontSizeIcon,\n    headerIconColor,\n    headerIconHoverColor\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-thead th${componentCls}-column-has-sorters`]: {\n        outline: 'none',\n        cursor: 'pointer',\n        // why left 0s? Avoid column header move with transition when left is changed\n        // https://github.com/ant-design/ant-design/issues/50588\n        transition: `all ${token.motionDurationSlow}, left 0s`,\n        '&:hover': {\n          background: token.tableHeaderSortHoverBg,\n          '&::before': {\n            backgroundColor: 'transparent !important'\n          }\n        },\n        '&:focus-visible': {\n          color: token.colorPrimary\n        },\n        // https://github.com/ant-design/ant-design/issues/30969\n        [`\n          &${componentCls}-cell-fix-left:hover,\n          &${componentCls}-cell-fix-right:hover\n        `]: {\n          background: token.tableFixedHeaderSortActiveBg\n        }\n      },\n      [`${componentCls}-thead th${componentCls}-column-sort`]: {\n        background: token.tableHeaderSortBg,\n        '&::before': {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`td${componentCls}-column-sort`]: {\n        background: token.tableBodySortBg\n      },\n      [`${componentCls}-column-title`]: {\n        position: 'relative',\n        zIndex: 1,\n        flex: 1,\n        minWidth: 0\n      },\n      [`${componentCls}-column-sorters`]: {\n        display: 'flex',\n        flex: 'auto',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          height: '100%',\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-column-sorters-tooltip-target-sorter`]: {\n        '&::after': {\n          content: 'none'\n        }\n      },\n      [`${componentCls}-column-sorter`]: {\n        marginInlineStart: marginXXS,\n        color: headerIconColor,\n        fontSize: 0,\n        transition: `color ${token.motionDurationSlow}`,\n        '&-inner': {\n          display: 'inline-flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        '&-up, &-down': {\n          fontSize: fontSizeIcon,\n          '&.active': {\n            color: token.colorPrimary\n          }\n        },\n        [`${componentCls}-column-sorter-up + ${componentCls}-column-sorter-down`]: {\n          marginTop: '-0.3em'\n        }\n      },\n      [`${componentCls}-column-sorters:hover ${componentCls}-column-sorter`]: {\n        color: headerIconHoverColor\n      }\n    }\n  };\n};\nexport default genSorterStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,eAAe;IACfC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,YAAYA,YAAY,qBAAqB,GAAG;QAC9DK,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,SAAS;QACjB;QACA;QACAC,UAAU,EAAE,OAAOR,KAAK,CAACS,kBAAkB,WAAW;QACtD,SAAS,EAAE;UACTC,UAAU,EAAEV,KAAK,CAACW,sBAAsB;UACxC,WAAW,EAAE;YACXC,eAAe,EAAE;UACnB;QACF,CAAC;QACD,iBAAiB,EAAE;UACjBC,KAAK,EAAEb,KAAK,CAACc;QACf,CAAC;QACD;QACA,CAAC;AACT,aAAab,YAAY;AACzB,aAAaA,YAAY;AACzB,SAAS,GAAG;UACFS,UAAU,EAAEV,KAAK,CAACe;QACpB;MACF,CAAC;MACD,CAAC,GAAGd,YAAY,YAAYA,YAAY,cAAc,GAAG;QACvDS,UAAU,EAAEV,KAAK,CAACgB,iBAAiB;QACnC,WAAW,EAAE;UACXJ,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,KAAKX,YAAY,cAAc,GAAG;QACjCS,UAAU,EAAEV,KAAK,CAACiB;MACpB,CAAC;MACD,CAAC,GAAGhB,YAAY,eAAe,GAAG;QAChCiB,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC;MACD,CAAC,GAAGpB,YAAY,iBAAiB,GAAG;QAClCqB,OAAO,EAAE,MAAM;QACfF,IAAI,EAAE,MAAM;QACZG,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/B,UAAU,EAAE;UACVN,QAAQ,EAAE,UAAU;UACpBO,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;QACX;MACF,CAAC;MACD,CAAC,GAAG3B,YAAY,uCAAuC,GAAG;QACxD,UAAU,EAAE;UACV2B,OAAO,EAAE;QACX;MACF,CAAC;MACD,CAAC,GAAG3B,YAAY,gBAAgB,GAAG;QACjC4B,iBAAiB,EAAE3B,SAAS;QAC5BW,KAAK,EAAET,eAAe;QACtB0B,QAAQ,EAAE,CAAC;QACXtB,UAAU,EAAE,SAASR,KAAK,CAACS,kBAAkB,EAAE;QAC/C,SAAS,EAAE;UACTa,OAAO,EAAE,aAAa;UACtBS,aAAa,EAAE,QAAQ;UACvBR,UAAU,EAAE;QACd,CAAC;QACD,cAAc,EAAE;UACdO,QAAQ,EAAE3B,YAAY;UACtB,UAAU,EAAE;YACVU,KAAK,EAAEb,KAAK,CAACc;UACf;QACF,CAAC;QACD,CAAC,GAAGb,YAAY,uBAAuBA,YAAY,qBAAqB,GAAG;UACzE+B,SAAS,EAAE;QACb;MACF,CAAC;MACD,CAAC,GAAG/B,YAAY,yBAAyBA,YAAY,gBAAgB,GAAG;QACtEY,KAAK,EAAER;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}