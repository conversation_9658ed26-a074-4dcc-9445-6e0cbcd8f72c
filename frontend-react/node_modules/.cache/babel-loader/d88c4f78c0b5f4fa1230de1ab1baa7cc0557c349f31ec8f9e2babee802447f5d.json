{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n    try {\n      // 创建图表\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333'\n        },\n        grid: {\n          vertLines: {\n            color: '#f0f0f0'\n          },\n          horzLines: {\n            color: '#f0f0f0'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc'\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100\n      });\n\n      // 创建K线系列\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444'\n      });\n\n      // 创建成交量系列\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume'\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0\n        }\n      });\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart.current && chartContainerRef.current) {\n          chart.current.applyOptions({\n            width: chartContainerRef.current.clientWidth\n          });\n        }\n      };\n      window.addEventListener('resize', handleResize);\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart.current) {\n          chart.current.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表初始化失败:', error);\n    }\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n\n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘)\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)'\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u56FE\\u8868\\u52A0\\u8F7D\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(LightweightChart, \"xsFfQmiJkkGMDJZPToJPhbAj94o=\");\n_c = LightweightChart;\nexport default LightweightChart;\nvar _c;\n$RefreshReg$(_c, \"LightweightChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "createChart", "Card", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LightweightChart", "data", "title", "height", "loading", "error", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "current", "layout", "backgroundColor", "textColor", "grid", "vertLines", "color", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "addHistogramSeries", "priceFormat", "type", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "handleResize", "applyOptions", "window", "addEventListener", "removeEventListener", "remove", "console", "chartReady", "Array", "isArray", "length", "candlestickData", "volumeData", "for<PERSON>ach", "item", "time", "Date", "date", "日期", "getTime", "push", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "value", "volume", "成交量", "sort", "a", "b", "setData", "<PERSON><PERSON><PERSON><PERSON>", "children", "message", "description", "showIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "bodyStyle", "padding", "spinning", "tip", "ref", "position", "display", "alignItems", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\n\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    try {\n      // 创建图表\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333',\n        },\n        grid: {\n          vertLines: { color: '#f0f0f0' },\n          horzLines: { color: '#f0f0f0' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc',\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100,\n      });\n\n      // 创建K线系列\n      candlestickSeries.current = chart.current.addCandlestickSeries({\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444',\n      });\n\n      // 创建成交量系列\n      volumeSeries.current = chart.current.addHistogramSeries({\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0,\n        },\n      });\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart.current && chartContainerRef.current) {\n          chart.current.applyOptions({\n            width: chartContainerRef.current.clientWidth,\n          });\n        }\n      };\n\n      window.addEventListener('resize', handleResize);\n\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart.current) {\n          chart.current.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表初始化失败:', error);\n    }\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n        \n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘),\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? \n            'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)',\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      \n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n\n  if (error) {\n    return (\n      <Card title={title}>\n        <Alert\n          message=\"图表加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n        />\n      </Card>\n    );\n  }\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default LightweightChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGf,MAAM,CAAC,CAAC;EAClC,MAAMgB,KAAK,GAAGhB,MAAM,CAAC,CAAC;EACtB,MAAMiB,iBAAiB,GAAGjB,MAAM,CAAC,CAAC;EAClC,MAAMkB,YAAY,GAAGlB,MAAM,CAAC,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,iBAAiB,CAACI,OAAO,EAAE;IAEhC,IAAI;MACF;MACAH,KAAK,CAACG,OAAO,GAAGjB,WAAW,CAACa,iBAAiB,CAACI,OAAO,EAAE;QACrDC,MAAM,EAAE;UACNC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAC;UAC/BC,SAAS,EAAE;YAAED,KAAK,EAAE;UAAU;QAChC,CAAC;QACDE,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB,CAAC;QACDC,KAAK,EAAEnB,iBAAiB,CAACI,OAAO,CAACgB,WAAW;QAC5CxB,MAAM,EAAEA,MAAM,GAAG;MACnB,CAAC,CAAC;;MAEF;MACAM,iBAAiB,CAACE,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACiB,oBAAoB,CAAC;QAC7DC,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,eAAe,EAAE,SAAS;QAC1BC,aAAa,EAAE,SAAS;QACxBC,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACAxB,YAAY,CAACC,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACwB,kBAAkB,CAAC;QACtDlB,KAAK,EAAE,SAAS;QAChBmB,WAAW,EAAE;UACXC,IAAI,EAAE;QACR,CAAC;QACDC,YAAY,EAAE,QAAQ;QACtBC,YAAY,EAAE;UACZC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE;QACV;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAIlC,KAAK,CAACG,OAAO,IAAIJ,iBAAiB,CAACI,OAAO,EAAE;UAC9CH,KAAK,CAACG,OAAO,CAACgC,YAAY,CAAC;YACzBjB,KAAK,EAAEnB,iBAAiB,CAACI,OAAO,CAACgB;UACnC,CAAC,CAAC;QACJ;MACF,CAAC;MAEDiB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MAE/C,OAAO,MAAM;QACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;QAClD,IAAIlC,KAAK,CAACG,OAAO,EAAE;UACjBH,KAAK,CAACG,OAAO,CAACoC,MAAM,CAAC,CAAC;QACxB;MACF,CAAC;IACH,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACd2C,OAAO,CAAC3C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACF,MAAM,CAAC,CAAC;;EAEZ;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0D,UAAU,IAAI,CAAChD,IAAI,IAAI,CAACiD,KAAK,CAACC,OAAO,CAAClD,IAAI,CAAC,IAAIA,IAAI,CAACmD,MAAM,KAAK,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,UAAU,GAAG,EAAE;MAErBrD,IAAI,CAACsD,OAAO,CAACC,IAAI,IAAI;QACnB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACI,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI;;QAE5D;QACAR,eAAe,CAACS,IAAI,CAAC;UACnBL,IAAI,EAAEA,IAAI;UACVM,IAAI,EAAEC,UAAU,CAACR,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC;UACtCC,IAAI,EAAEF,UAAU,CAACR,IAAI,CAACU,IAAI,IAAIV,IAAI,CAACW,EAAE,CAAC;UACtCC,GAAG,EAAEJ,UAAU,CAACR,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,EAAE,CAAC;UACpCC,KAAK,EAAEN,UAAU,CAACR,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE;QACzC,CAAC,CAAC;;QAEF;QACAjB,UAAU,CAACQ,IAAI,CAAC;UACdL,IAAI,EAAEA,IAAI;UACVe,KAAK,EAAER,UAAU,CAACR,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACkB,GAAG,CAAC;UAC1CzD,KAAK,EAAE,CAACuC,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE,MAAMf,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC,GACtD,wBAAwB,GAAG;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAZ,eAAe,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;MAC/CH,UAAU,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;;MAE1C;MACA,IAAIhD,iBAAiB,CAACE,OAAO,EAAE;QAC7BF,iBAAiB,CAACE,OAAO,CAACmE,OAAO,CAACzB,eAAe,CAAC;MACpD;MAEA,IAAI3C,YAAY,CAACC,OAAO,EAAE;QACxBD,YAAY,CAACC,OAAO,CAACmE,OAAO,CAACxB,UAAU,CAAC;MAC1C;;MAEA;MACA,IAAI9C,KAAK,CAACG,OAAO,EAAE;QACjBH,KAAK,CAACG,OAAO,CAACY,SAAS,CAAC,CAAC,CAACwD,UAAU,CAAC,CAAC;MACxC;IAEF,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACd2C,OAAO,CAAC3C,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEgD,UAAU,CAAC,CAAC;EAEtB,IAAI5C,KAAK,EAAE;IACT,oBACEN,OAAA,CAACJ,IAAI;MAACO,KAAK,EAAEA,KAAM;MAAA8E,QAAA,eACjBjF,OAAA,CAACF,KAAK;QACJoF,OAAO,EAAC,sCAAQ;QAChBC,WAAW,EAAE7E,KAAM;QACnBgC,IAAI,EAAC,OAAO;QACZ8C,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX;EAEA,oBACExF,OAAA,CAACJ,IAAI;IACHO,KAAK,EAAEA,KAAM;IACbsF,KAAK,EAAE;MAAE9D,KAAK,EAAE;IAAO,CAAE;IACzB+D,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,eAE/BjF,OAAA,CAACH,IAAI;MAAC+F,QAAQ,EAAEvF,OAAQ;MAACwF,GAAG,EAAC,yCAAW;MAAAZ,QAAA,gBACtCjF,OAAA;QACE8F,GAAG,EAAEtF,iBAAkB;QACvBiF,KAAK,EAAE;UACL9D,KAAK,EAAE,MAAM;UACbvB,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB2F,QAAQ,EAAE;QACZ;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACnF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACmD,MAAM,KAAK,CAAC,CAAC,iBACvCrD,OAAA;QACEyF,KAAK,EAAE;UACLrF,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB4F,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBhF,KAAK,EAAE,MAAM;UACbiF,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACjF,EAAA,CA3LIN,gBAAgB;AAAAmG,EAAA,GAAhBnG,gBAAgB;AA6LtB,eAAeA,gBAAgB;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}