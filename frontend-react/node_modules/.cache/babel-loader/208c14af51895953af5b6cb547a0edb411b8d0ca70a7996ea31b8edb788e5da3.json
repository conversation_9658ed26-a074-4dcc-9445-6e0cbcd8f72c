{"ast": null, "code": "import { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport { warning } from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { getWin } from \"../util\";\nexport default function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n  var openRef = React.useRef(open);\n  openRef.current = open;\n  var popupPointerDownRef = React.useRef(false);\n\n  // Click to hide is special action since click popup element should not hide\n  React.useEffect(function () {\n    if (clickToHide && popupEle && (!mask || maskClosable)) {\n      var onPointerDown = function onPointerDown() {\n        popupPointerDownRef.current = false;\n      };\n      var onTriggerClose = function onTriggerClose(e) {\n        var _e$composedPath;\n        if (openRef.current && !inPopupOrChild(((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 || (_e$composedPath = _e$composedPath.call(e)) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath[0]) || e.target) && !popupPointerDownRef.current) {\n          triggerOpen(false);\n        }\n      };\n      var win = getWin(popupEle);\n      win.addEventListener('pointerdown', onPointerDown, true);\n      win.addEventListener('mousedown', onTriggerClose, true);\n      win.addEventListener('contextmenu', onTriggerClose, true);\n\n      // shadow root\n      var targetShadowRoot = getShadowRoot(targetEle);\n      if (targetShadowRoot) {\n        targetShadowRoot.addEventListener('mousedown', onTriggerClose, true);\n        targetShadowRoot.addEventListener('contextmenu', onTriggerClose, true);\n      }\n\n      // Warning if target and popup not in same root\n      if (process.env.NODE_ENV !== 'production') {\n        var _targetEle$getRootNod, _popupEle$getRootNode;\n        var targetRoot = targetEle === null || targetEle === void 0 || (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n        var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n        warning(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n      }\n      return function () {\n        win.removeEventListener('pointerdown', onPointerDown, true);\n        win.removeEventListener('mousedown', onTriggerClose, true);\n        win.removeEventListener('contextmenu', onTriggerClose, true);\n        if (targetShadowRoot) {\n          targetShadowRoot.removeEventListener('mousedown', onTriggerClose, true);\n          targetShadowRoot.removeEventListener('contextmenu', onTriggerClose, true);\n        }\n      };\n    }\n  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);\n  function onPopupPointerDown() {\n    popupPointerDownRef.current = true;\n  }\n  return onPopupPointerDown;\n}", "map": {"version": 3, "names": ["getShadowRoot", "warning", "React", "getWin", "useWinClick", "open", "clickToHide", "targetEle", "popup<PERSON>le", "mask", "maskClosable", "inPopupOrChild", "triggerOpen", "openRef", "useRef", "current", "popupPointerDownRef", "useEffect", "onPointerDown", "onTriggerClose", "e", "_e$composedPath", "<PERSON><PERSON><PERSON>", "call", "target", "win", "addEventListener", "targetShadowRoot", "process", "env", "NODE_ENV", "_targetEle$getRootNod", "_popupEle$getRootNode", "targetRoot", "getRootNode", "popupRoot", "removeEventListener", "onPopupPointerDown"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@rc-component/trigger/es/hooks/useWinClick.js"], "sourcesContent": ["import { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport { warning } from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { getWin } from \"../util\";\nexport default function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n  var openRef = React.useRef(open);\n  openRef.current = open;\n  var popupPointerDownRef = React.useRef(false);\n\n  // Click to hide is special action since click popup element should not hide\n  React.useEffect(function () {\n    if (clickToHide && popupEle && (!mask || maskClosable)) {\n      var onPointerDown = function onPointerDown() {\n        popupPointerDownRef.current = false;\n      };\n      var onTriggerClose = function onTriggerClose(e) {\n        var _e$composedPath;\n        if (openRef.current && !inPopupOrChild(((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 || (_e$composedPath = _e$composedPath.call(e)) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath[0]) || e.target) && !popupPointerDownRef.current) {\n          triggerOpen(false);\n        }\n      };\n      var win = getWin(popupEle);\n      win.addEventListener('pointerdown', onPointerDown, true);\n      win.addEventListener('mousedown', onTriggerClose, true);\n      win.addEventListener('contextmenu', onTriggerClose, true);\n\n      // shadow root\n      var targetShadowRoot = getShadowRoot(targetEle);\n      if (targetShadowRoot) {\n        targetShadowRoot.addEventListener('mousedown', onTriggerClose, true);\n        targetShadowRoot.addEventListener('contextmenu', onTriggerClose, true);\n      }\n\n      // Warning if target and popup not in same root\n      if (process.env.NODE_ENV !== 'production') {\n        var _targetEle$getRootNod, _popupEle$getRootNode;\n        var targetRoot = targetEle === null || targetEle === void 0 || (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n        var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n        warning(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n      }\n      return function () {\n        win.removeEventListener('pointerdown', onPointerDown, true);\n        win.removeEventListener('mousedown', onTriggerClose, true);\n        win.removeEventListener('contextmenu', onTriggerClose, true);\n        if (targetShadowRoot) {\n          targetShadowRoot.removeEventListener('mousedown', onTriggerClose, true);\n          targetShadowRoot.removeEventListener('contextmenu', onTriggerClose, true);\n        }\n      };\n    }\n  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);\n  function onPopupPointerDown() {\n    popupPointerDownRef.current = true;\n  }\n  return onPopupPointerDown;\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,SAAS;AAChC,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAE;EAC3H,IAAIC,OAAO,GAAGX,KAAK,CAACY,MAAM,CAACT,IAAI,CAAC;EAChCQ,OAAO,CAACE,OAAO,GAAGV,IAAI;EACtB,IAAIW,mBAAmB,GAAGd,KAAK,CAACY,MAAM,CAAC,KAAK,CAAC;;EAE7C;EACAZ,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,IAAIX,WAAW,IAAIE,QAAQ,KAAK,CAACC,IAAI,IAAIC,YAAY,CAAC,EAAE;MACtD,IAAIQ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;QAC3CF,mBAAmB,CAACD,OAAO,GAAG,KAAK;MACrC,CAAC;MACD,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;QAC9C,IAAIC,eAAe;QACnB,IAAIR,OAAO,CAACE,OAAO,IAAI,CAACJ,cAAc,CAAC,CAAC,CAACU,eAAe,GAAGD,CAAC,CAACE,YAAY,MAAM,IAAI,IAAID,eAAe,KAAK,KAAK,CAAC,IAAI,CAACA,eAAe,GAAGA,eAAe,CAACE,IAAI,CAACH,CAAC,CAAC,MAAM,IAAI,IAAIC,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,KAAKD,CAAC,CAACI,MAAM,CAAC,IAAI,CAACR,mBAAmB,CAACD,OAAO,EAAE;UACnRH,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC;MACD,IAAIa,GAAG,GAAGtB,MAAM,CAACK,QAAQ,CAAC;MAC1BiB,GAAG,CAACC,gBAAgB,CAAC,aAAa,EAAER,aAAa,EAAE,IAAI,CAAC;MACxDO,GAAG,CAACC,gBAAgB,CAAC,WAAW,EAAEP,cAAc,EAAE,IAAI,CAAC;MACvDM,GAAG,CAACC,gBAAgB,CAAC,aAAa,EAAEP,cAAc,EAAE,IAAI,CAAC;;MAEzD;MACA,IAAIQ,gBAAgB,GAAG3B,aAAa,CAACO,SAAS,CAAC;MAC/C,IAAIoB,gBAAgB,EAAE;QACpBA,gBAAgB,CAACD,gBAAgB,CAAC,WAAW,EAAEP,cAAc,EAAE,IAAI,CAAC;QACpEQ,gBAAgB,CAACD,gBAAgB,CAAC,aAAa,EAAEP,cAAc,EAAE,IAAI,CAAC;MACxE;;MAEA;MACA,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIC,qBAAqB,EAAEC,qBAAqB;QAChD,IAAIC,UAAU,GAAG1B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAI,CAACwB,qBAAqB,GAAGxB,SAAS,CAAC2B,WAAW,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACR,IAAI,CAAChB,SAAS,CAAC;QAC5M,IAAI4B,SAAS,GAAG,CAACH,qBAAqB,GAAGxB,QAAQ,CAAC0B,WAAW,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACT,IAAI,CAACf,QAAQ,CAAC;QAC3JP,OAAO,CAACgC,UAAU,KAAKE,SAAS,EAAE,+DAA+D,CAAC;MACpG;MACA,OAAO,YAAY;QACjBV,GAAG,CAACW,mBAAmB,CAAC,aAAa,EAAElB,aAAa,EAAE,IAAI,CAAC;QAC3DO,GAAG,CAACW,mBAAmB,CAAC,WAAW,EAAEjB,cAAc,EAAE,IAAI,CAAC;QAC1DM,GAAG,CAACW,mBAAmB,CAAC,aAAa,EAAEjB,cAAc,EAAE,IAAI,CAAC;QAC5D,IAAIQ,gBAAgB,EAAE;UACpBA,gBAAgB,CAACS,mBAAmB,CAAC,WAAW,EAAEjB,cAAc,EAAE,IAAI,CAAC;UACvEQ,gBAAgB,CAACS,mBAAmB,CAAC,aAAa,EAAEjB,cAAc,EAAE,IAAI,CAAC;QAC3E;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACb,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,CAAC,CAAC;EAC1D,SAAS2B,kBAAkBA,CAAA,EAAG;IAC5BrB,mBAAmB,CAACD,OAAO,GAAG,IAAI;EACpC;EACA,OAAOsB,kBAAkB;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}