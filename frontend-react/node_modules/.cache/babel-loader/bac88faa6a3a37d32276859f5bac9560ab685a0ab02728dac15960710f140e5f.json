{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { createPortal } from 'react-dom';\nimport NoticeList from \"./NoticeList\";\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-notification' : _props$prefixCls,\n    container = props.container,\n    motion = props.motion,\n    maxCount = props.maxCount,\n    className = props.className,\n    style = props.style,\n    onAllRemoved = props.onAllRemoved,\n    stack = props.stack,\n    renderNotifications = props.renderNotifications;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    configList = _React$useState2[0],\n    setConfigList = _React$useState2[1];\n\n  // ======================== Close =========================\n  var onNoticeClose = function onNoticeClose(key) {\n    var _config$onClose;\n    // Trigger close event\n    var config = configList.find(function (item) {\n      return item.key === key;\n    });\n    config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n    setConfigList(function (list) {\n      return list.filter(function (item) {\n        return item.key !== key;\n      });\n    });\n  };\n\n  // ========================= Refs =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      open: function open(config) {\n        setConfigList(function (list) {\n          var clone = _toConsumableArray(list);\n\n          // Replace if exist\n          var index = clone.findIndex(function (item) {\n            return item.key === config.key;\n          });\n          var innerConfig = _objectSpread({}, config);\n          if (index >= 0) {\n            var _list$index;\n            innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n            clone[index] = innerConfig;\n          } else {\n            innerConfig.times = 0;\n            clone.push(innerConfig);\n          }\n          if (maxCount > 0 && clone.length > maxCount) {\n            clone = clone.slice(-maxCount);\n          }\n          return clone;\n        });\n      },\n      close: function close(key) {\n        onNoticeClose(key);\n      },\n      destroy: function destroy() {\n        setConfigList([]);\n      }\n    };\n  });\n\n  // ====================== Placements ======================\n  var _React$useState3 = React.useState({}),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    placements = _React$useState4[0],\n    setPlacements = _React$useState4[1];\n  React.useEffect(function () {\n    var nextPlacements = {};\n    configList.forEach(function (config) {\n      var _config$placement = config.placement,\n        placement = _config$placement === void 0 ? 'topRight' : _config$placement;\n      if (placement) {\n        nextPlacements[placement] = nextPlacements[placement] || [];\n        nextPlacements[placement].push(config);\n      }\n    });\n\n    // Fill exist placements to avoid empty list causing remove without motion\n    Object.keys(placements).forEach(function (placement) {\n      nextPlacements[placement] = nextPlacements[placement] || [];\n    });\n    setPlacements(nextPlacements);\n  }, [configList]);\n\n  // Clean up container if all notices fade out\n  var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n    setPlacements(function (originPlacements) {\n      var clone = _objectSpread({}, originPlacements);\n      var list = clone[placement] || [];\n      if (!list.length) {\n        delete clone[placement];\n      }\n      return clone;\n    });\n  };\n\n  // Effect tell that placements is empty now\n  var emptyRef = React.useRef(false);\n  React.useEffect(function () {\n    if (Object.keys(placements).length > 0) {\n      emptyRef.current = true;\n    } else if (emptyRef.current) {\n      // Trigger only when from exist to empty\n      onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n      emptyRef.current = false;\n    }\n  }, [placements]);\n  // ======================== Render ========================\n  if (!container) {\n    return null;\n  }\n  var placementList = Object.keys(placements);\n  return /*#__PURE__*/createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, placementList.map(function (placement) {\n    var placementConfigList = placements[placement];\n    var list = /*#__PURE__*/React.createElement(NoticeList, {\n      key: placement,\n      configList: placementConfigList,\n      placement: placement,\n      prefixCls: prefixCls,\n      className: className === null || className === void 0 ? void 0 : className(placement),\n      style: style === null || style === void 0 ? void 0 : style(placement),\n      motion: motion,\n      onNoticeClose: onNoticeClose,\n      onAllNoticeRemoved: onAllNoticeRemoved,\n      stack: stack\n    });\n    return renderNotifications ? renderNotifications(list, {\n      prefixCls: prefixCls,\n      key: placement\n    }) : list;\n  })), container);\n});\nif (process.env.NODE_ENV !== 'production') {\n  Notifications.displayName = 'Notifications';\n}\nexport default Notifications;", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "_slicedToArray", "React", "createPortal", "NoticeList", "Notifications", "forwardRef", "props", "ref", "_props$prefixCls", "prefixCls", "container", "motion", "maxCount", "className", "style", "onAllRemoved", "stack", "renderNotifications", "_React$useState", "useState", "_React$useState2", "configList", "setConfigList", "onNoticeClose", "key", "_config$onClose", "config", "find", "item", "onClose", "call", "list", "filter", "useImperativeHandle", "open", "clone", "index", "findIndex", "innerConfig", "_list$index", "times", "push", "length", "slice", "close", "destroy", "_React$useState3", "_React$useState4", "placements", "setPlacements", "useEffect", "nextPlacements", "for<PERSON>ach", "_config$placement", "placement", "Object", "keys", "onAllNoticeRemoved", "originPlacements", "emptyRef", "useRef", "current", "placementList", "createElement", "Fragment", "map", "placementConfigList", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-notification/es/Notifications.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { createPortal } from 'react-dom';\nimport NoticeList from \"./NoticeList\";\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-notification' : _props$prefixCls,\n    container = props.container,\n    motion = props.motion,\n    maxCount = props.maxCount,\n    className = props.className,\n    style = props.style,\n    onAllRemoved = props.onAllRemoved,\n    stack = props.stack,\n    renderNotifications = props.renderNotifications;\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    configList = _React$useState2[0],\n    setConfigList = _React$useState2[1];\n\n  // ======================== Close =========================\n  var onNoticeClose = function onNoticeClose(key) {\n    var _config$onClose;\n    // Trigger close event\n    var config = configList.find(function (item) {\n      return item.key === key;\n    });\n    config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n    setConfigList(function (list) {\n      return list.filter(function (item) {\n        return item.key !== key;\n      });\n    });\n  };\n\n  // ========================= Refs =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      open: function open(config) {\n        setConfigList(function (list) {\n          var clone = _toConsumableArray(list);\n\n          // Replace if exist\n          var index = clone.findIndex(function (item) {\n            return item.key === config.key;\n          });\n          var innerConfig = _objectSpread({}, config);\n          if (index >= 0) {\n            var _list$index;\n            innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n            clone[index] = innerConfig;\n          } else {\n            innerConfig.times = 0;\n            clone.push(innerConfig);\n          }\n          if (maxCount > 0 && clone.length > maxCount) {\n            clone = clone.slice(-maxCount);\n          }\n          return clone;\n        });\n      },\n      close: function close(key) {\n        onNoticeClose(key);\n      },\n      destroy: function destroy() {\n        setConfigList([]);\n      }\n    };\n  });\n\n  // ====================== Placements ======================\n  var _React$useState3 = React.useState({}),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    placements = _React$useState4[0],\n    setPlacements = _React$useState4[1];\n  React.useEffect(function () {\n    var nextPlacements = {};\n    configList.forEach(function (config) {\n      var _config$placement = config.placement,\n        placement = _config$placement === void 0 ? 'topRight' : _config$placement;\n      if (placement) {\n        nextPlacements[placement] = nextPlacements[placement] || [];\n        nextPlacements[placement].push(config);\n      }\n    });\n\n    // Fill exist placements to avoid empty list causing remove without motion\n    Object.keys(placements).forEach(function (placement) {\n      nextPlacements[placement] = nextPlacements[placement] || [];\n    });\n    setPlacements(nextPlacements);\n  }, [configList]);\n\n  // Clean up container if all notices fade out\n  var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n    setPlacements(function (originPlacements) {\n      var clone = _objectSpread({}, originPlacements);\n      var list = clone[placement] || [];\n      if (!list.length) {\n        delete clone[placement];\n      }\n      return clone;\n    });\n  };\n\n  // Effect tell that placements is empty now\n  var emptyRef = React.useRef(false);\n  React.useEffect(function () {\n    if (Object.keys(placements).length > 0) {\n      emptyRef.current = true;\n    } else if (emptyRef.current) {\n      // Trigger only when from exist to empty\n      onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n      emptyRef.current = false;\n    }\n  }, [placements]);\n  // ======================== Render ========================\n  if (!container) {\n    return null;\n  }\n  var placementList = Object.keys(placements);\n  return /*#__PURE__*/createPortal( /*#__PURE__*/React.createElement(React.Fragment, null, placementList.map(function (placement) {\n    var placementConfigList = placements[placement];\n    var list = /*#__PURE__*/React.createElement(NoticeList, {\n      key: placement,\n      configList: placementConfigList,\n      placement: placement,\n      prefixCls: prefixCls,\n      className: className === null || className === void 0 ? void 0 : className(placement),\n      style: style === null || style === void 0 ? void 0 : style(placement),\n      motion: motion,\n      onNoticeClose: onNoticeClose,\n      onAllNoticeRemoved: onAllNoticeRemoved,\n      stack: stack\n    });\n    return renderNotifications ? renderNotifications(list, {\n      prefixCls: prefixCls,\n      key: placement\n    }) : list;\n  })), container);\n});\nif (process.env.NODE_ENV !== 'production') {\n  Notifications.displayName = 'Notifications';\n}\nexport default Notifications;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,UAAU,MAAM,cAAc;AACrC;AACA,IAAIC,aAAa,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,mBAAmB,GAAGX,KAAK,CAACW,mBAAmB;EACjD,IAAIC,eAAe,GAAGjB,KAAK,CAACkB,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGpB,cAAc,CAACkB,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAErC;EACA,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAE;IAC9C,IAAIC,eAAe;IACnB;IACA,IAAIC,MAAM,GAAGL,UAAU,CAACM,IAAI,CAAC,UAAUC,IAAI,EAAE;MAC3C,OAAOA,IAAI,CAACJ,GAAG,KAAKA,GAAG;IACzB,CAAC,CAAC;IACFE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAI,CAACD,eAAe,GAAGC,MAAM,CAACG,OAAO,MAAM,IAAI,IAAIJ,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACK,IAAI,CAACJ,MAAM,CAAC;IACjJJ,aAAa,CAAC,UAAUS,IAAI,EAAE;MAC5B,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUJ,IAAI,EAAE;QACjC,OAAOA,IAAI,CAACJ,GAAG,KAAKA,GAAG;MACzB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;;EAED;EACAvB,KAAK,CAACgC,mBAAmB,CAAC1B,GAAG,EAAE,YAAY;IACzC,OAAO;MACL2B,IAAI,EAAE,SAASA,IAAIA,CAACR,MAAM,EAAE;QAC1BJ,aAAa,CAAC,UAAUS,IAAI,EAAE;UAC5B,IAAII,KAAK,GAAGpC,kBAAkB,CAACgC,IAAI,CAAC;;UAEpC;UACA,IAAIK,KAAK,GAAGD,KAAK,CAACE,SAAS,CAAC,UAAUT,IAAI,EAAE;YAC1C,OAAOA,IAAI,CAACJ,GAAG,KAAKE,MAAM,CAACF,GAAG;UAChC,CAAC,CAAC;UACF,IAAIc,WAAW,GAAGxC,aAAa,CAAC,CAAC,CAAC,EAAE4B,MAAM,CAAC;UAC3C,IAAIU,KAAK,IAAI,CAAC,EAAE;YACd,IAAIG,WAAW;YACfD,WAAW,CAACE,KAAK,GAAG,CAAC,CAAC,CAACD,WAAW,GAAGR,IAAI,CAACK,KAAK,CAAC,MAAM,IAAI,IAAIG,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACC,KAAK,KAAK,CAAC,IAAI,CAAC;YAC5HL,KAAK,CAACC,KAAK,CAAC,GAAGE,WAAW;UAC5B,CAAC,MAAM;YACLA,WAAW,CAACE,KAAK,GAAG,CAAC;YACrBL,KAAK,CAACM,IAAI,CAACH,WAAW,CAAC;UACzB;UACA,IAAI1B,QAAQ,GAAG,CAAC,IAAIuB,KAAK,CAACO,MAAM,GAAG9B,QAAQ,EAAE;YAC3CuB,KAAK,GAAGA,KAAK,CAACQ,KAAK,CAAC,CAAC/B,QAAQ,CAAC;UAChC;UACA,OAAOuB,KAAK;QACd,CAAC,CAAC;MACJ,CAAC;MACDS,KAAK,EAAE,SAASA,KAAKA,CAACpB,GAAG,EAAE;QACzBD,aAAa,CAACC,GAAG,CAAC;MACpB,CAAC;MACDqB,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BvB,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIwB,gBAAgB,GAAG7C,KAAK,CAACkB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvC4B,gBAAgB,GAAG/C,cAAc,CAAC8C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC9C,KAAK,CAACiD,SAAS,CAAC,YAAY;IAC1B,IAAIC,cAAc,GAAG,CAAC,CAAC;IACvB9B,UAAU,CAAC+B,OAAO,CAAC,UAAU1B,MAAM,EAAE;MACnC,IAAI2B,iBAAiB,GAAG3B,MAAM,CAAC4B,SAAS;QACtCA,SAAS,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,iBAAiB;MAC3E,IAAIC,SAAS,EAAE;QACbH,cAAc,CAACG,SAAS,CAAC,GAAGH,cAAc,CAACG,SAAS,CAAC,IAAI,EAAE;QAC3DH,cAAc,CAACG,SAAS,CAAC,CAACb,IAAI,CAACf,MAAM,CAAC;MACxC;IACF,CAAC,CAAC;;IAEF;IACA6B,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC,CAACI,OAAO,CAAC,UAAUE,SAAS,EAAE;MACnDH,cAAc,CAACG,SAAS,CAAC,GAAGH,cAAc,CAACG,SAAS,CAAC,IAAI,EAAE;IAC7D,CAAC,CAAC;IACFL,aAAa,CAACE,cAAc,CAAC;EAC/B,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIoC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACH,SAAS,EAAE;IAC9DL,aAAa,CAAC,UAAUS,gBAAgB,EAAE;MACxC,IAAIvB,KAAK,GAAGrC,aAAa,CAAC,CAAC,CAAC,EAAE4D,gBAAgB,CAAC;MAC/C,IAAI3B,IAAI,GAAGI,KAAK,CAACmB,SAAS,CAAC,IAAI,EAAE;MACjC,IAAI,CAACvB,IAAI,CAACW,MAAM,EAAE;QAChB,OAAOP,KAAK,CAACmB,SAAS,CAAC;MACzB;MACA,OAAOnB,KAAK;IACd,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIwB,QAAQ,GAAG1D,KAAK,CAAC2D,MAAM,CAAC,KAAK,CAAC;EAClC3D,KAAK,CAACiD,SAAS,CAAC,YAAY;IAC1B,IAAIK,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC,CAACN,MAAM,GAAG,CAAC,EAAE;MACtCiB,QAAQ,CAACE,OAAO,GAAG,IAAI;IACzB,CAAC,MAAM,IAAIF,QAAQ,CAACE,OAAO,EAAE;MAC3B;MACA9C,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC,CAAC;MAClE4C,QAAQ,CAACE,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;EAChB;EACA,IAAI,CAACtC,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAIoD,aAAa,GAAGP,MAAM,CAACC,IAAI,CAACR,UAAU,CAAC;EAC3C,OAAO,aAAa9C,YAAY,CAAE,aAAaD,KAAK,CAAC8D,aAAa,CAAC9D,KAAK,CAAC+D,QAAQ,EAAE,IAAI,EAAEF,aAAa,CAACG,GAAG,CAAC,UAAUX,SAAS,EAAE;IAC9H,IAAIY,mBAAmB,GAAGlB,UAAU,CAACM,SAAS,CAAC;IAC/C,IAAIvB,IAAI,GAAG,aAAa9B,KAAK,CAAC8D,aAAa,CAAC5D,UAAU,EAAE;MACtDqB,GAAG,EAAE8B,SAAS;MACdjC,UAAU,EAAE6C,mBAAmB;MAC/BZ,SAAS,EAAEA,SAAS;MACpB7C,SAAS,EAAEA,SAAS;MACpBI,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACyC,SAAS,CAAC;MACrFxC,KAAK,EAAEA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwC,SAAS,CAAC;MACrE3C,MAAM,EAAEA,MAAM;MACdY,aAAa,EAAEA,aAAa;MAC5BkC,kBAAkB,EAAEA,kBAAkB;MACtCzC,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,OAAOC,mBAAmB,GAAGA,mBAAmB,CAACc,IAAI,EAAE;MACrDtB,SAAS,EAAEA,SAAS;MACpBe,GAAG,EAAE8B;IACP,CAAC,CAAC,GAAGvB,IAAI;EACX,CAAC,CAAC,CAAC,EAAErB,SAAS,CAAC;AACjB,CAAC,CAAC;AACF,IAAIyD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjE,aAAa,CAACkE,WAAW,GAAG,eAAe;AAC7C;AACA,eAAelE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}