{"ast": null, "code": "import { merge as mergeToken } from \"./statistic\";\nfunction getDefaultComponentToken(component, token, getDefaultToken) {\n  if (typeof getDefaultToken === 'function') {\n    var _token$component;\n    return getDefaultToken(mergeToken(token, (_token$component = token[component]) !== null && _token$component !== void 0 ? _token$component : {}));\n  }\n  return getDefaultToken !== null && getDefaultToken !== void 0 ? getDefaultToken : {};\n}\nexport default getDefaultComponentToken;", "map": {"version": 3, "names": ["merge", "mergeToken", "getDefaultComponentToken", "component", "token", "getDefaultToken", "_token$component"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@ant-design/cssinjs-utils/es/util/getDefaultComponentToken.js"], "sourcesContent": ["import { merge as mergeToken } from \"./statistic\";\nfunction getDefaultComponentToken(component, token, getDefaultToken) {\n  if (typeof getDefaultToken === 'function') {\n    var _token$component;\n    return getDefaultToken(mergeToken(token, (_token$component = token[component]) !== null && _token$component !== void 0 ? _token$component : {}));\n  }\n  return getDefaultToken !== null && getDefaultToken !== void 0 ? getDefaultToken : {};\n}\nexport default getDefaultComponentToken;"], "mappings": "AAAA,SAASA,KAAK,IAAIC,UAAU,QAAQ,aAAa;AACjD,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,KAAK,EAAEC,eAAe,EAAE;EACnE,IAAI,OAAOA,eAAe,KAAK,UAAU,EAAE;IACzC,IAAIC,gBAAgB;IACpB,OAAOD,eAAe,CAACJ,UAAU,CAACG,KAAK,EAAE,CAACE,gBAAgB,GAAGF,KAAK,CAACD,SAAS,CAAC,MAAM,IAAI,IAAIG,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;EAClJ;EACA,OAAOD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,CAAC;AACtF;AACA,eAAeH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}