{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/TestChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestChart = ({\n  data,\n  title = '测试K线图'\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n    console.log('开始创建测试图表...');\n    try {\n      // 创建图表\n      const chart = createChart(chartContainerRef.current, {\n        width: 800,\n        height: 400,\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333'\n        }\n      });\n      console.log('图表创建成功:', chart);\n      console.log('图表方法列表:', Object.getOwnPropertyNames(chart));\n      console.log('图表原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(chart)));\n\n      // 尝试不同的API调用方式\n      let candlestickSeries = null;\n\n      // 方式1: 直接调用\n      if (typeof chart.addCandlestickSeries === 'function') {\n        console.log('使用 addCandlestickSeries 方法');\n        candlestickSeries = chart.addCandlestickSeries({\n          upColor: '#ef4444',\n          downColor: '#22c55e'\n        });\n      }\n      // 方式2: 检查其他可能的方法名\n      else if (typeof chart.addCandlestickChart === 'function') {\n        console.log('使用 addCandlestickChart 方法');\n        candlestickSeries = chart.addCandlestickChart({\n          upColor: '#ef4444',\n          downColor: '#22c55e'\n        });\n      }\n      // 方式3: 检查是否有其他系列添加方法\n      else {\n        console.log('查找所有可用方法:');\n        const methods = Object.getOwnPropertyNames(chart).filter(name => typeof chart[name] === 'function' && name.includes('add'));\n        console.log('包含add的方法:', methods);\n        const prototypeMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(chart)).filter(name => typeof chart[name] === 'function' && name.includes('add'));\n        console.log('原型中包含add的方法:', prototypeMethods);\n      }\n      if (candlestickSeries && data && data.length > 0) {\n        console.log('设置测试数据...');\n\n        // 创建测试数据\n        const testData = [{\n          time: '2023-01-01',\n          open: 100,\n          high: 110,\n          low: 95,\n          close: 105\n        }, {\n          time: '2023-01-02',\n          open: 105,\n          high: 115,\n          low: 100,\n          close: 108\n        }, {\n          time: '2023-01-03',\n          open: 108,\n          high: 112,\n          low: 103,\n          close: 107\n        }];\n        candlestickSeries.setData(testData);\n        console.log('测试数据设置成功');\n      }\n      return () => {\n        console.log('清理测试图表');\n        chart.remove();\n      };\n    } catch (error) {\n      console.error('测试图表创建失败:', error);\n      console.error('错误堆栈:', error.stack);\n    }\n  }, [data]);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chartContainerRef,\n      style: {\n        width: '100%',\n        height: '400px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        fontSize: '12px',\n        color: '#666'\n      },\n      children: \"\\u8BF7\\u67E5\\u770B\\u6D4F\\u89C8\\u5668\\u63A7\\u5236\\u53F0\\u83B7\\u53D6\\u8BE6\\u7EC6\\u7684API\\u8C03\\u8BD5\\u4FE1\\u606F\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(TestChart, \"ZI1LdXO604IZpmh0kAttG3FSLiA=\");\n_c = TestChart;\nexport default TestChart;\nvar _c;\n$RefreshReg$(_c, \"TestChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "createChart", "Card", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "data", "title", "_s", "chartContainerRef", "current", "console", "log", "chart", "width", "height", "layout", "backgroundColor", "textColor", "Object", "getOwnPropertyNames", "getPrototypeOf", "candlestickSeries", "addCandlestickSeries", "upColor", "downColor", "addCandlestickChart", "methods", "filter", "name", "includes", "prototypeMethods", "length", "testData", "time", "open", "high", "low", "close", "setData", "remove", "error", "stack", "children", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/TestChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card } from 'antd';\n\nconst TestChart = ({ data, title = '测试K线图' }) => {\n  const chartContainerRef = useRef();\n\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    console.log('开始创建测试图表...');\n\n    try {\n      // 创建图表\n      const chart = createChart(chartContainerRef.current, {\n        width: 800,\n        height: 400,\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333',\n        },\n      });\n\n      console.log('图表创建成功:', chart);\n      console.log('图表方法列表:', Object.getOwnPropertyNames(chart));\n      console.log('图表原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(chart)));\n\n      // 尝试不同的API调用方式\n      let candlestickSeries = null;\n\n      // 方式1: 直接调用\n      if (typeof chart.addCandlestickSeries === 'function') {\n        console.log('使用 addCandlestickSeries 方法');\n        candlestickSeries = chart.addCandlestickSeries({\n          upColor: '#ef4444',\n          downColor: '#22c55e',\n        });\n      } \n      // 方式2: 检查其他可能的方法名\n      else if (typeof chart.addCandlestickChart === 'function') {\n        console.log('使用 addCandlestickChart 方法');\n        candlestickSeries = chart.addCandlestickChart({\n          upColor: '#ef4444',\n          downColor: '#22c55e',\n        });\n      }\n      // 方式3: 检查是否有其他系列添加方法\n      else {\n        console.log('查找所有可用方法:');\n        const methods = Object.getOwnPropertyNames(chart).filter(name => \n          typeof chart[name] === 'function' && name.includes('add')\n        );\n        console.log('包含add的方法:', methods);\n        \n        const prototypeMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(chart)).filter(name => \n          typeof chart[name] === 'function' && name.includes('add')\n        );\n        console.log('原型中包含add的方法:', prototypeMethods);\n      }\n\n      if (candlestickSeries && data && data.length > 0) {\n        console.log('设置测试数据...');\n        \n        // 创建测试数据\n        const testData = [\n          { time: '2023-01-01', open: 100, high: 110, low: 95, close: 105 },\n          { time: '2023-01-02', open: 105, high: 115, low: 100, close: 108 },\n          { time: '2023-01-03', open: 108, high: 112, low: 103, close: 107 },\n        ];\n\n        candlestickSeries.setData(testData);\n        console.log('测试数据设置成功');\n      }\n\n      return () => {\n        console.log('清理测试图表');\n        chart.remove();\n      };\n\n    } catch (error) {\n      console.error('测试图表创建失败:', error);\n      console.error('错误堆栈:', error.stack);\n    }\n  }, [data]);\n\n  return (\n    <Card title={title}>\n      <div\n        ref={chartContainerRef}\n        style={{\n          width: '100%',\n          height: '400px',\n        }}\n      />\n      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>\n        请查看浏览器控制台获取详细的API调试信息\n      </div>\n    </Card>\n  );\n};\n\nexport default TestChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK,GAAG;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAMC,iBAAiB,GAAGT,MAAM,CAAC,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,iBAAiB,CAACC,OAAO,EAAE;IAEhCC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAE1B,IAAI;MACF;MACA,MAAMC,KAAK,GAAGZ,WAAW,CAACQ,iBAAiB,CAACC,OAAO,EAAE;QACnDI,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE;UACNC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MAEFP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,KAAK,CAAC;MAC7BF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEO,MAAM,CAACC,mBAAmB,CAACP,KAAK,CAAC,CAAC;MACzDF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEO,MAAM,CAACC,mBAAmB,CAACD,MAAM,CAACE,cAAc,CAACR,KAAK,CAAC,CAAC,CAAC;;MAEhF;MACA,IAAIS,iBAAiB,GAAG,IAAI;;MAE5B;MACA,IAAI,OAAOT,KAAK,CAACU,oBAAoB,KAAK,UAAU,EAAE;QACpDZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCU,iBAAiB,GAAGT,KAAK,CAACU,oBAAoB,CAAC;UAC7CC,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAI,OAAOZ,KAAK,CAACa,mBAAmB,KAAK,UAAU,EAAE;QACxDf,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCU,iBAAiB,GAAGT,KAAK,CAACa,mBAAmB,CAAC;UAC5CF,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA;MAAA,KACK;QACHd,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,MAAMe,OAAO,GAAGR,MAAM,CAACC,mBAAmB,CAACP,KAAK,CAAC,CAACe,MAAM,CAACC,IAAI,IAC3D,OAAOhB,KAAK,CAACgB,IAAI,CAAC,KAAK,UAAU,IAAIA,IAAI,CAACC,QAAQ,CAAC,KAAK,CAC1D,CAAC;QACDnB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEe,OAAO,CAAC;QAEjC,MAAMI,gBAAgB,GAAGZ,MAAM,CAACC,mBAAmB,CAACD,MAAM,CAACE,cAAc,CAACR,KAAK,CAAC,CAAC,CAACe,MAAM,CAACC,IAAI,IAC3F,OAAOhB,KAAK,CAACgB,IAAI,CAAC,KAAK,UAAU,IAAIA,IAAI,CAACC,QAAQ,CAAC,KAAK,CAC1D,CAAC;QACDnB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEmB,gBAAgB,CAAC;MAC/C;MAEA,IAAIT,iBAAiB,IAAIhB,IAAI,IAAIA,IAAI,CAAC0B,MAAM,GAAG,CAAC,EAAE;QAChDrB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;QAExB;QACA,MAAMqB,QAAQ,GAAG,CACf;UAAEC,IAAI,EAAE,YAAY;UAAEC,IAAI,EAAE,GAAG;UAAEC,IAAI,EAAE,GAAG;UAAEC,GAAG,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAC,EACjE;UAAEJ,IAAI,EAAE,YAAY;UAAEC,IAAI,EAAE,GAAG;UAAEC,IAAI,EAAE,GAAG;UAAEC,GAAG,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAI,CAAC,EAClE;UAAEJ,IAAI,EAAE,YAAY;UAAEC,IAAI,EAAE,GAAG;UAAEC,IAAI,EAAE,GAAG;UAAEC,GAAG,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAI,CAAC,CACnE;QAEDhB,iBAAiB,CAACiB,OAAO,CAACN,QAAQ,CAAC;QACnCtB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB;MAEA,OAAO,MAAM;QACXD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrBC,KAAK,CAAC2B,MAAM,CAAC,CAAC;MAChB,CAAC;IAEH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACC,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACpC,IAAI,CAAC,CAAC;EAEV,oBACEF,OAAA,CAACF,IAAI;IAACK,KAAK,EAAEA,KAAM;IAAAoC,QAAA,gBACjBvC,OAAA;MACEwC,GAAG,EAAEnC,iBAAkB;MACvBoC,KAAK,EAAE;QACL/B,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV;IAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF7C,OAAA;MAAKyC,KAAK,EAAE;QAAEK,SAAS,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAT,QAAA,EAAC;IAEpE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACzC,EAAA,CA/FIH,SAAS;AAAAgD,EAAA,GAAThD,SAAS;AAiGf,eAAeA,SAAS;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}