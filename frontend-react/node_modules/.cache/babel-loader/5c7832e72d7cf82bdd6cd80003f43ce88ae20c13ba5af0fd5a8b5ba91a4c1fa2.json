{"ast": null, "code": "import * as React from 'react';\nvar CacheContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, next) {\n  return !next.open;\n});\nif (process.env.NODE_ENV !== 'production') {\n  CacheContent.displayName = 'CacheContent';\n}\nexport default CacheContent;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo", "_ref", "children", "_", "next", "open", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-cascader/es/OptionList/CacheContent.js"], "sourcesContent": ["import * as React from 'react';\nvar CacheContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, next) {\n  return !next.open;\n});\nif (process.env.NODE_ENV !== 'production') {\n  CacheContent.displayName = 'CacheContent';\n}\nexport default CacheContent;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,IAAI,CAAC,UAAUC,IAAI,EAAE;EACzD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,CAAC,EAAEC,IAAI,EAAE;EACpB,OAAO,CAACA,IAAI,CAACC,IAAI;AACnB,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCT,YAAY,CAACU,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}