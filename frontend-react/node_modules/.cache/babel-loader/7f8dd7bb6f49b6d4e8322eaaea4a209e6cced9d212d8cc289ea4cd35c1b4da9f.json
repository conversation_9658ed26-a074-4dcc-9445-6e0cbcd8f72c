{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ProjectOutlinedSvg from \"@ant-design/icons-svg/es/asn/ProjectOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ProjectOutlined = function ProjectOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ProjectOutlinedSvg\n  }));\n};\n\n/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4MCA3NTJoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2NDY0YzAgNC40IDMuNiA4IDggOHptMTkyLTI4MGg4MGM0LjQgMCA4LTMuNiA4LThWMjgwYzAtNC40LTMuNi04LTgtOGgtODBjLTQuNCAwLTggMy42LTggOHYxODRjMCA0LjQgMy42IDggOCA4em0xOTIgNzJoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2MjU2YzAgNC40IDMuNiA4IDggOHptMjE2LTQzMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNzI4SDE4NFYxODRoNjU2djY1NnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ProjectOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ProjectOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ProjectOutlinedSvg", "AntdIcon", "ProjectOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/ProjectOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ProjectOutlinedSvg from \"@ant-design/icons-svg/es/asn/ProjectOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ProjectOutlined = function ProjectOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ProjectOutlinedSvg\n  }));\n};\n\n/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4MCA3NTJoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2NDY0YzAgNC40IDMuNiA4IDggOHptMTkyLTI4MGg4MGM0LjQgMCA4LTMuNiA4LThWMjgwYzAtNC40LTMuNi04LTgtOGgtODBjLTQuNCAwLTggMy42LTggOHYxODRjMCA0LjQgMy42IDggOCA4em0xOTIgNzJoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2MjU2YzAgNC40IDMuNiA4IDggOHptMjE2LTQzMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNzI4SDE4NFYxODRoNjU2djY1NnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ProjectOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ProjectOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}