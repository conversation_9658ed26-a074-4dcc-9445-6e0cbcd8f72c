{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Row, Col, Card, Form, Input, Button, Select, Slider, DatePicker, Table, message, Typography, Space, Tag, Statistic, List, Alert, Divider } from 'antd';\nimport { SearchOutlined, HistoryOutlined, ReloadOutlined, RocketOutlined } from '@ant-design/icons';\nimport { searchAPI, stockAPI, featureAPI } from '../services/api';\nimport LightweightChart from '../components/LightweightChart';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst SimilaritySearch = () => {\n  _s();\n  var _searchResults$query_, _searchResults$query_2, _searchResults$query_3;\n  const [form] = Form.useForm();\n  const [stockList, setStockList] = useState([]);\n  const [indexes, setIndexes] = useState([]);\n  const [searchResults, setSearchResults] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [historyLoading, setHistoryLoading] = useState(false);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    try {\n      const response = await stockAPI.getStockList();\n      if (response.data.success) {\n        setStockList(response.data.data);\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n    }\n  }, []);\n\n  // 加载索引列表\n  const loadIndexes = useCallback(async () => {\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      }\n    } catch (error) {\n      console.error('加载索引列表失败:', error);\n    }\n  }, []);\n\n  // 加载搜索历史\n  const loadSearchHistory = useCallback(async () => {\n    setHistoryLoading(true);\n    try {\n      const response = await searchAPI.getSearchHistory(10);\n      if (response.data.success) {\n        setSearchHistory(response.data.history);\n      }\n    } catch (error) {\n      console.error('加载搜索历史失败:', error);\n    } finally {\n      setHistoryLoading(false);\n    }\n  }, []);\n\n  // 执行相似性搜索\n  const handleSearch = async values => {\n    setSearchLoading(true);\n    try {\n      const data = {\n        query_stock_code: values.stockCode,\n        query_start_date: values.dateRange[0].format('YYYY-MM-DD'),\n        query_end_date: values.dateRange[1].format('YYYY-MM-DD'),\n        window_size: values.windowSize,\n        top_k: values.topK,\n        index_name: values.indexName\n      };\n      const response = await searchAPI.searchSimilar(data);\n      if (response.data.success) {\n        setSearchResults(response.data);\n        message.success(`✅ 搜索完成！找到 ${response.data.total_results} 个相似结果`);\n        loadSearchHistory(); // 刷新搜索历史\n      } else {\n        message.error('搜索失败');\n      }\n    } catch (error) {\n      console.error('搜索失败:', error);\n      message.error('搜索失败');\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadIndexes();\n    loadSearchHistory();\n  }, [loadStockList, loadIndexes, loadSearchHistory]);\n\n  // 搜索结果表格列定义\n  const resultColumns = [{\n    title: '排名',\n    key: 'rank',\n    width: 60,\n    render: (_, __, index) => index + 1\n  }, {\n    title: '股票代码',\n    dataIndex: 'stock_code',\n    key: 'stock_code',\n    render: code => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      children: code\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '匹配日期',\n    key: 'date_range',\n    render: record => /*#__PURE__*/_jsxDEV(Text, {\n      children: [dayjs(record.start_date).format('MM-DD'), \" ~ \", dayjs(record.end_date).format('MM-DD')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '相似度',\n    dataIndex: 'similarity_score',\n    key: 'similarity_score',\n    render: score => /*#__PURE__*/_jsxDEV(Tag, {\n      color: score > 0.8 ? 'green' : score > 0.6 ? 'orange' : 'blue',\n      children: [(score * 100).toFixed(2), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '距离',\n    dataIndex: 'distance',\n    key: 'distance',\n    render: distance => /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: distance.toFixed(4)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 29\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), \"\\u76F8\\u4F3C\\u6027\\u641C\\u7D22\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleSearch,\n            initialValues: {\n              windowSize: 5,\n              topK: 10,\n              dateRange: [dayjs().subtract(1, 'month'), dayjs().subtract(15, 'day')]\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stockCode\",\n              label: \"\\u67E5\\u8BE2\\u80A1\\u7968\",\n              rules: [{\n                required: true,\n                message: '请选择股票'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9\\u80A1\\u7968\",\n                showSearch: true,\n                filterOption: (input, option) => option.children.toLowerCase().includes(input.toLowerCase()),\n                children: stockList.map(stock => /*#__PURE__*/_jsxDEV(Option, {\n                  value: stock.code,\n                  children: [stock.code, \" - \", stock.name]\n                }, stock.code, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u67E5\\u8BE2\\u65E5\\u671F\\u8303\\u56F4\",\n              rules: [{\n                required: true,\n                message: '请选择日期范围'\n              }],\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"windowSize\",\n              label: \"\\u641C\\u7D22\\u7A97\\u53E3\\u5927\\u5C0F\",\n              tooltip: \"\\u5339\\u914D\\u7684K\\u7EBF\\u957F\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(Slider, {\n                min: 3,\n                max: 20,\n                marks: {\n                  3: '3天',\n                  5: '5天',\n                  10: '10天',\n                  20: '20天'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"topK\",\n              label: \"\\u8FD4\\u56DE\\u7ED3\\u679C\\u6570\\u91CF\",\n              children: /*#__PURE__*/_jsxDEV(Slider, {\n                min: 5,\n                max: 50,\n                marks: {\n                  5: '5',\n                  10: '10',\n                  20: '20',\n                  50: '50'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"indexName\",\n              label: \"\\u4F7F\\u7528\\u7D22\\u5F15\",\n              rules: [{\n                required: true,\n                message: '请选择索引'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9FAISS\\u7D22\\u5F15\",\n                children: indexes.map(index => /*#__PURE__*/_jsxDEV(Option, {\n                  value: index.index_name,\n                  children: [index.index_name, \" (\", index.total_vectors, \"\\u4E2A\\u5411\\u91CF)\"]\n                }, index.index_name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: searchLoading,\n                icon: /*#__PURE__*/_jsxDEV(RocketOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 25\n                }, this),\n                block: true,\n                size: \"large\",\n                children: \"\\u5F00\\u59CB\\u641C\\u7D22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), \"\\u641C\\u7D22\\u5386\\u53F2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 23\n            }, this),\n            onClick: loadSearchHistory,\n            loading: historyLoading,\n            size: \"small\",\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this),\n          style: {\n            marginTop: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(List, {\n            loading: historyLoading,\n            dataSource: searchHistory,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: item.query_stock_code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    children: [item.window_size, \"\\u5929\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: dayjs(item.query_time).format('MM-DD HH:mm')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [item.total_results, \" \\u4E2A\\u7ED3\\u679C\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this),\n            locale: {\n              emptyText: '暂无搜索历史'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: searchResults ? /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u67E5\\u8BE2ID\",\n                  value: (_searchResults$query_ = searchResults.query_id) === null || _searchResults$query_ === void 0 ? void 0 : _searchResults$query_.slice(0, 8)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u67E5\\u8BE2\\u80A1\\u7968\",\n                  value: (_searchResults$query_2 = searchResults.query_info) === null || _searchResults$query_2 === void 0 ? void 0 : _searchResults$query_2.stock_code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u7ED3\\u679C\\u6570\\u91CF\",\n                  value: searchResults.total_results\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u641C\\u7D22\\u65F6\\u95F4\",\n                  value: dayjs(searchResults.search_time).format('HH:mm:ss')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\uD83C\\uDFAF \\u76F8\\u4F3C\\u8D70\\u52BF\\u6392\\u884C\\u699C\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              dataSource: searchResults.results,\n              columns: resultColumns,\n              pagination: {\n                pageSize: 10,\n                showSizeChanger: false,\n                showQuickJumper: true\n              },\n              rowKey: (record, index) => `${record.stock_code}_${index}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), searchResults.query_data && /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\uD83D\\uDCCA \\u67E5\\u8BE2\\u8D70\\u52BF\\u56FE\",\n            children: /*#__PURE__*/_jsxDEV(LightweightChart, {\n              data: searchResults.query_data,\n              title: `查询走势: ${(_searchResults$query_3 = searchResults.query_info) === null || _searchResults$query_3 === void 0 ? void 0 : _searchResults$query_3.stock_code}`,\n              height: 400\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '60px 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(SearchOutlined, {\n              style: {\n                fontSize: '64px',\n                color: '#ccc',\n                marginBottom: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              type: \"secondary\",\n              children: \"\\u5F00\\u59CB\\u76F8\\u4F3C\\u6027\\u641C\\u7D22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\uD83D\\uDC48 \\u5728\\u5DE6\\u4FA7\\u8BBE\\u7F6E\\u641C\\u7D22\\u53C2\\u6570\\uFF0C\\u627E\\u5230\\u4E0E\\u6307\\u5B9A\\u80A1\\u7968\\u8D70\\u52BF\\u76F8\\u4F3C\\u7684K\\u7EBF\\u6A21\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'left',\n                maxWidth: '500px',\n                margin: '0 auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 5,\n                children: \"\\uD83D\\uDD0D \\u641C\\u7D22\\u8BF4\\u660E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  color: '#666'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u67E5\\u8BE2\\u80A1\\u7968\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 25\n                  }, this), \": \\u9009\\u62E9\\u8981\\u5206\\u6790\\u7684\\u80A1\\u7968\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u65E5\\u671F\\u8303\\u56F4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 25\n                  }, this), \": \\u6307\\u5B9AK\\u7EBF\\u7247\\u6BB5\\u7684\\u65F6\\u95F4\\u8303\\u56F4\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7A97\\u53E3\\u5927\\u5C0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 25\n                  }, this), \": \\u5339\\u914D\\u7684K\\u7EBF\\u957F\\u5EA6\\uFF08\\u5EFA\\u8BAE5-10\\u5929\\uFF09\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7ED3\\u679C\\u6570\\u91CF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 25\n                  }, this), \": \\u8FD4\\u56DE\\u6700\\u76F8\\u4F3C\\u7684N\\u4E2A\\u7ED3\\u679C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u4F7F\\u7528\\u7D22\\u5F15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 25\n                  }, this), \": \\u9009\\u62E9\\u9884\\u6784\\u5EFA\\u7684FAISS\\u7D22\\u5F15\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(SimilaritySearch, \"nzW6mhmlxstRcw8+X7e8b6aLqhE=\", false, function () {\n  return [Form.useForm];\n});\n_c = SimilaritySearch;\nexport default SimilaritySearch;\nvar _c;\n$RefreshReg$(_c, \"SimilaritySearch\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Row", "Col", "Card", "Form", "Input", "<PERSON><PERSON>", "Select", "Slide<PERSON>", "DatePicker", "Table", "message", "Typography", "Space", "Tag", "Statistic", "List", "<PERSON><PERSON>", "Divider", "SearchOutlined", "HistoryOutlined", "ReloadOutlined", "RocketOutlined", "searchAPI", "stockAPI", "featureAPI", "LightweightChart", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "RangePicker", "SimilaritySearch", "_s", "_searchResults$query_", "_searchResults$query_2", "_searchResults$query_3", "form", "useForm", "stockList", "setStockList", "indexes", "setIndexes", "searchResults", "setSearchResults", "searchHistory", "setSearchHistory", "searchLoading", "setSearchLoading", "historyLoading", "setHistoryLoading", "loadStockList", "response", "getStockList", "data", "success", "error", "console", "loadIndexes", "getIndexes", "loadSearchHistory", "getSearchHistory", "history", "handleSearch", "values", "query_stock_code", "stockCode", "query_start_date", "date<PERSON><PERSON><PERSON>", "format", "query_end_date", "window_size", "windowSize", "top_k", "topK", "index_name", "indexName", "searchSimilar", "total_results", "resultColumns", "title", "key", "width", "render", "_", "__", "index", "dataIndex", "code", "strong", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "record", "start_date", "end_date", "score", "color", "toFixed", "distance", "type", "gutter", "xs", "lg", "layout", "onFinish", "initialValues", "subtract", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "showSearch", "filterOption", "input", "option", "toLowerCase", "includes", "map", "stock", "value", "style", "tooltip", "min", "max", "marks", "total_vectors", "htmlType", "loading", "icon", "block", "size", "extra", "onClick", "marginTop", "dataSource", "renderItem", "item", "Meta", "description", "fontSize", "query_time", "locale", "emptyText", "direction", "span", "query_id", "slice", "query_info", "stock_code", "search_time", "results", "columns", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "<PERSON><PERSON><PERSON>", "query_data", "height", "textAlign", "padding", "marginBottom", "level", "max<PERSON><PERSON><PERSON>", "margin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SimilaritySearch.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Form,\n  Input,\n  Button,\n  Select,\n  Slider,\n  DatePicker,\n  Table,\n  message,\n  Typography,\n  Space,\n  Tag,\n  Statistic,\n  List,\n  Alert,\n  Divider,\n} from 'antd';\nimport {\n  SearchOutlined,\n  HistoryOutlined,\n  ReloadOutlined,\n  RocketOutlined,\n} from '@ant-design/icons';\nimport { searchAPI, stockAPI, featureAPI } from '../services/api';\nimport LightweightChart from '../components/LightweightChart';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\nconst SimilaritySearch = () => {\n  const [form] = Form.useForm();\n  const [stockList, setStockList] = useState([]);\n  const [indexes, setIndexes] = useState([]);\n  const [searchResults, setSearchResults] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [historyLoading, setHistoryLoading] = useState(false);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    try {\n      const response = await stockAPI.getStockList();\n      if (response.data.success) {\n        setStockList(response.data.data);\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n    }\n  }, []);\n\n  // 加载索引列表\n  const loadIndexes = useCallback(async () => {\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      }\n    } catch (error) {\n      console.error('加载索引列表失败:', error);\n    }\n  }, []);\n\n  // 加载搜索历史\n  const loadSearchHistory = useCallback(async () => {\n    setHistoryLoading(true);\n    try {\n      const response = await searchAPI.getSearchHistory(10);\n      if (response.data.success) {\n        setSearchHistory(response.data.history);\n      }\n    } catch (error) {\n      console.error('加载搜索历史失败:', error);\n    } finally {\n      setHistoryLoading(false);\n    }\n  }, []);\n\n  // 执行相似性搜索\n  const handleSearch = async (values) => {\n    setSearchLoading(true);\n    try {\n      const data = {\n        query_stock_code: values.stockCode,\n        query_start_date: values.dateRange[0].format('YYYY-MM-DD'),\n        query_end_date: values.dateRange[1].format('YYYY-MM-DD'),\n        window_size: values.windowSize,\n        top_k: values.topK,\n        index_name: values.indexName,\n      };\n\n      const response = await searchAPI.searchSimilar(data);\n      if (response.data.success) {\n        setSearchResults(response.data);\n        message.success(`✅ 搜索完成！找到 ${response.data.total_results} 个相似结果`);\n        loadSearchHistory(); // 刷新搜索历史\n      } else {\n        message.error('搜索失败');\n      }\n    } catch (error) {\n      console.error('搜索失败:', error);\n      message.error('搜索失败');\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadIndexes();\n    loadSearchHistory();\n  }, [loadStockList, loadIndexes, loadSearchHistory]);\n\n  // 搜索结果表格列定义\n  const resultColumns = [\n    {\n      title: '排名',\n      key: 'rank',\n      width: 60,\n      render: (_, __, index) => index + 1,\n    },\n    {\n      title: '股票代码',\n      dataIndex: 'stock_code',\n      key: 'stock_code',\n      render: (code) => <Text strong>{code}</Text>,\n    },\n    {\n      title: '匹配日期',\n      key: 'date_range',\n      render: (record) => (\n        <Text>\n          {dayjs(record.start_date).format('MM-DD')} ~ {dayjs(record.end_date).format('MM-DD')}\n        </Text>\n      ),\n    },\n    {\n      title: '相似度',\n      dataIndex: 'similarity_score',\n      key: 'similarity_score',\n      render: (score) => (\n        <Tag color={score > 0.8 ? 'green' : score > 0.6 ? 'orange' : 'blue'}>\n          {(score * 100).toFixed(2)}%\n        </Tag>\n      ),\n    },\n    {\n      title: '距离',\n      dataIndex: 'distance',\n      key: 'distance',\n      render: (distance) => <Text type=\"secondary\">{distance.toFixed(4)}</Text>,\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={[16, 16]}>\n        {/* 搜索表单 */}\n        <Col xs={24} lg={8}>\n          <Card\n            title={\n              <Space>\n                <SearchOutlined />\n                相似性搜索\n              </Space>\n            }\n          >\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleSearch}\n              initialValues={{\n                windowSize: 5,\n                topK: 10,\n                dateRange: [dayjs().subtract(1, 'month'), dayjs().subtract(15, 'day')],\n              }}\n            >\n              <Form.Item\n                name=\"stockCode\"\n                label=\"查询股票\"\n                rules={[{ required: true, message: '请选择股票' }]}\n              >\n                <Select\n                  placeholder=\"选择股票\"\n                  showSearch\n                  filterOption={(input, option) =>\n                    option.children.toLowerCase().includes(input.toLowerCase())\n                  }\n                >\n                  {stockList.map(stock => (\n                    <Option key={stock.code} value={stock.code}>\n                      {stock.code} - {stock.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Form.Item\n                name=\"dateRange\"\n                label=\"查询日期范围\"\n                rules={[{ required: true, message: '请选择日期范围' }]}\n              >\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n\n              <Form.Item\n                name=\"windowSize\"\n                label=\"搜索窗口大小\"\n                tooltip=\"匹配的K线长度\"\n              >\n                <Slider\n                  min={3}\n                  max={20}\n                  marks={{\n                    3: '3天',\n                    5: '5天',\n                    10: '10天',\n                    20: '20天',\n                  }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"topK\"\n                label=\"返回结果数量\"\n              >\n                <Slider\n                  min={5}\n                  max={50}\n                  marks={{\n                    5: '5',\n                    10: '10',\n                    20: '20',\n                    50: '50',\n                  }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"indexName\"\n                label=\"使用索引\"\n                rules={[{ required: true, message: '请选择索引' }]}\n              >\n                <Select placeholder=\"选择FAISS索引\">\n                  {indexes.map(index => (\n                    <Option key={index.index_name} value={index.index_name}>\n                      {index.index_name} ({index.total_vectors}个向量)\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={searchLoading}\n                  icon={<RocketOutlined />}\n                  block\n                  size=\"large\"\n                >\n                  开始搜索\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n\n          {/* 搜索历史 */}\n          <Card\n            title={\n              <Space>\n                <HistoryOutlined />\n                搜索历史\n              </Space>\n            }\n            extra={\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={loadSearchHistory}\n                loading={historyLoading}\n                size=\"small\"\n              >\n                刷新\n              </Button>\n            }\n            style={{ marginTop: '16px' }}\n          >\n            <List\n              loading={historyLoading}\n              dataSource={searchHistory}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        <Text strong>{item.query_stock_code}</Text>\n                        <Tag color=\"blue\">{item.window_size}天</Tag>\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {dayjs(item.query_time).format('MM-DD HH:mm')}\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {item.total_results} 个结果\n                        </Text>\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n              locale={{ emptyText: '暂无搜索历史' }}\n            />\n          </Card>\n        </Col>\n\n        {/* 搜索结果 */}\n        <Col xs={24} lg={16}>\n          {searchResults ? (\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              {/* 搜索信息 */}\n              <Card>\n                <Row gutter={16}>\n                  <Col span={6}>\n                    <Statistic title=\"查询ID\" value={searchResults.query_id?.slice(0, 8)} />\n                  </Col>\n                  <Col span={6}>\n                    <Statistic title=\"查询股票\" value={searchResults.query_info?.stock_code} />\n                  </Col>\n                  <Col span={6}>\n                    <Statistic title=\"结果数量\" value={searchResults.total_results} />\n                  </Col>\n                  <Col span={6}>\n                    <Statistic \n                      title=\"搜索时间\" \n                      value={dayjs(searchResults.search_time).format('HH:mm:ss')} \n                    />\n                  </Col>\n                </Row>\n              </Card>\n\n              {/* 搜索结果表格 */}\n              <Card title=\"🎯 相似走势排行榜\">\n                <Table\n                  dataSource={searchResults.results}\n                  columns={resultColumns}\n                  pagination={{\n                    pageSize: 10,\n                    showSizeChanger: false,\n                    showQuickJumper: true,\n                  }}\n                  rowKey={(record, index) => `${record.stock_code}_${index}`}\n                />\n              </Card>\n\n              {/* K线图对比 */}\n              {searchResults.query_data && (\n                <Card title=\"📊 查询走势图\">\n                  <LightweightChart\n                    data={searchResults.query_data}\n                    title={`查询走势: ${searchResults.query_info?.stock_code}`}\n                    height={400}\n                  />\n                </Card>\n              )}\n            </Space>\n          ) : (\n            <Card>\n              <div style={{ textAlign: 'center', padding: '60px 0' }}>\n                <SearchOutlined style={{ fontSize: '64px', color: '#ccc', marginBottom: '16px' }} />\n                <Title level={4} type=\"secondary\">开始相似性搜索</Title>\n                <Text type=\"secondary\">\n                  👈 在左侧设置搜索参数，找到与指定股票走势相似的K线模式\n                </Text>\n                \n                <Divider />\n                \n                <div style={{ textAlign: 'left', maxWidth: '500px', margin: '0 auto' }}>\n                  <Title level={5}>🔍 搜索说明</Title>\n                  <ul style={{ color: '#666' }}>\n                    <li><strong>查询股票</strong>: 选择要分析的股票</li>\n                    <li><strong>日期范围</strong>: 指定K线片段的时间范围</li>\n                    <li><strong>窗口大小</strong>: 匹配的K线长度（建议5-10天）</li>\n                    <li><strong>结果数量</strong>: 返回最相似的N个结果</li>\n                    <li><strong>使用索引</strong>: 选择预构建的FAISS索引</li>\n                  </ul>\n                </div>\n              </div>\n            </Card>\n          )}\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default SimilaritySearch;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;AACjE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGnB,UAAU;AAClC,MAAM;EAAEoB;AAAO,CAAC,GAAGzB,MAAM;AACzB,MAAM;EAAE0B;AAAY,CAAC,GAAGxB,UAAU;AAElC,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC7B,MAAM,CAACC,IAAI,CAAC,GAAGnC,IAAI,CAACoC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMuD,aAAa,GAAGrD,WAAW,CAAC,YAAY;IAC5C,IAAI;MACF,MAAMsD,QAAQ,GAAG,MAAM9B,QAAQ,CAAC+B,YAAY,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBf,YAAY,CAACY,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,WAAW,GAAG5D,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF,MAAMsD,QAAQ,GAAG,MAAM7B,UAAU,CAACoC,UAAU,CAAC,CAAC;MAC9C,IAAIP,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBb,UAAU,CAACU,QAAQ,CAACE,IAAI,CAACb,OAAO,CAAC;MACnC;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,iBAAiB,GAAG9D,WAAW,CAAC,YAAY;IAChDoD,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM/B,SAAS,CAACwC,gBAAgB,CAAC,EAAE,CAAC;MACrD,IAAIT,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBT,gBAAgB,CAACM,QAAQ,CAACE,IAAI,CAACQ,OAAO,CAAC;MACzC;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRN,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrChB,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMM,IAAI,GAAG;QACXW,gBAAgB,EAAED,MAAM,CAACE,SAAS;QAClCC,gBAAgB,EAAEH,MAAM,CAACI,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;QAC1DC,cAAc,EAAEN,MAAM,CAACI,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;QACxDE,WAAW,EAAEP,MAAM,CAACQ,UAAU;QAC9BC,KAAK,EAAET,MAAM,CAACU,IAAI;QAClBC,UAAU,EAAEX,MAAM,CAACY;MACrB,CAAC;MAED,MAAMxB,QAAQ,GAAG,MAAM/B,SAAS,CAACwD,aAAa,CAACvB,IAAI,CAAC;MACpD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBX,gBAAgB,CAACQ,QAAQ,CAACE,IAAI,CAAC;QAC/B7C,OAAO,CAAC8C,OAAO,CAAC,aAAaH,QAAQ,CAACE,IAAI,CAACwB,aAAa,QAAQ,CAAC;QACjElB,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACLnD,OAAO,CAAC+C,KAAK,CAAC,MAAM,CAAC;MACvB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B/C,OAAO,CAAC+C,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRR,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACdsD,aAAa,CAAC,CAAC;IACfO,WAAW,CAAC,CAAC;IACbE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACT,aAAa,EAAEO,WAAW,EAAEE,iBAAiB,CAAC,CAAC;;EAEnD;EACA,MAAMmB,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEC,KAAK,KAAKA,KAAK,GAAG;EACpC,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbO,SAAS,EAAE,YAAY;IACvBN,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGK,IAAI,iBAAK7D,OAAA,CAACE,IAAI;MAAC4D,MAAM;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAC7C,CAAC,EACD;IACEd,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGY,MAAM,iBACbpE,OAAA,CAACE,IAAI;MAAA6D,QAAA,GACFjE,KAAK,CAACsE,MAAM,CAACC,UAAU,CAAC,CAAC3B,MAAM,CAAC,OAAO,CAAC,EAAC,KAAG,EAAC5C,KAAK,CAACsE,MAAM,CAACE,QAAQ,CAAC,CAAC5B,MAAM,CAAC,OAAO,CAAC;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF;EAEV,CAAC,EACD;IACEd,KAAK,EAAE,KAAK;IACZO,SAAS,EAAE,kBAAkB;IAC7BN,GAAG,EAAE,kBAAkB;IACvBE,MAAM,EAAGe,KAAK,iBACZvE,OAAA,CAACf,GAAG;MAACuF,KAAK,EAAED,KAAK,GAAG,GAAG,GAAG,OAAO,GAAGA,KAAK,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAO;MAAAR,QAAA,GACjE,CAACQ,KAAK,GAAG,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5B;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAET,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXO,SAAS,EAAE,UAAU;IACrBN,GAAG,EAAE,UAAU;IACfE,MAAM,EAAGkB,QAAQ,iBAAK1E,OAAA,CAACE,IAAI;MAACyE,IAAI,EAAC,WAAW;MAAAZ,QAAA,EAAEW,QAAQ,CAACD,OAAO,CAAC,CAAC;IAAC;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAC1E,CAAC,CACF;EAED,oBACEnE,OAAA;IAAA+D,QAAA,eACE/D,OAAA,CAAC5B,GAAG;MAACwG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAb,QAAA,gBAEpB/D,OAAA,CAAC3B,GAAG;QAACwG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,gBACjB/D,OAAA,CAAC1B,IAAI;UACH+E,KAAK,eACHrD,OAAA,CAAChB,KAAK;YAAA+E,QAAA,gBACJ/D,OAAA,CAACV,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,eAED/D,OAAA,CAACzB,IAAI;YACHmC,IAAI,EAAEA,IAAK;YACXqE,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE5C,YAAa;YACvB6C,aAAa,EAAE;cACbpC,UAAU,EAAE,CAAC;cACbE,IAAI,EAAE,EAAE;cACRN,SAAS,EAAE,CAAC3C,KAAK,CAAC,CAAC,CAACoF,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAEpF,KAAK,CAAC,CAAC,CAACoF,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC;YACvE,CAAE;YAAAnB,QAAA,gBAEF/D,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzG,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiF,QAAA,eAE9C/D,OAAA,CAACtB,MAAM;gBACL8G,WAAW,EAAC,0BAAM;gBAClBC,UAAU;gBACVC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAAC7B,QAAQ,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAC3D;gBAAA9B,QAAA,EAEAnD,SAAS,CAACmF,GAAG,CAACC,KAAK,iBAClBhG,OAAA,CAACG,MAAM;kBAAkB8F,KAAK,EAAED,KAAK,CAACnC,IAAK;kBAAAE,QAAA,GACxCiC,KAAK,CAACnC,IAAI,EAAC,KAAG,EAACmC,KAAK,CAACZ,IAAI;gBAAA,GADfY,KAAK,CAACnC,IAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZnE,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAiF,QAAA,eAEhD/D,OAAA,CAACI,WAAW;gBAAC8F,KAAK,EAAE;kBAAE3C,KAAK,EAAE;gBAAO;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eAEZnE,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,sCAAQ;cACdc,OAAO,EAAC,uCAAS;cAAApC,QAAA,eAEjB/D,OAAA,CAACrB,MAAM;gBACLyH,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRC,KAAK,EAAE;kBACL,CAAC,EAAE,IAAI;kBACP,CAAC,EAAE,IAAI;kBACP,EAAE,EAAE,KAAK;kBACT,EAAE,EAAE;gBACN;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZnE,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,sCAAQ;cAAAtB,QAAA,eAEd/D,OAAA,CAACrB,MAAM;gBACLyH,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRC,KAAK,EAAE;kBACL,CAAC,EAAE,GAAG;kBACN,EAAE,EAAE,IAAI;kBACR,EAAE,EAAE,IAAI;kBACR,EAAE,EAAE;gBACN;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZnE,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzG,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAiF,QAAA,eAE9C/D,OAAA,CAACtB,MAAM;gBAAC8G,WAAW,EAAC,+BAAW;gBAAAzB,QAAA,EAC5BjD,OAAO,CAACiF,GAAG,CAACpC,KAAK,iBAChB3D,OAAA,CAACG,MAAM;kBAAwB8F,KAAK,EAAEtC,KAAK,CAACX,UAAW;kBAAAe,QAAA,GACpDJ,KAAK,CAACX,UAAU,EAAC,IAAE,EAACW,KAAK,CAAC4C,aAAa,EAAC,qBAC3C;gBAAA,GAFa5C,KAAK,CAACX,UAAU;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZnE,OAAA,CAACzB,IAAI,CAAC4G,IAAI;cAAApB,QAAA,eACR/D,OAAA,CAACvB,MAAM;gBACLkG,IAAI,EAAC,SAAS;gBACd6B,QAAQ,EAAC,QAAQ;gBACjBC,OAAO,EAAErF,aAAc;gBACvBsF,IAAI,eAAE1G,OAAA,CAACP,cAAc;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBwC,KAAK;gBACLC,IAAI,EAAC,OAAO;gBAAA7C,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPnE,OAAA,CAAC1B,IAAI;UACH+E,KAAK,eACHrD,OAAA,CAAChB,KAAK;YAAA+E,QAAA,gBACJ/D,OAAA,CAACT,eAAe;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACD0C,KAAK,eACH7G,OAAA,CAACvB,MAAM;YACLiI,IAAI,eAAE1G,OAAA,CAACR,cAAc;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB2C,OAAO,EAAE7E,iBAAkB;YAC3BwE,OAAO,EAAEnF,cAAe;YACxBsF,IAAI,EAAC,OAAO;YAAA7C,QAAA,EACb;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACD+B,KAAK,EAAE;YAAEa,SAAS,EAAE;UAAO,CAAE;UAAAhD,QAAA,eAE7B/D,OAAA,CAACb,IAAI;YACHsH,OAAO,EAAEnF,cAAe;YACxB0F,UAAU,EAAE9F,aAAc;YAC1B+F,UAAU,EAAGC,IAAI,iBACflH,OAAA,CAACb,IAAI,CAACgG,IAAI;cAAApB,QAAA,eACR/D,OAAA,CAACb,IAAI,CAACgG,IAAI,CAACgC,IAAI;gBACb9D,KAAK,eACHrD,OAAA,CAAChB,KAAK;kBAAA+E,QAAA,gBACJ/D,OAAA,CAACE,IAAI;oBAAC4D,MAAM;oBAAAC,QAAA,EAAEmD,IAAI,CAAC5E;kBAAgB;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CnE,OAAA,CAACf,GAAG;oBAACuF,KAAK,EAAC,MAAM;oBAAAT,QAAA,GAAEmD,IAAI,CAACtE,WAAW,EAAC,QAAC;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACR;gBACDiD,WAAW,eACTpH,OAAA;kBAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;oBAACyE,IAAI,EAAC,WAAW;oBAACuB,KAAK,EAAE;sBAAEmB,QAAQ,EAAE;oBAAO,CAAE;oBAAAtD,QAAA,EAChDjE,KAAK,CAACoH,IAAI,CAACI,UAAU,CAAC,CAAC5E,MAAM,CAAC,aAAa;kBAAC;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACPnE,OAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnE,OAAA,CAACE,IAAI;oBAACyE,IAAI,EAAC,WAAW;oBAACuB,KAAK,EAAE;sBAAEmB,QAAQ,EAAE;oBAAO,CAAE;oBAAAtD,QAAA,GAChDmD,IAAI,CAAC/D,aAAa,EAAC,qBACtB;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACX;YACFoD,MAAM,EAAE;cAAEC,SAAS,EAAE;YAAS;UAAE;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnE,OAAA,CAAC3B,GAAG;QAACwG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAf,QAAA,EACjB/C,aAAa,gBACZhB,OAAA,CAAChB,KAAK;UAACyI,SAAS,EAAC,UAAU;UAACvB,KAAK,EAAE;YAAE3C,KAAK,EAAE;UAAO,CAAE;UAACqD,IAAI,EAAC,OAAO;UAAA7C,QAAA,gBAEhE/D,OAAA,CAAC1B,IAAI;YAAAyF,QAAA,eACH/D,OAAA,CAAC5B,GAAG;cAACwG,MAAM,EAAE,EAAG;cAAAb,QAAA,gBACd/D,OAAA,CAAC3B,GAAG;gBAACqJ,IAAI,EAAE,CAAE;gBAAA3D,QAAA,eACX/D,OAAA,CAACd,SAAS;kBAACmE,KAAK,EAAC,gBAAM;kBAAC4C,KAAK,GAAA1F,qBAAA,GAAES,aAAa,CAAC2G,QAAQ,cAAApH,qBAAA,uBAAtBA,qBAAA,CAAwBqH,KAAK,CAAC,CAAC,EAAE,CAAC;gBAAE;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACqJ,IAAI,EAAE,CAAE;gBAAA3D,QAAA,eACX/D,OAAA,CAACd,SAAS;kBAACmE,KAAK,EAAC,0BAAM;kBAAC4C,KAAK,GAAAzF,sBAAA,GAAEQ,aAAa,CAAC6G,UAAU,cAAArH,sBAAA,uBAAxBA,sBAAA,CAA0BsH;gBAAW;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACqJ,IAAI,EAAE,CAAE;gBAAA3D,QAAA,eACX/D,OAAA,CAACd,SAAS;kBAACmE,KAAK,EAAC,0BAAM;kBAAC4C,KAAK,EAAEjF,aAAa,CAACmC;gBAAc;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNnE,OAAA,CAAC3B,GAAG;gBAACqJ,IAAI,EAAE,CAAE;gBAAA3D,QAAA,eACX/D,OAAA,CAACd,SAAS;kBACRmE,KAAK,EAAC,0BAAM;kBACZ4C,KAAK,EAAEnG,KAAK,CAACkB,aAAa,CAAC+G,WAAW,CAAC,CAACrF,MAAM,CAAC,UAAU;gBAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPnE,OAAA,CAAC1B,IAAI;YAAC+E,KAAK,EAAC,yDAAY;YAAAU,QAAA,eACtB/D,OAAA,CAACnB,KAAK;cACJmI,UAAU,EAAEhG,aAAa,CAACgH,OAAQ;cAClCC,OAAO,EAAE7E,aAAc;cACvB8E,UAAU,EAAE;gBACVC,QAAQ,EAAE,EAAE;gBACZC,eAAe,EAAE,KAAK;gBACtBC,eAAe,EAAE;cACnB,CAAE;cACFC,MAAM,EAAEA,CAAClE,MAAM,EAAET,KAAK,KAAK,GAAGS,MAAM,CAAC0D,UAAU,IAAInE,KAAK;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGNnD,aAAa,CAACuH,UAAU,iBACvBvI,OAAA,CAAC1B,IAAI;YAAC+E,KAAK,EAAC,6CAAU;YAAAU,QAAA,eACpB/D,OAAA,CAACH,gBAAgB;cACf8B,IAAI,EAAEX,aAAa,CAACuH,UAAW;cAC/BlF,KAAK,EAAE,UAAA5C,sBAAA,GAASO,aAAa,CAAC6G,UAAU,cAAApH,sBAAA,uBAAxBA,sBAAA,CAA0BqH,UAAU,EAAG;cACvDU,MAAM,EAAE;YAAI;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAERnE,OAAA,CAAC1B,IAAI;UAAAyF,QAAA,eACH/D,OAAA;YAAKkG,KAAK,EAAE;cAAEuC,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAS,CAAE;YAAA3E,QAAA,gBACrD/D,OAAA,CAACV,cAAc;cAAC4G,KAAK,EAAE;gBAAEmB,QAAQ,EAAE,MAAM;gBAAE7C,KAAK,EAAE,MAAM;gBAAEmE,YAAY,EAAE;cAAO;YAAE;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFnE,OAAA,CAACC,KAAK;cAAC2I,KAAK,EAAE,CAAE;cAACjE,IAAI,EAAC,WAAW;cAAAZ,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDnE,OAAA,CAACE,IAAI;cAACyE,IAAI,EAAC,WAAW;cAAAZ,QAAA,EAAC;YAEvB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPnE,OAAA,CAACX,OAAO;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEXnE,OAAA;cAAKkG,KAAK,EAAE;gBAAEuC,SAAS,EAAE,MAAM;gBAAEI,QAAQ,EAAE,OAAO;gBAAEC,MAAM,EAAE;cAAS,CAAE;cAAA/E,QAAA,gBACrE/D,OAAA,CAACC,KAAK;gBAAC2I,KAAK,EAAE,CAAE;gBAAA7E,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCnE,OAAA;gBAAIkG,KAAK,EAAE;kBAAE1B,KAAK,EAAE;gBAAO,CAAE;gBAAAT,QAAA,gBAC3B/D,OAAA;kBAAA+D,QAAA,gBAAI/D,OAAA;oBAAA+D,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,sDAAU;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCnE,OAAA;kBAAA+D,QAAA,gBAAI/D,OAAA;oBAAA+D,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,mEAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3CnE,OAAA;kBAAA+D,QAAA,gBAAI/D,OAAA;oBAAA+D,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,6EAAkB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChDnE,OAAA;kBAAA+D,QAAA,gBAAI/D,OAAA;oBAAA+D,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,6DAAY;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1CnE,OAAA;kBAAA+D,QAAA,gBAAI/D,OAAA;oBAAA+D,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,2DAAe;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA/WID,gBAAgB;EAAA,QACL9B,IAAI,CAACoC,OAAO;AAAA;AAAAoI,EAAA,GADvB1I,gBAAgB;AAiXtB,eAAeA,gBAAgB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}