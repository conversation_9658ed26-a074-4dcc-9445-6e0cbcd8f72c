{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport NotificationOutlinedSvg from \"@ant-design/icons-svg/es/asn/NotificationOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar NotificationOutlined = function NotificationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: NotificationOutlinedSvg\n  }));\n};\n\n/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJjLTMuOCAwLTcuNy43LTExLjYgMi4zTDI5MiAzNDUuOUgxMjhjLTguOCAwLTE2IDcuNC0xNiAxNi42djI5OWMwIDkuMiA3LjIgMTYuNiAxNiAxNi42aDEwMS43Yy0zLjcgMTEuNi01LjcgMjMuOS01LjcgMzYuNCAwIDY1LjkgNTMuOCAxMTkuNSAxMjAgMTE5LjUgNTUuNCAwIDEwMi4xLTM3LjYgMTE1LjktODguNGw0MDguNiAxNjQuMmMzLjkgMS41IDcuOCAyLjMgMTEuNiAyLjMgMTYuOSAwIDMyLTE0LjIgMzItMzMuMlYxNDUuMkM5MTIgMTI2LjIgODk3IDExMiA4ODAgMTEyek0zNDQgNzYyLjNjLTI2LjUgMC00OC0yMS40LTQ4LTQ3LjggMC0xMS4yIDMuOS0yMS45IDExLTMwLjRsODQuOSAzNC4xYy0yIDI0LjYtMjIuNyA0NC4xLTQ3LjkgNDQuMXptNDk2IDU4LjRMMzE4LjggNjExLjNsLTEyLjktNS4ySDE4NFY0MTcuOWgxMjEuOWwxMi45LTUuMkw4NDAgMjAzLjN2NjE3LjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(NotificationOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'NotificationOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "NotificationOutlinedSvg", "AntdIcon", "NotificationOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/NotificationOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport NotificationOutlinedSvg from \"@ant-design/icons-svg/es/asn/NotificationOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar NotificationOutlined = function NotificationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: NotificationOutlinedSvg\n  }));\n};\n\n/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJjLTMuOCAwLTcuNy43LTExLjYgMi4zTDI5MiAzNDUuOUgxMjhjLTguOCAwLTE2IDcuNC0xNiAxNi42djI5OWMwIDkuMiA3LjIgMTYuNiAxNiAxNi42aDEwMS43Yy0zLjcgMTEuNi01LjcgMjMuOS01LjcgMzYuNCAwIDY1LjkgNTMuOCAxMTkuNSAxMjAgMTE5LjUgNTUuNCAwIDEwMi4xLTM3LjYgMTE1LjktODguNGw0MDguNiAxNjQuMmMzLjkgMS41IDcuOCAyLjMgMTEuNiAyLjMgMTYuOSAwIDMyLTE0LjIgMzItMzMuMlYxNDUuMkM5MTIgMTI2LjIgODk3IDExMiA4ODAgMTEyek0zNDQgNzYyLjNjLTI2LjUgMC00OC0yMS40LTQ4LTQ3LjggMC0xMS4yIDMuOS0yMS45IDExLTMwLjRsODQuOSAzNC4xYy0yIDI0LjYtMjIuNyA0NC4xLTQ3LjkgNDQuMXptNDk2IDU4LjRMMzE4LjggNjExLjNsLTEyLjktNS4ySDE4NFY0MTcuOWgxMjEuOWwxMi45LTUuMkw4NDAgMjAzLjN2NjE3LjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(NotificationOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'NotificationOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,uBAAuB,MAAM,mDAAmD;AACvF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,oBAAoB,CAAC;AACjE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,sBAAsB;AAC9C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}