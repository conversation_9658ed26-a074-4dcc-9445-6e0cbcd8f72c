{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SolutionOutlinedSvg from \"@ant-design/icons-svg/es/asn/SolutionOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SolutionOutlined = function SolutionOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SolutionOutlinedSvg\n  }));\n};\n\n/**![solution](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OCAyNjRjMC00LjQtMy42LTgtOC04SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OHptLTggMTM2SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6TTQ4MCA1NDRIMjk2Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDE4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptLTQ4IDMwOEgyMDhWMTQ4aDU2MHYzNDRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYxMDhjMC0xNy43LTE0LjMtMzItMzItMzJIMTY4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3ODRjMCAxNy43IDE0LjMgMzIgMzIgMzJoMjY0YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0zNTYuOC03NC40YzI5LTI2LjMgNDcuMi02NC4zIDQ3LjItMTA2LjYgMC03OS41LTY0LjUtMTQ0LTE0NC0xNDRzLTE0NCA2NC41LTE0NCAxNDRjMCA0Mi4zIDE4LjIgODAuMyA0Ny4yIDEwNi42LTU3IDMyLjUtOTYuMiA5Mi43LTk5LjIgMTYyLjEtLjIgNC41IDMuNSA4LjMgOCA4LjNoNDguMWM0LjIgMCA3LjctMy4zIDgtNy42QzU2NCA4NzEuMiA2MjEuNyA4MTYgNjkyIDgxNnMxMjggNTUuMiAxMzEuOSAxMjQuNGMuMiA0LjIgMy43IDcuNiA4IDcuNkg4ODBjNC42IDAgOC4yLTMuOCA4LTguMy0yLjktNjkuNS00Mi4yLTEyOS42LTk5LjItMTYyLjF6TTY5MiA1OTFjNDQuMiAwIDgwIDM1LjggODAgODBzLTM1LjggODAtODAgODAtODAtMzUuOC04MC04MCAzNS44LTgwIDgwLTgweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SolutionOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SolutionOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SolutionOutlinedSvg", "AntdIcon", "SolutionOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/SolutionOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SolutionOutlinedSvg from \"@ant-design/icons-svg/es/asn/SolutionOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SolutionOutlined = function SolutionOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SolutionOutlinedSvg\n  }));\n};\n\n/**![solution](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OCAyNjRjMC00LjQtMy42LTgtOC04SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OHptLTggMTM2SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6TTQ4MCA1NDRIMjk2Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDE4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptLTQ4IDMwOEgyMDhWMTQ4aDU2MHYzNDRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYxMDhjMC0xNy43LTE0LjMtMzItMzItMzJIMTY4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3ODRjMCAxNy43IDE0LjMgMzIgMzIgMzJoMjY0YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0zNTYuOC03NC40YzI5LTI2LjMgNDcuMi02NC4zIDQ3LjItMTA2LjYgMC03OS41LTY0LjUtMTQ0LTE0NC0xNDRzLTE0NCA2NC41LTE0NCAxNDRjMCA0Mi4zIDE4LjIgODAuMyA0Ny4yIDEwNi42LTU3IDMyLjUtOTYuMiA5Mi43LTk5LjIgMTYyLjEtLjIgNC41IDMuNSA4LjMgOCA4LjNoNDguMWM0LjIgMCA3LjctMy4zIDgtNy42QzU2NCA4NzEuMiA2MjEuNyA4MTYgNjkyIDgxNnMxMjggNTUuMiAxMzEuOSAxMjQuNGMuMiA0LjIgMy43IDcuNiA4IDcuNkg4ODBjNC42IDAgOC4yLTMuOCA4LTguMy0yLjktNjkuNS00Mi4yLTEyOS42LTk5LjItMTYyLjF6TTY5MiA1OTFjNDQuMiAwIDgwIDM1LjggODAgODBzLTM1LjggODAtODAgODAtODAtMzUuOC04MC04MCAzNS44LTgwIDgwLTgweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SolutionOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SolutionOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}