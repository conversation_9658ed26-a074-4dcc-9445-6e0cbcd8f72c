{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StableChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chartInstanceRef = useRef(null);\n  const [chartError, setChartError] = useState(null);\n\n  // 清理图表的安全方法\n  const safeRemoveChart = () => {\n    if (chartInstanceRef.current) {\n      try {\n        chartInstanceRef.current.remove();\n      } catch (error) {\n        console.warn('清理图表时出现警告:', error.message);\n      } finally {\n        chartInstanceRef.current = null;\n      }\n    }\n  };\n\n  // 创建图表\n  const createChartInstance = () => {\n    if (!chartContainerRef.current) return null;\n    try {\n      const chart = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333'\n        },\n        grid: {\n          vertLines: {\n            color: '#f0f0f0'\n          },\n          horzLines: {\n            color: '#f0f0f0'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc'\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100\n      });\n      return chart;\n    } catch (error) {\n      console.error('创建图表失败:', error);\n      setChartError(error.message);\n      return null;\n    }\n  };\n\n  // 处理数据更新\n  useEffect(() => {\n    if (loading || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    // 清理现有图表\n    safeRemoveChart();\n    setChartError(null);\n\n    // 创建新图表\n    const chart = createChartInstance();\n    if (!chart) return;\n    chartInstanceRef.current = chart;\n    try {\n      // 创建K线系列 - 使用新版本API\n      const candlestickSeries = chart.addSeries('Candlestick', {\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444'\n      });\n\n      // 处理数据\n      const candlestickData = [];\n      data.forEach(item => {\n        try {\n          const dateStr = item.date || item.日期;\n          if (!dateStr) return;\n          const date = new Date(dateStr);\n          if (isNaN(date.getTime())) return;\n          const timeValue = Math.floor(date.getTime() / 1000);\n          const open = parseFloat(item.open || item.开盘);\n          const high = parseFloat(item.high || item.最高);\n          const low = parseFloat(item.low || item.最低);\n          const close = parseFloat(item.close || item.收盘);\n          if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n            candlestickData.push({\n              time: timeValue,\n              open: open,\n              high: high,\n              low: low,\n              close: close\n            });\n          }\n        } catch (error) {\n          console.warn('处理数据项失败:', error);\n        }\n      });\n\n      // 排序并设置数据\n      if (candlestickData.length > 0) {\n        candlestickData.sort((a, b) => a.time - b.time);\n        candlestickSeries.setData(candlestickData);\n\n        // 延迟自适应视图\n        setTimeout(() => {\n          if (chartInstanceRef.current) {\n            try {\n              chart.timeScale().fitContent();\n            } catch (error) {\n              console.warn('自适应视图失败:', error);\n            }\n          }\n        }, 200);\n      }\n\n      // 窗口大小变化处理\n      const handleResize = () => {\n        if (chartInstanceRef.current && chartContainerRef.current) {\n          try {\n            chart.applyOptions({\n              width: chartContainerRef.current.clientWidth\n            });\n          } catch (error) {\n            console.warn('调整图表大小失败:', error);\n          }\n        }\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 返回清理函数\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        safeRemoveChart();\n      };\n    } catch (error) {\n      console.error('设置图表数据失败:', error);\n      setChartError(error.message);\n      safeRemoveChart();\n    }\n  }, [data, height, loading]);\n\n  // 组件卸载时清理\n  useEffect(() => {\n    return () => {\n      safeRemoveChart();\n    };\n  }, []);\n  if (chartError) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      style: {\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#ff4d4f',\n          fontSize: '16px'\n        },\n        children: [\"\\u56FE\\u8868\\u52A0\\u8F7D\\u5931\\u8D25: \", chartError]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(StableChart, \"Vx+I8poHwiIUanCo+9QSlXoqV/4=\");\n_c = StableChart;\nexport default StableChart;\nvar _c;\n$RefreshReg$(_c, \"StableChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "createChart", "Card", "Spin", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "data", "title", "height", "loading", "_s", "chartContainerRef", "chartInstanceRef", "chartError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "safeRemoveChart", "current", "remove", "error", "console", "warn", "message", "createChartInstance", "chart", "layout", "backgroundColor", "textColor", "grid", "vertLines", "color", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "Array", "isArray", "length", "candlestickSeries", "addSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "candlestickData", "for<PERSON>ach", "item", "dateStr", "date", "日期", "Date", "isNaN", "getTime", "timeValue", "Math", "floor", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "push", "time", "sort", "a", "b", "setData", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "handleResize", "applyOptions", "window", "addEventListener", "removeEventListener", "style", "children", "display", "alignItems", "justifyContent", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bodyStyle", "padding", "spinning", "tip", "ref", "position", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/StableChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin } from 'antd';\n\nconst StableChart = ({ \n  data, \n  title = 'K线图', \n  height = 500,\n  loading = false \n}) => {\n  const chartContainerRef = useRef();\n  const chartInstanceRef = useRef(null);\n  const [chartError, setChartError] = useState(null);\n\n  // 清理图表的安全方法\n  const safeRemoveChart = () => {\n    if (chartInstanceRef.current) {\n      try {\n        chartInstanceRef.current.remove();\n      } catch (error) {\n        console.warn('清理图表时出现警告:', error.message);\n      } finally {\n        chartInstanceRef.current = null;\n      }\n    }\n  };\n\n  // 创建图表\n  const createChartInstance = () => {\n    if (!chartContainerRef.current) return null;\n\n    try {\n      const chart = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333',\n        },\n        grid: {\n          vertLines: { color: '#f0f0f0' },\n          horzLines: { color: '#f0f0f0' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc',\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100,\n      });\n\n      return chart;\n    } catch (error) {\n      console.error('创建图表失败:', error);\n      setChartError(error.message);\n      return null;\n    }\n  };\n\n  // 处理数据更新\n  useEffect(() => {\n    if (loading || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    // 清理现有图表\n    safeRemoveChart();\n    setChartError(null);\n\n    // 创建新图表\n    const chart = createChartInstance();\n    if (!chart) return;\n\n    chartInstanceRef.current = chart;\n\n    try {\n      // 创建K线系列 - 使用新版本API\n      const candlestickSeries = chart.addSeries('Candlestick', {\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444',\n      });\n\n      // 处理数据\n      const candlestickData = [];\n      \n      data.forEach(item => {\n        try {\n          const dateStr = item.date || item.日期;\n          if (!dateStr) return;\n\n          const date = new Date(dateStr);\n          if (isNaN(date.getTime())) return;\n\n          const timeValue = Math.floor(date.getTime() / 1000);\n          const open = parseFloat(item.open || item.开盘);\n          const high = parseFloat(item.high || item.最高);\n          const low = parseFloat(item.low || item.最低);\n          const close = parseFloat(item.close || item.收盘);\n\n          if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n            candlestickData.push({\n              time: timeValue,\n              open: open,\n              high: high,\n              low: low,\n              close: close,\n            });\n          }\n        } catch (error) {\n          console.warn('处理数据项失败:', error);\n        }\n      });\n\n      // 排序并设置数据\n      if (candlestickData.length > 0) {\n        candlestickData.sort((a, b) => a.time - b.time);\n        candlestickSeries.setData(candlestickData);\n        \n        // 延迟自适应视图\n        setTimeout(() => {\n          if (chartInstanceRef.current) {\n            try {\n              chart.timeScale().fitContent();\n            } catch (error) {\n              console.warn('自适应视图失败:', error);\n            }\n          }\n        }, 200);\n      }\n\n      // 窗口大小变化处理\n      const handleResize = () => {\n        if (chartInstanceRef.current && chartContainerRef.current) {\n          try {\n            chart.applyOptions({\n              width: chartContainerRef.current.clientWidth,\n            });\n          } catch (error) {\n            console.warn('调整图表大小失败:', error);\n          }\n        }\n      };\n\n      window.addEventListener('resize', handleResize);\n\n      // 返回清理函数\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        safeRemoveChart();\n      };\n\n    } catch (error) {\n      console.error('设置图表数据失败:', error);\n      setChartError(error.message);\n      safeRemoveChart();\n    }\n  }, [data, height, loading]);\n\n  // 组件卸载时清理\n  useEffect(() => {\n    return () => {\n      safeRemoveChart();\n    };\n  }, []);\n\n  if (chartError) {\n    return (\n      <Card title={title} style={{ width: '100%' }}>\n        <div\n          style={{\n            height: height - 100,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            color: '#ff4d4f',\n            fontSize: '16px',\n          }}\n        >\n          图表加载失败: {chartError}\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default StableChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGb,MAAM,CAAC,CAAC;EAClC,MAAMc,gBAAgB,GAAGd,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIH,gBAAgB,CAACI,OAAO,EAAE;MAC5B,IAAI;QACFJ,gBAAgB,CAACI,OAAO,CAACC,MAAM,CAAC,CAAC;MACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEF,KAAK,CAACG,OAAO,CAAC;MAC3C,CAAC,SAAS;QACRT,gBAAgB,CAACI,OAAO,GAAG,IAAI;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACX,iBAAiB,CAACK,OAAO,EAAE,OAAO,IAAI;IAE3C,IAAI;MACF,MAAMO,KAAK,GAAGvB,WAAW,CAACW,iBAAiB,CAACK,OAAO,EAAE;QACnDQ,MAAM,EAAE;UACNC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAC;UAC/BC,SAAS,EAAE;YAAED,KAAK,EAAE;UAAU;QAChC,CAAC;QACDE,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB,CAAC;QACDC,KAAK,EAAE3B,iBAAiB,CAACK,OAAO,CAACuB,WAAW;QAC5C/B,MAAM,EAAEA,MAAM,GAAG;MACnB,CAAC,CAAC;MAEF,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BJ,aAAa,CAACI,KAAK,CAACG,OAAO,CAAC;MAC5B,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACAxB,SAAS,CAAC,MAAM;IACd,IAAIY,OAAO,IAAI,CAACH,IAAI,IAAI,CAACkC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,IAAIA,IAAI,CAACoC,MAAM,KAAK,CAAC,EAAE;MACjE;IACF;;IAEA;IACA3B,eAAe,CAAC,CAAC;IACjBD,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMS,KAAK,GAAGD,mBAAmB,CAAC,CAAC;IACnC,IAAI,CAACC,KAAK,EAAE;IAEZX,gBAAgB,CAACI,OAAO,GAAGO,KAAK;IAEhC,IAAI;MACF;MACA,MAAMoB,iBAAiB,GAAGpB,KAAK,CAACqB,SAAS,CAAC,aAAa,EAAE;QACvDC,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,eAAe,EAAE,SAAS;QAC1BC,aAAa,EAAE,SAAS;QACxBC,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,eAAe,GAAG,EAAE;MAE1B7C,IAAI,CAAC8C,OAAO,CAACC,IAAI,IAAI;QACnB,IAAI;UACF,MAAMC,OAAO,GAAGD,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,EAAE;UACpC,IAAI,CAACF,OAAO,EAAE;UAEd,MAAMC,IAAI,GAAG,IAAIE,IAAI,CAACH,OAAO,CAAC;UAC9B,IAAII,KAAK,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,EAAE;UAE3B,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACP,IAAI,CAACI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACnD,MAAMI,IAAI,GAAGC,UAAU,CAACX,IAAI,CAACU,IAAI,IAAIV,IAAI,CAACY,EAAE,CAAC;UAC7C,MAAMC,IAAI,GAAGF,UAAU,CAACX,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACc,EAAE,CAAC;UAC7C,MAAMC,GAAG,GAAGJ,UAAU,CAACX,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACgB,EAAE,CAAC;UAC3C,MAAMC,KAAK,GAAGN,UAAU,CAACX,IAAI,CAACiB,KAAK,IAAIjB,IAAI,CAACkB,EAAE,CAAC;UAE/C,IAAI,CAACb,KAAK,CAACK,IAAI,CAAC,IAAI,CAACL,KAAK,CAACQ,IAAI,CAAC,IAAI,CAACR,KAAK,CAACU,GAAG,CAAC,IAAI,CAACV,KAAK,CAACY,KAAK,CAAC,EAAE;YAChEnB,eAAe,CAACqB,IAAI,CAAC;cACnBC,IAAI,EAAEb,SAAS;cACfG,IAAI,EAAEA,IAAI;cACVG,IAAI,EAAEA,IAAI;cACVE,GAAG,EAAEA,GAAG;cACRE,KAAK,EAAEA;YACT,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,UAAU,EAAEF,KAAK,CAAC;QACjC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIiC,eAAe,CAACT,MAAM,GAAG,CAAC,EAAE;QAC9BS,eAAe,CAACuB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,IAAI,GAAGG,CAAC,CAACH,IAAI,CAAC;QAC/C9B,iBAAiB,CAACkC,OAAO,CAAC1B,eAAe,CAAC;;QAE1C;QACA2B,UAAU,CAAC,MAAM;UACf,IAAIlE,gBAAgB,CAACI,OAAO,EAAE;YAC5B,IAAI;cACFO,KAAK,CAACY,SAAS,CAAC,CAAC,CAAC4C,UAAU,CAAC,CAAC;YAChC,CAAC,CAAC,OAAO7D,KAAK,EAAE;cACdC,OAAO,CAACC,IAAI,CAAC,UAAU,EAAEF,KAAK,CAAC;YACjC;UACF;QACF,CAAC,EAAE,GAAG,CAAC;MACT;;MAEA;MACA,MAAM8D,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAIpE,gBAAgB,CAACI,OAAO,IAAIL,iBAAiB,CAACK,OAAO,EAAE;UACzD,IAAI;YACFO,KAAK,CAAC0D,YAAY,CAAC;cACjB3C,KAAK,EAAE3B,iBAAiB,CAACK,OAAO,CAACuB;YACnC,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOrB,KAAK,EAAE;YACdC,OAAO,CAACC,IAAI,CAAC,WAAW,EAAEF,KAAK,CAAC;UAClC;QACF;MACF,CAAC;MAEDgE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;QAClDjE,eAAe,CAAC,CAAC;MACnB,CAAC;IAEH,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCJ,aAAa,CAACI,KAAK,CAACG,OAAO,CAAC;MAC5BN,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,IAAI,EAAEE,MAAM,EAAEC,OAAO,CAAC,CAAC;;EAE3B;EACAZ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXkB,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,UAAU,EAAE;IACd,oBACET,OAAA,CAACH,IAAI;MAACM,KAAK,EAAEA,KAAM;MAAC8E,KAAK,EAAE;QAAE/C,KAAK,EAAE;MAAO,CAAE;MAAAgD,QAAA,eAC3ClF,OAAA;QACEiF,KAAK,EAAE;UACL7E,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB+E,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB5D,KAAK,EAAE,SAAS;UAChB6D,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,GACH,wCACS,EAACzE,UAAU;MAAA;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE1F,OAAA,CAACH,IAAI;IACHM,KAAK,EAAEA,KAAM;IACb8E,KAAK,EAAE;MAAE/C,KAAK,EAAE;IAAO,CAAE;IACzByD,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,eAE/BlF,OAAA,CAACF,IAAI;MAAC+F,QAAQ,EAAExF,OAAQ;MAACyF,GAAG,EAAC,yCAAW;MAAAZ,QAAA,gBACtClF,OAAA;QACE+F,GAAG,EAAExF,iBAAkB;QACvB0E,KAAK,EAAE;UACL/C,KAAK,EAAE,MAAM;UACb9B,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB4F,QAAQ,EAAE;QACZ;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACrF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACoC,MAAM,KAAK,CAAC,CAAC,iBACvCtC,OAAA;QACEiF,KAAK,EAAE;UACL7E,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB+E,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB5D,KAAK,EAAE,MAAM;UACb6D,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACpF,EAAA,CA7NIL,WAAW;AAAAgG,EAAA,GAAXhG,WAAW;AA+NjB,eAAeA,WAAW;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}