{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SlackOutlinedSvg from \"@ant-design/icons-svg/es/asn/SlackOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SlackOutlined = function SlackOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SlackOutlinedSvg\n  }));\n};\n\n/**![slack](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQwOS40IDEyOGMtNDIuNCAwLTc2LjcgMzQuNC03Ni43IDc2LjggMCAyMC4zIDguMSAzOS45IDIyLjQgNTQuM2E3Ni43NCA3Ni43NCAwIDAwNTQuMyAyMi41aDc2Ljd2LTc2LjhjMC00Mi4zLTM0LjMtNzYuNy03Ni43LTc2Ljh6bTAgMjA0LjhIMjA0LjdjLTQyLjQgMC03Ni43IDM0LjQtNzYuNyA3Ni44czM0LjQgNzYuOCA3Ni43IDc2LjhoMjA0LjZjNDIuNCAwIDc2LjctMzQuNCA3Ni43LTc2LjguMS00Mi40LTM0LjMtNzYuOC03Ni42LTc2Ljh6TTYxNCA0ODYuNGM0Mi40IDAgNzYuOC0zNC40IDc2LjctNzYuOFYyMDQuOGMwLTQyLjQtMzQuMy03Ni44LTc2LjctNzYuOC00Mi40IDAtNzYuNyAzNC40LTc2LjcgNzYuOHYyMDQuOGMwIDQyLjUgMzQuMyA3Ni44IDc2LjcgNzYuOHptMjgxLjQtNzYuOGMwLTQyLjQtMzQuNC03Ni44LTc2LjctNzYuOFM3NDIgMzY3LjIgNzQyIDQwOS42djc2LjhoNzYuN2M0Mi4zIDAgNzYuNy0zNC40IDc2LjctNzYuOHptLTc2LjggMTI4SDYxNGMtNDIuNCAwLTc2LjcgMzQuNC03Ni43IDc2LjggMCAyMC4zIDguMSAzOS45IDIyLjQgNTQuM2E3Ni43NCA3Ni43NCAwIDAwNTQuMyAyMi41aDIwNC42YzQyLjQgMCA3Ni43LTM0LjQgNzYuNy03Ni44LjEtNDIuNC0zNC4zLTc2LjctNzYuNy03Ni44ek02MTQgNzQyLjRoLTc2Ljd2NzYuOGMwIDQyLjQgMzQuNCA3Ni44IDc2LjcgNzYuOCA0Mi40IDAgNzYuOC0zNC40IDc2LjctNzYuOC4xLTQyLjQtMzQuMy03Ni43LTc2LjctNzYuOHpNNDA5LjQgNTM3LjZjLTQyLjQgMC03Ni43IDM0LjQtNzYuNyA3Ni44djIwNC44YzAgNDIuNCAzNC40IDc2LjggNzYuNyA3Ni44IDQyLjQgMCA3Ni44LTM0LjQgNzYuNy03Ni44VjYxNC40YzAtMjAuMy04LjEtMzkuOS0yMi40LTU0LjNhNzYuOTIgNzYuOTIgMCAwMC01NC4zLTIyLjV6TTEyOCA2MTQuNGMwIDIwLjMgOC4xIDM5LjkgMjIuNCA1NC4zYTc2Ljc0IDc2Ljc0IDAgMDA1NC4zIDIyLjVjNDIuNCAwIDc2LjgtMzQuNCA3Ni43LTc2Ljh2LTc2LjhoLTc2LjdjLTQyLjMgMC03Ni43IDM0LjQtNzYuNyA3Ni44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SlackOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SlackOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SlackOutlinedSvg", "AntdIcon", "Slack<PERSON>utlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/SlackOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SlackOutlinedSvg from \"@ant-design/icons-svg/es/asn/SlackOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SlackOutlined = function SlackOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SlackOutlinedSvg\n  }));\n};\n\n/**![slack](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQwOS40IDEyOGMtNDIuNCAwLTc2LjcgMzQuNC03Ni43IDc2LjggMCAyMC4zIDguMSAzOS45IDIyLjQgNTQuM2E3Ni43NCA3Ni43NCAwIDAwNTQuMyAyMi41aDc2Ljd2LTc2LjhjMC00Mi4zLTM0LjMtNzYuNy03Ni43LTc2Ljh6bTAgMjA0LjhIMjA0LjdjLTQyLjQgMC03Ni43IDM0LjQtNzYuNyA3Ni44czM0LjQgNzYuOCA3Ni43IDc2LjhoMjA0LjZjNDIuNCAwIDc2LjctMzQuNCA3Ni43LTc2LjguMS00Mi40LTM0LjMtNzYuOC03Ni42LTc2Ljh6TTYxNCA0ODYuNGM0Mi40IDAgNzYuOC0zNC40IDc2LjctNzYuOFYyMDQuOGMwLTQyLjQtMzQuMy03Ni44LTc2LjctNzYuOC00Mi40IDAtNzYuNyAzNC40LTc2LjcgNzYuOHYyMDQuOGMwIDQyLjUgMzQuMyA3Ni44IDc2LjcgNzYuOHptMjgxLjQtNzYuOGMwLTQyLjQtMzQuNC03Ni44LTc2LjctNzYuOFM3NDIgMzY3LjIgNzQyIDQwOS42djc2LjhoNzYuN2M0Mi4zIDAgNzYuNy0zNC40IDc2LjctNzYuOHptLTc2LjggMTI4SDYxNGMtNDIuNCAwLTc2LjcgMzQuNC03Ni43IDc2LjggMCAyMC4zIDguMSAzOS45IDIyLjQgNTQuM2E3Ni43NCA3Ni43NCAwIDAwNTQuMyAyMi41aDIwNC42YzQyLjQgMCA3Ni43LTM0LjQgNzYuNy03Ni44LjEtNDIuNC0zNC4zLTc2LjctNzYuNy03Ni44ek02MTQgNzQyLjRoLTc2Ljd2NzYuOGMwIDQyLjQgMzQuNCA3Ni44IDc2LjcgNzYuOCA0Mi40IDAgNzYuOC0zNC40IDc2LjctNzYuOC4xLTQyLjQtMzQuMy03Ni43LTc2LjctNzYuOHpNNDA5LjQgNTM3LjZjLTQyLjQgMC03Ni43IDM0LjQtNzYuNyA3Ni44djIwNC44YzAgNDIuNCAzNC40IDc2LjggNzYuNyA3Ni44IDQyLjQgMCA3Ni44LTM0LjQgNzYuNy03Ni44VjYxNC40YzAtMjAuMy04LjEtMzkuOS0yMi40LTU0LjNhNzYuOTIgNzYuOTIgMCAwMC01NC4zLTIyLjV6TTEyOCA2MTQuNGMwIDIwLjMgOC4xIDM5LjkgMjIuNCA1NC4zYTc2Ljc0IDc2Ljc0IDAgMDA1NC4zIDIyLjVjNDIuNCAwIDc2LjgtMzQuNCA3Ni43LTc2Ljh2LTc2LjhoLTc2LjdjLTQyLjMgMC03Ni43IDM0LjQtNzYuNyA3Ni44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SlackOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SlackOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}