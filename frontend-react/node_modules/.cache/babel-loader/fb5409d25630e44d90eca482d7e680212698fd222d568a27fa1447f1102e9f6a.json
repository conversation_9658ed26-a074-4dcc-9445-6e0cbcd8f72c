{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [chartReady, setChartReady] = useState(false);\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 创建图表\n    chart.current = createChart(chartContainerRef.current, {\n      layout: {\n        backgroundColor: '#ffffff',\n        textColor: '#333'\n      },\n      grid: {\n        vertLines: {\n          color: '#f0f0f0'\n        },\n        horzLines: {\n          color: '#f0f0f0'\n        }\n      },\n      crosshair: {\n        mode: 1 // Normal crosshair mode\n      },\n      rightPriceScale: {\n        borderColor: '#cccccc'\n      },\n      timeScale: {\n        borderColor: '#cccccc',\n        timeVisible: true,\n        secondsVisible: false\n      },\n      width: chartContainerRef.current.clientWidth,\n      height: height - 100 // 减去标题和工具栏高度\n    });\n\n    // 创建K线系列\n    candlestickSeries.current = chart.current.addCandlestickSeries({\n      upColor: '#ef4444',\n      // 红色上涨\n      downColor: '#22c55e',\n      // 绿色下跌\n      borderDownColor: '#22c55e',\n      borderUpColor: '#ef4444',\n      wickDownColor: '#22c55e',\n      wickUpColor: '#ef4444'\n    });\n\n    // 创建成交量系列\n    volumeSeries.current = chart.current.addHistogramSeries({\n      color: '#26a69a',\n      priceFormat: {\n        type: 'volume'\n      },\n      priceScaleId: 'volume',\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0\n      }\n    });\n\n    // 设置成交量价格刻度\n    chart.current.priceScale('volume').applyOptions({\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0\n      }\n    });\n    setChartReady(true);\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      if (chart.current && chartContainerRef.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n\n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘)\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)'\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u56FE\\u8868\\u52A0\\u8F7D\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(LightweightChart, \"CDcVyzhgpJ+sUTNz6devG9+znsU=\");\n_c = LightweightChart;\nexport default LightweightChart;\nvar _c;\n$RefreshReg$(_c, \"LightweightChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "createChart", "Card", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LightweightChart", "data", "title", "height", "loading", "error", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "chartReady", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "layout", "backgroundColor", "textColor", "grid", "vertLines", "color", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "addHistogramSeries", "priceFormat", "type", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "priceScale", "applyOptions", "handleResize", "window", "addEventListener", "removeEventListener", "remove", "Array", "isArray", "length", "candlestickData", "volumeData", "for<PERSON>ach", "item", "time", "Date", "date", "日期", "getTime", "push", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "value", "volume", "成交量", "sort", "a", "b", "setData", "<PERSON><PERSON><PERSON><PERSON>", "console", "children", "message", "description", "showIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "bodyStyle", "padding", "spinning", "tip", "ref", "position", "display", "alignItems", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\n\nconst LightweightChart = ({ \n  data, \n  title = 'K线图', \n  height = 500,\n  loading = false,\n  error = null \n}) => {\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [chartReady, setChartReady] = useState(false);\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 创建图表\n    chart.current = createChart(chartContainerRef.current, {\n      layout: {\n        backgroundColor: '#ffffff',\n        textColor: '#333',\n      },\n      grid: {\n        vertLines: { color: '#f0f0f0' },\n        horzLines: { color: '#f0f0f0' },\n      },\n      crosshair: {\n        mode: 1, // Normal crosshair mode\n      },\n      rightPriceScale: {\n        borderColor: '#cccccc',\n      },\n      timeScale: {\n        borderColor: '#cccccc',\n        timeVisible: true,\n        secondsVisible: false,\n      },\n      width: chartContainerRef.current.clientWidth,\n      height: height - 100, // 减去标题和工具栏高度\n    });\n\n    // 创建K线系列\n    candlestickSeries.current = chart.current.addCandlestickSeries({\n      upColor: '#ef4444', // 红色上涨\n      downColor: '#22c55e', // 绿色下跌\n      borderDownColor: '#22c55e',\n      borderUpColor: '#ef4444',\n      wickDownColor: '#22c55e',\n      wickUpColor: '#ef4444',\n    });\n\n    // 创建成交量系列\n    volumeSeries.current = chart.current.addHistogramSeries({\n      color: '#26a69a',\n      priceFormat: {\n        type: 'volume',\n      },\n      priceScaleId: 'volume',\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0,\n      },\n    });\n\n    // 设置成交量价格刻度\n    chart.current.priceScale('volume').applyOptions({\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0,\n      },\n    });\n\n    setChartReady(true);\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      if (chart.current && chartContainerRef.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth,\n        });\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n        \n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘),\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? \n            'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)',\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      \n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n\n  if (error) {\n    return (\n      <Card title={title}>\n        <Alert\n          message=\"图表加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n        />\n      </Card>\n    );\n  }\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default LightweightChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGf,MAAM,CAAC,CAAC;EAClC,MAAMgB,KAAK,GAAGhB,MAAM,CAAC,CAAC;EACtB,MAAMiB,iBAAiB,GAAGjB,MAAM,CAAC,CAAC;EAClC,MAAMkB,YAAY,GAAGlB,MAAM,CAAC,CAAC;EAC7B,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,iBAAiB,CAACM,OAAO,EAAE;;IAEhC;IACAL,KAAK,CAACK,OAAO,GAAGnB,WAAW,CAACa,iBAAiB,CAACM,OAAO,EAAE;MACrDC,MAAM,EAAE;QACNC,eAAe,EAAE,SAAS;QAC1BC,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJC,SAAS,EAAE;UAAEC,KAAK,EAAE;QAAU,CAAC;QAC/BC,SAAS,EAAE;UAAED,KAAK,EAAE;QAAU;MAChC,CAAC;MACDE,SAAS,EAAE;QACTC,IAAI,EAAE,CAAC,CAAE;MACX,CAAC;MACDC,eAAe,EAAE;QACfC,WAAW,EAAE;MACf,CAAC;MACDC,SAAS,EAAE;QACTD,WAAW,EAAE,SAAS;QACtBE,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE;MAClB,CAAC;MACDC,KAAK,EAAErB,iBAAiB,CAACM,OAAO,CAACgB,WAAW;MAC5C1B,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAE;IACxB,CAAC,CAAC;;IAEF;IACAM,iBAAiB,CAACI,OAAO,GAAGL,KAAK,CAACK,OAAO,CAACiB,oBAAoB,CAAC;MAC7DC,OAAO,EAAE,SAAS;MAAE;MACpBC,SAAS,EAAE,SAAS;MAAE;MACtBC,eAAe,EAAE,SAAS;MAC1BC,aAAa,EAAE,SAAS;MACxBC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA1B,YAAY,CAACG,OAAO,GAAGL,KAAK,CAACK,OAAO,CAACwB,kBAAkB,CAAC;MACtDlB,KAAK,EAAE,SAAS;MAChBmB,WAAW,EAAE;QACXC,IAAI,EAAE;MACR,CAAC;MACDC,YAAY,EAAE,QAAQ;MACtBC,YAAY,EAAE;QACZC,GAAG,EAAE,GAAG;QACRC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;;IAEF;IACAnC,KAAK,CAACK,OAAO,CAAC+B,UAAU,CAAC,QAAQ,CAAC,CAACC,YAAY,CAAC;MAC9CJ,YAAY,EAAE;QACZC,GAAG,EAAE,GAAG;QACRC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IAEF/B,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMkC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAItC,KAAK,CAACK,OAAO,IAAIN,iBAAiB,CAACM,OAAO,EAAE;QAC9CL,KAAK,CAACK,OAAO,CAACgC,YAAY,CAAC;UACzBjB,KAAK,EAAErB,iBAAiB,CAACM,OAAO,CAACgB;QACnC,CAAC,CAAC;MACJ;IACF,CAAC;IAEDkB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MAClD,IAAItC,KAAK,CAACK,OAAO,EAAE;QACjBL,KAAK,CAACK,OAAO,CAACqC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAAC/C,MAAM,CAAC,CAAC;;EAEZ;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACoB,UAAU,IAAI,CAACV,IAAI,IAAI,CAACkD,KAAK,CAACC,OAAO,CAACnD,IAAI,CAAC,IAAIA,IAAI,CAACoD,MAAM,KAAK,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,UAAU,GAAG,EAAE;MAErBtD,IAAI,CAACuD,OAAO,CAACC,IAAI,IAAI;QACnB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACI,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI;;QAE5D;QACAR,eAAe,CAACS,IAAI,CAAC;UACnBL,IAAI,EAAEA,IAAI;UACVM,IAAI,EAAEC,UAAU,CAACR,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC;UACtCC,IAAI,EAAEF,UAAU,CAACR,IAAI,CAACU,IAAI,IAAIV,IAAI,CAACW,EAAE,CAAC;UACtCC,GAAG,EAAEJ,UAAU,CAACR,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,EAAE,CAAC;UACpCC,KAAK,EAAEN,UAAU,CAACR,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE;QACzC,CAAC,CAAC;;QAEF;QACAjB,UAAU,CAACQ,IAAI,CAAC;UACdL,IAAI,EAAEA,IAAI;UACVe,KAAK,EAAER,UAAU,CAACR,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACkB,GAAG,CAAC;UAC1CxD,KAAK,EAAE,CAACsC,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE,MAAMf,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC,GACtD,wBAAwB,GAAG;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAZ,eAAe,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;MAC/CH,UAAU,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;;MAE1C;MACA,IAAIjD,iBAAiB,CAACI,OAAO,EAAE;QAC7BJ,iBAAiB,CAACI,OAAO,CAACkE,OAAO,CAACzB,eAAe,CAAC;MACpD;MAEA,IAAI5C,YAAY,CAACG,OAAO,EAAE;QACxBH,YAAY,CAACG,OAAO,CAACkE,OAAO,CAACxB,UAAU,CAAC;MAC1C;;MAEA;MACA,IAAI/C,KAAK,CAACK,OAAO,EAAE;QACjBL,KAAK,CAACK,OAAO,CAACY,SAAS,CAAC,CAAC,CAACuD,UAAU,CAAC,CAAC;MACxC;IAEF,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACd4E,OAAO,CAAC5E,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEU,UAAU,CAAC,CAAC;EAEtB,IAAIN,KAAK,EAAE;IACT,oBACEN,OAAA,CAACJ,IAAI;MAACO,KAAK,EAAEA,KAAM;MAAAgF,QAAA,eACjBnF,OAAA,CAACF,KAAK;QACJsF,OAAO,EAAC,sCAAQ;QAChBC,WAAW,EAAE/E,KAAM;QACnBkC,IAAI,EAAC,OAAO;QACZ8C,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX;EAEA,oBACE1F,OAAA,CAACJ,IAAI;IACHO,KAAK,EAAEA,KAAM;IACbwF,KAAK,EAAE;MAAE9D,KAAK,EAAE;IAAO,CAAE;IACzB+D,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,eAE/BnF,OAAA,CAACH,IAAI;MAACiG,QAAQ,EAAEzF,OAAQ;MAAC0F,GAAG,EAAC,yCAAW;MAAAZ,QAAA,gBACtCnF,OAAA;QACEgG,GAAG,EAAExF,iBAAkB;QACvBmF,KAAK,EAAE;UACL9D,KAAK,EAAE,MAAM;UACbzB,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB6F,QAAQ,EAAE;QACZ;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACrF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACoD,MAAM,KAAK,CAAC,CAAC,iBACvCtD,OAAA;QACE2F,KAAK,EAAE;UACLvF,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB8F,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBhF,KAAK,EAAE,MAAM;UACbiF,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACnF,EAAA,CAlMIN,gBAAgB;AAAAqG,EAAA,GAAhBrG,gBAAgB;AAoMtB,eAAeA,gBAAgB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}