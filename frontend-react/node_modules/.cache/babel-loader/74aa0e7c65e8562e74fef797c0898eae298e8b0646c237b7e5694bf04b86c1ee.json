{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LinkedinOutlinedSvg from \"@ant-design/icons-svg/es/asn/LinkedinOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LinkedinOutlined = function LinkedinOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LinkedinOutlinedSvg\n  }));\n};\n\n/**![linkedin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg0Ny43IDExMkgxNzYuM2MtMzUuNSAwLTY0LjMgMjguOC02NC4zIDY0LjN2NjcxLjRjMCAzNS41IDI4LjggNjQuMyA2NC4zIDY0LjNoNjcxLjRjMzUuNSAwIDY0LjMtMjguOCA2NC4zLTY0LjNWMTc2LjNjMC0zNS41LTI4LjgtNjQuMy02NC4zLTY0LjN6bTAgNzM2Yy00NDcuOC0uMS02NzEuNy0uMi02NzEuNy0uMy4xLTQ0Ny44LjItNjcxLjcuMy02NzEuNyA0NDcuOC4xIDY3MS43LjIgNjcxLjcuMy0uMSA0NDcuOC0uMiA2NzEuNy0uMyA2NzEuN3pNMjMwLjYgNDExLjloMTE4Ljd2MzgxLjhIMjMwLjZ6bTU5LjQtNTIuMmMzNy45IDAgNjguOC0zMC44IDY4LjgtNjguOGE2OC44IDY4LjggMCAxMC0xMzcuNiAwYy0uMSAzOCAzMC43IDY4LjggNjguOCA2OC44em0yNTIuMyAyNDUuMWMwLTQ5LjggOS41LTk4IDcxLjItOTggNjAuOCAwIDYxLjcgNTYuOSA2MS43IDEwMS4ydjE4NS43aDExOC42VjU4NC4zYzAtMTAyLjgtMjIuMi0xODEuOS0xNDIuMy0xODEuOS01Ny43IDAtOTYuNCAzMS43LTExMi4zIDYxLjdoLTEuNnYtNTIuMkg0MjMuN3YzODEuOGgxMTguNlY2MDQuOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LinkedinOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LinkedinOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "LinkedinOutlinedSvg", "AntdIcon", "LinkedinOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/LinkedinOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LinkedinOutlinedSvg from \"@ant-design/icons-svg/es/asn/LinkedinOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LinkedinOutlined = function LinkedinOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LinkedinOutlinedSvg\n  }));\n};\n\n/**![linkedin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg0Ny43IDExMkgxNzYuM2MtMzUuNSAwLTY0LjMgMjguOC02NC4zIDY0LjN2NjcxLjRjMCAzNS41IDI4LjggNjQuMyA2NC4zIDY0LjNoNjcxLjRjMzUuNSAwIDY0LjMtMjguOCA2NC4zLTY0LjNWMTc2LjNjMC0zNS41LTI4LjgtNjQuMy02NC4zLTY0LjN6bTAgNzM2Yy00NDcuOC0uMS02NzEuNy0uMi02NzEuNy0uMy4xLTQ0Ny44LjItNjcxLjcuMy02NzEuNyA0NDcuOC4xIDY3MS43LjIgNjcxLjcuMy0uMSA0NDcuOC0uMiA2NzEuNy0uMyA2NzEuN3pNMjMwLjYgNDExLjloMTE4Ljd2MzgxLjhIMjMwLjZ6bTU5LjQtNTIuMmMzNy45IDAgNjguOC0zMC44IDY4LjgtNjguOGE2OC44IDY4LjggMCAxMC0xMzcuNiAwYy0uMSAzOCAzMC43IDY4LjggNjguOCA2OC44em0yNTIuMyAyNDUuMWMwLTQ5LjggOS41LTk4IDcxLjItOTggNjAuOCAwIDYxLjcgNTYuOSA2MS43IDEwMS4ydjE4NS43aDExOC42VjU4NC4zYzAtMTAyLjgtMjIuMi0xODEuOS0xNDIuMy0xODEuOS01Ny43IDAtOTYuNCAzMS43LTExMi4zIDYxLjdoLTEuNnYtNTIuMkg0MjMuN3YzODEuOGgxMTguNlY2MDQuOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LinkedinOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LinkedinOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}