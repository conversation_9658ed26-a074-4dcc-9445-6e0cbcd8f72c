{"ast": null, "code": "import { createContext, useContext } from \"./context\";\nimport createImmutable from \"./Immutable\";\n\n// For legacy usage, we export it directly\nvar _createImmutable = createImmutable(),\n  makeImmutable = _createImmutable.makeImmutable,\n  responseImmutable = _createImmutable.responseImmutable,\n  useImmutableMark = _createImmutable.useImmutableMark;\nexport { createContext, useContext, createImmutable, makeImmutable, responseImmutable, useImmutableMark };", "map": {"version": 3, "names": ["createContext", "useContext", "createImmutable", "_createImmutable", "makeImmutable", "responseImmutable", "useImmutableMark"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@rc-component/context/es/index.js"], "sourcesContent": ["import { createContext, useContext } from \"./context\";\nimport createImmutable from \"./Immutable\";\n\n// For legacy usage, we export it directly\nvar _createImmutable = createImmutable(),\n  makeImmutable = _createImmutable.makeImmutable,\n  responseImmutable = _createImmutable.responseImmutable,\n  useImmutableMark = _createImmutable.useImmutableMark;\nexport { createContext, useContext, createImmutable, makeImmutable, responseImmutable, useImmutableMark };"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,WAAW;AACrD,OAAOC,eAAe,MAAM,aAAa;;AAEzC;AACA,IAAIC,gBAAgB,GAAGD,eAAe,CAAC,CAAC;EACtCE,aAAa,GAAGD,gBAAgB,CAACC,aAAa;EAC9CC,iBAAiB,GAAGF,gBAAgB,CAACE,iBAAiB;EACtDC,gBAAgB,GAAGH,gBAAgB,CAACG,gBAAgB;AACtD,SAASN,aAAa,EAAEC,UAAU,EAAEC,eAAe,EAAEE,aAAa,EAAEC,iBAAiB,EAAEC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}