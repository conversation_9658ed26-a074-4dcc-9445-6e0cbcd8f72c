{"ast": null, "code": "import { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport * as React from 'react';\nvar SPEED_PTG = 1 / 3;\nexport default function useScrollTo(ulRef, value) {\n  // ========================= Scroll =========================\n  var scrollingRef = React.useRef(false);\n  var scrollRafRef = React.useRef(null);\n  var scrollDistRef = React.useRef(null);\n  var isScrolling = function isScrolling() {\n    return scrollingRef.current;\n  };\n  var stopScroll = function stopScroll() {\n    raf.cancel(scrollRafRef.current);\n    scrollingRef.current = false;\n  };\n  var scrollRafTimesRef = React.useRef();\n  var startScroll = function startScroll() {\n    var ul = ulRef.current;\n    scrollDistRef.current = null;\n    scrollRafTimesRef.current = 0;\n    if (ul) {\n      var targetLi = ul.querySelector(\"[data-value=\\\"\".concat(value, \"\\\"]\"));\n      var firstLi = ul.querySelector(\"li\");\n      var doScroll = function doScroll() {\n        stopScroll();\n        scrollingRef.current = true;\n        scrollRafTimesRef.current += 1;\n        var currentTop = ul.scrollTop;\n        var firstLiTop = firstLi.offsetTop;\n        var targetLiTop = targetLi.offsetTop;\n        var targetTop = targetLiTop - firstLiTop;\n\n        // Wait for element exist. 5 frames is enough\n        if (targetLiTop === 0 && targetLi !== firstLi || !isVisible(ul)) {\n          if (scrollRafTimesRef.current <= 5) {\n            scrollRafRef.current = raf(doScroll);\n          }\n          return;\n        }\n        var nextTop = currentTop + (targetTop - currentTop) * SPEED_PTG;\n        var dist = Math.abs(targetTop - nextTop);\n\n        // Break if dist get larger, which means user is scrolling\n        if (scrollDistRef.current !== null && scrollDistRef.current < dist) {\n          stopScroll();\n          return;\n        }\n        scrollDistRef.current = dist;\n\n        // Stop when dist is less than 1\n        if (dist <= 1) {\n          ul.scrollTop = targetTop;\n          stopScroll();\n          return;\n        }\n\n        // IE not support `scrollTo`\n        ul.scrollTop = nextTop;\n        scrollRafRef.current = raf(doScroll);\n      };\n      if (targetLi && firstLi) {\n        doScroll();\n      }\n    }\n  };\n\n  // ======================== Trigger =========================\n  var syncScroll = useEvent(startScroll);\n  return [syncScroll, stopScroll, isScrolling];\n}", "map": {"version": 3, "names": ["useEvent", "raf", "isVisible", "React", "SPEED_PTG", "useScrollTo", "ulRef", "value", "scrollingRef", "useRef", "scrollRafRef", "scrollDistRef", "isScrolling", "current", "stopScroll", "cancel", "scrollRafTimesRef", "startScroll", "ul", "targetLi", "querySelector", "concat", "firstLi", "doScroll", "currentTop", "scrollTop", "firstLiTop", "offsetTop", "targetLiTop", "targetTop", "nextTop", "dist", "Math", "abs", "syncScroll"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-picker/es/PickerPanel/TimePanel/TimePanelBody/useScrollTo.js"], "sourcesContent": ["import { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport * as React from 'react';\nvar SPEED_PTG = 1 / 3;\nexport default function useScrollTo(ulRef, value) {\n  // ========================= Scroll =========================\n  var scrollingRef = React.useRef(false);\n  var scrollRafRef = React.useRef(null);\n  var scrollDistRef = React.useRef(null);\n  var isScrolling = function isScrolling() {\n    return scrollingRef.current;\n  };\n  var stopScroll = function stopScroll() {\n    raf.cancel(scrollRafRef.current);\n    scrollingRef.current = false;\n  };\n  var scrollRafTimesRef = React.useRef();\n  var startScroll = function startScroll() {\n    var ul = ulRef.current;\n    scrollDistRef.current = null;\n    scrollRafTimesRef.current = 0;\n    if (ul) {\n      var targetLi = ul.querySelector(\"[data-value=\\\"\".concat(value, \"\\\"]\"));\n      var firstLi = ul.querySelector(\"li\");\n      var doScroll = function doScroll() {\n        stopScroll();\n        scrollingRef.current = true;\n        scrollRafTimesRef.current += 1;\n        var currentTop = ul.scrollTop;\n        var firstLiTop = firstLi.offsetTop;\n        var targetLiTop = targetLi.offsetTop;\n        var targetTop = targetLiTop - firstLiTop;\n\n        // Wait for element exist. 5 frames is enough\n        if (targetLiTop === 0 && targetLi !== firstLi || !isVisible(ul)) {\n          if (scrollRafTimesRef.current <= 5) {\n            scrollRafRef.current = raf(doScroll);\n          }\n          return;\n        }\n        var nextTop = currentTop + (targetTop - currentTop) * SPEED_PTG;\n        var dist = Math.abs(targetTop - nextTop);\n\n        // Break if dist get larger, which means user is scrolling\n        if (scrollDistRef.current !== null && scrollDistRef.current < dist) {\n          stopScroll();\n          return;\n        }\n        scrollDistRef.current = dist;\n\n        // Stop when dist is less than 1\n        if (dist <= 1) {\n          ul.scrollTop = targetTop;\n          stopScroll();\n          return;\n        }\n\n        // IE not support `scrollTo`\n        ul.scrollTop = nextTop;\n        scrollRafRef.current = raf(doScroll);\n      };\n      if (targetLi && firstLi) {\n        doScroll();\n      }\n    }\n  };\n\n  // ======================== Trigger =========================\n  var syncScroll = useEvent(startScroll);\n  return [syncScroll, stopScroll, isScrolling];\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAClC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,SAAS,GAAG,CAAC,GAAG,CAAC;AACrB,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAChD;EACA,IAAIC,YAAY,GAAGL,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EACtC,IAAIC,YAAY,GAAGP,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIE,aAAa,GAAGR,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,OAAOJ,YAAY,CAACK,OAAO;EAC7B,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrCb,GAAG,CAACc,MAAM,CAACL,YAAY,CAACG,OAAO,CAAC;IAChCL,YAAY,CAACK,OAAO,GAAG,KAAK;EAC9B,CAAC;EACD,IAAIG,iBAAiB,GAAGb,KAAK,CAACM,MAAM,CAAC,CAAC;EACtC,IAAIQ,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIC,EAAE,GAAGZ,KAAK,CAACO,OAAO;IACtBF,aAAa,CAACE,OAAO,GAAG,IAAI;IAC5BG,iBAAiB,CAACH,OAAO,GAAG,CAAC;IAC7B,IAAIK,EAAE,EAAE;MACN,IAAIC,QAAQ,GAAGD,EAAE,CAACE,aAAa,CAAC,gBAAgB,CAACC,MAAM,CAACd,KAAK,EAAE,KAAK,CAAC,CAAC;MACtE,IAAIe,OAAO,GAAGJ,EAAE,CAACE,aAAa,CAAC,IAAI,CAAC;MACpC,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;QACjCT,UAAU,CAAC,CAAC;QACZN,YAAY,CAACK,OAAO,GAAG,IAAI;QAC3BG,iBAAiB,CAACH,OAAO,IAAI,CAAC;QAC9B,IAAIW,UAAU,GAAGN,EAAE,CAACO,SAAS;QAC7B,IAAIC,UAAU,GAAGJ,OAAO,CAACK,SAAS;QAClC,IAAIC,WAAW,GAAGT,QAAQ,CAACQ,SAAS;QACpC,IAAIE,SAAS,GAAGD,WAAW,GAAGF,UAAU;;QAExC;QACA,IAAIE,WAAW,KAAK,CAAC,IAAIT,QAAQ,KAAKG,OAAO,IAAI,CAACpB,SAAS,CAACgB,EAAE,CAAC,EAAE;UAC/D,IAAIF,iBAAiB,CAACH,OAAO,IAAI,CAAC,EAAE;YAClCH,YAAY,CAACG,OAAO,GAAGZ,GAAG,CAACsB,QAAQ,CAAC;UACtC;UACA;QACF;QACA,IAAIO,OAAO,GAAGN,UAAU,GAAG,CAACK,SAAS,GAAGL,UAAU,IAAIpB,SAAS;QAC/D,IAAI2B,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACJ,SAAS,GAAGC,OAAO,CAAC;;QAExC;QACA,IAAInB,aAAa,CAACE,OAAO,KAAK,IAAI,IAAIF,aAAa,CAACE,OAAO,GAAGkB,IAAI,EAAE;UAClEjB,UAAU,CAAC,CAAC;UACZ;QACF;QACAH,aAAa,CAACE,OAAO,GAAGkB,IAAI;;QAE5B;QACA,IAAIA,IAAI,IAAI,CAAC,EAAE;UACbb,EAAE,CAACO,SAAS,GAAGI,SAAS;UACxBf,UAAU,CAAC,CAAC;UACZ;QACF;;QAEA;QACAI,EAAE,CAACO,SAAS,GAAGK,OAAO;QACtBpB,YAAY,CAACG,OAAO,GAAGZ,GAAG,CAACsB,QAAQ,CAAC;MACtC,CAAC;MACD,IAAIJ,QAAQ,IAAIG,OAAO,EAAE;QACvBC,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;;EAED;EACA,IAAIW,UAAU,GAAGlC,QAAQ,CAACiB,WAAW,CAAC;EACtC,OAAO,CAACiB,UAAU,EAAEpB,UAAU,EAAEF,WAAW,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}