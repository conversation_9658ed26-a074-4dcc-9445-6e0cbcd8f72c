{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n    try {\n      // 创建图表\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333'\n        },\n        grid: {\n          vertLines: {\n            color: '#f0f0f0'\n          },\n          horzLines: {\n            color: '#f0f0f0'\n          }\n        },\n        crosshair: {\n          mode: 1\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc'\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100\n      });\n\n      // 创建K线系列 - 使用新版本API\n      candlestickSeries.current = chart.current.addSeries('Candlestick', {\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444'\n      });\n\n      // 创建成交量系列 - 使用新版本API\n      volumeSeries.current = chart.current.addSeries('Histogram', {\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume'\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0\n        }\n      });\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart.current && chartContainerRef.current) {\n          chart.current.applyOptions({\n            width: chartContainerRef.current.clientWidth\n          });\n        }\n      };\n      window.addEventListener('resize', handleResize);\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart.current) {\n          chart.current.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表初始化失败:', error);\n    }\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chart.current || !candlestickSeries.current || !volumeSeries.current || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n      data.forEach(item => {\n        // 处理日期格式\n        let timeValue;\n        const dateStr = item.date || item.日期;\n        if (dateStr) {\n          const date = new Date(dateStr);\n          timeValue = Math.floor(date.getTime() / 1000);\n        } else {\n          return; // 跳过没有日期的数据\n        }\n\n        // K线数据\n        const open = parseFloat(item.open || item.开盘);\n        const high = parseFloat(item.high || item.最高);\n        const low = parseFloat(item.low || item.最低);\n        const close = parseFloat(item.close || item.收盘);\n        const volume = parseFloat(item.volume || item.成交量);\n        if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n          candlestickData.push({\n            time: timeValue,\n            open: open,\n            high: high,\n            low: low,\n            close: close\n          });\n        }\n        if (!isNaN(volume)) {\n          volumeData.push({\n            time: timeValue,\n            value: volume,\n            color: close >= open ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)'\n          });\n        }\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickData.length > 0) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      setTimeout(() => {\n        if (chart.current) {\n          chart.current.timeScale().fitContent();\n        }\n      }, 100);\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u56FE\\u8868\\u52A0\\u8F7D\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(LightweightChart, \"xsFfQmiJkkGMDJZPToJPhbAj94o=\");\n_c = LightweightChart;\nexport default LightweightChart;\nvar _c;\n$RefreshReg$(_c, \"LightweightChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "createChart", "Card", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LightweightChart", "data", "title", "height", "loading", "error", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "current", "layout", "backgroundColor", "textColor", "grid", "vertLines", "color", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "addSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "priceFormat", "type", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "handleResize", "applyOptions", "window", "addEventListener", "removeEventListener", "remove", "console", "Array", "isArray", "length", "candlestickData", "volumeData", "for<PERSON>ach", "item", "timeValue", "dateStr", "date", "日期", "Date", "Math", "floor", "getTime", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "volume", "成交量", "isNaN", "push", "time", "value", "sort", "a", "b", "setData", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "children", "message", "description", "showIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "bodyStyle", "padding", "spinning", "tip", "ref", "position", "display", "alignItems", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\n\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    try {\n      // 创建图表\n      chart.current = createChart(chartContainerRef.current, {\n        layout: {\n          backgroundColor: '#ffffff',\n          textColor: '#333',\n        },\n        grid: {\n          vertLines: { color: '#f0f0f0' },\n          horzLines: { color: '#f0f0f0' },\n        },\n        crosshair: {\n          mode: 1,\n        },\n        rightPriceScale: {\n          borderColor: '#cccccc',\n        },\n        timeScale: {\n          borderColor: '#cccccc',\n          timeVisible: true,\n          secondsVisible: false,\n        },\n        width: chartContainerRef.current.clientWidth,\n        height: height - 100,\n      });\n\n      // 创建K线系列 - 使用新版本API\n      candlestickSeries.current = chart.current.addSeries('Candlestick', {\n        upColor: '#ef4444',\n        downColor: '#22c55e',\n        borderDownColor: '#22c55e',\n        borderUpColor: '#ef4444',\n        wickDownColor: '#22c55e',\n        wickUpColor: '#ef4444',\n      });\n\n      // 创建成交量系列 - 使用新版本API\n      volumeSeries.current = chart.current.addSeries('Histogram', {\n        color: '#26a69a',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: 'volume',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0,\n        },\n      });\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (chart.current && chartContainerRef.current) {\n          chart.current.applyOptions({\n            width: chartContainerRef.current.clientWidth,\n          });\n        }\n      };\n\n      window.addEventListener('resize', handleResize);\n\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        if (chart.current) {\n          chart.current.remove();\n        }\n      };\n    } catch (error) {\n      console.error('图表初始化失败:', error);\n    }\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chart.current || !candlestickSeries.current || !volumeSeries.current || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n\n      data.forEach(item => {\n        // 处理日期格式\n        let timeValue;\n        const dateStr = item.date || item.日期;\n        if (dateStr) {\n          const date = new Date(dateStr);\n          timeValue = Math.floor(date.getTime() / 1000);\n        } else {\n          return; // 跳过没有日期的数据\n        }\n\n        // K线数据\n        const open = parseFloat(item.open || item.开盘);\n        const high = parseFloat(item.high || item.最高);\n        const low = parseFloat(item.low || item.最低);\n        const close = parseFloat(item.close || item.收盘);\n        const volume = parseFloat(item.volume || item.成交量);\n\n        if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n          candlestickData.push({\n            time: timeValue,\n            open: open,\n            high: high,\n            low: low,\n            close: close,\n          });\n        }\n\n        if (!isNaN(volume)) {\n          volumeData.push({\n            time: timeValue,\n            value: volume,\n            color: close >= open ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)',\n          });\n        }\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickData.length > 0) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n\n      if (volumeData.length > 0) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      setTimeout(() => {\n        if (chart.current) {\n          chart.current.timeScale().fitContent();\n        }\n      }, 100);\n\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data]);\n\n  if (error) {\n    return (\n      <Card title={title}>\n        <Alert\n          message=\"图表加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n        />\n      </Card>\n    );\n  }\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default LightweightChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGf,MAAM,CAAC,CAAC;EAClC,MAAMgB,KAAK,GAAGhB,MAAM,CAAC,CAAC;EACtB,MAAMiB,iBAAiB,GAAGjB,MAAM,CAAC,CAAC;EAClC,MAAMkB,YAAY,GAAGlB,MAAM,CAAC,CAAC;;EAE7B;EACAD,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,iBAAiB,CAACI,OAAO,EAAE;IAEhC,IAAI;MACF;MACAH,KAAK,CAACG,OAAO,GAAGjB,WAAW,CAACa,iBAAiB,CAACI,OAAO,EAAE;QACrDC,MAAM,EAAE;UACNC,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAE;UACJC,SAAS,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAC;UAC/BC,SAAS,EAAE;YAAED,KAAK,EAAE;UAAU;QAChC,CAAC;QACDE,SAAS,EAAE;UACTC,IAAI,EAAE;QACR,CAAC;QACDC,eAAe,EAAE;UACfC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTD,WAAW,EAAE,SAAS;UACtBE,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE;QAClB,CAAC;QACDC,KAAK,EAAEnB,iBAAiB,CAACI,OAAO,CAACgB,WAAW;QAC5CxB,MAAM,EAAEA,MAAM,GAAG;MACnB,CAAC,CAAC;;MAEF;MACAM,iBAAiB,CAACE,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACiB,SAAS,CAAC,aAAa,EAAE;QACjEC,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,eAAe,EAAE,SAAS;QAC1BC,aAAa,EAAE,SAAS;QACxBC,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACAxB,YAAY,CAACC,OAAO,GAAGH,KAAK,CAACG,OAAO,CAACiB,SAAS,CAAC,WAAW,EAAE;QAC1DX,KAAK,EAAE,SAAS;QAChBkB,WAAW,EAAE;UACXC,IAAI,EAAE;QACR,CAAC;QACDC,YAAY,EAAE,QAAQ;QACtBC,YAAY,EAAE;UACZC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE;QACV;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAIjC,KAAK,CAACG,OAAO,IAAIJ,iBAAiB,CAACI,OAAO,EAAE;UAC9CH,KAAK,CAACG,OAAO,CAAC+B,YAAY,CAAC;YACzBhB,KAAK,EAAEnB,iBAAiB,CAACI,OAAO,CAACgB;UACnC,CAAC,CAAC;QACJ;MACF,CAAC;MAEDgB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MAE/C,OAAO,MAAM;QACXE,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;QAClD,IAAIjC,KAAK,CAACG,OAAO,EAAE;UACjBH,KAAK,CAACG,OAAO,CAACmC,MAAM,CAAC,CAAC;QACxB;MACF,CAAC;IACH,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACd0C,OAAO,CAAC1C,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACF,MAAM,CAAC,CAAC;;EAEZ;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,KAAK,CAACG,OAAO,IAAI,CAACF,iBAAiB,CAACE,OAAO,IAAI,CAACD,YAAY,CAACC,OAAO,IAAI,CAACV,IAAI,IAAI,CAAC+C,KAAK,CAACC,OAAO,CAAChD,IAAI,CAAC,IAAIA,IAAI,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC/H;IACF;IAEA,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,UAAU,GAAG,EAAE;MAErBnD,IAAI,CAACoD,OAAO,CAACC,IAAI,IAAI;QACnB;QACA,IAAIC,SAAS;QACb,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACI,EAAE;QACpC,IAAIF,OAAO,EAAE;UACX,MAAMC,IAAI,GAAG,IAAIE,IAAI,CAACH,OAAO,CAAC;UAC9BD,SAAS,GAAGK,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/C,CAAC,MAAM;UACL,OAAO,CAAC;QACV;;QAEA;QACA,MAAMC,IAAI,GAAGC,UAAU,CAACV,IAAI,CAACS,IAAI,IAAIT,IAAI,CAACW,EAAE,CAAC;QAC7C,MAAMC,IAAI,GAAGF,UAAU,CAACV,IAAI,CAACY,IAAI,IAAIZ,IAAI,CAACa,EAAE,CAAC;QAC7C,MAAMC,GAAG,GAAGJ,UAAU,CAACV,IAAI,CAACc,GAAG,IAAId,IAAI,CAACe,EAAE,CAAC;QAC3C,MAAMC,KAAK,GAAGN,UAAU,CAACV,IAAI,CAACgB,KAAK,IAAIhB,IAAI,CAACiB,EAAE,CAAC;QAC/C,MAAMC,MAAM,GAAGR,UAAU,CAACV,IAAI,CAACkB,MAAM,IAAIlB,IAAI,CAACmB,GAAG,CAAC;QAElD,IAAI,CAACC,KAAK,CAACX,IAAI,CAAC,IAAI,CAACW,KAAK,CAACR,IAAI,CAAC,IAAI,CAACQ,KAAK,CAACN,GAAG,CAAC,IAAI,CAACM,KAAK,CAACJ,KAAK,CAAC,EAAE;UAChEnB,eAAe,CAACwB,IAAI,CAAC;YACnBC,IAAI,EAAErB,SAAS;YACfQ,IAAI,EAAEA,IAAI;YACVG,IAAI,EAAEA,IAAI;YACVE,GAAG,EAAEA,GAAG;YACRE,KAAK,EAAEA;UACT,CAAC,CAAC;QACJ;QAEA,IAAI,CAACI,KAAK,CAACF,MAAM,CAAC,EAAE;UAClBpB,UAAU,CAACuB,IAAI,CAAC;YACdC,IAAI,EAAErB,SAAS;YACfsB,KAAK,EAAEL,MAAM;YACbvD,KAAK,EAAEqD,KAAK,IAAIP,IAAI,GAAG,wBAAwB,GAAG;UACpD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACAZ,eAAe,CAAC2B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACH,IAAI,GAAGI,CAAC,CAACJ,IAAI,CAAC;MAC/CxB,UAAU,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACH,IAAI,GAAGI,CAAC,CAACJ,IAAI,CAAC;;MAE1C;MACA,IAAIzB,eAAe,CAACD,MAAM,GAAG,CAAC,EAAE;QAC9BzC,iBAAiB,CAACE,OAAO,CAACsE,OAAO,CAAC9B,eAAe,CAAC;MACpD;MAEA,IAAIC,UAAU,CAACF,MAAM,GAAG,CAAC,EAAE;QACzBxC,YAAY,CAACC,OAAO,CAACsE,OAAO,CAAC7B,UAAU,CAAC;MAC1C;;MAEA;MACA8B,UAAU,CAAC,MAAM;QACf,IAAI1E,KAAK,CAACG,OAAO,EAAE;UACjBH,KAAK,CAACG,OAAO,CAACY,SAAS,CAAC,CAAC,CAAC4D,UAAU,CAAC,CAAC;QACxC;MACF,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAO9E,KAAK,EAAE;MACd0C,OAAO,CAAC1C,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EAEV,IAAII,KAAK,EAAE;IACT,oBACEN,OAAA,CAACJ,IAAI;MAACO,KAAK,EAAEA,KAAM;MAAAkF,QAAA,eACjBrF,OAAA,CAACF,KAAK;QACJwF,OAAO,EAAC,sCAAQ;QAChBC,WAAW,EAAEjF,KAAM;QACnB+B,IAAI,EAAC,OAAO;QACZmD,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX;EAEA,oBACE5F,OAAA,CAACJ,IAAI;IACHO,KAAK,EAAEA,KAAM;IACb0F,KAAK,EAAE;MAAElE,KAAK,EAAE;IAAO,CAAE;IACzBmE,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,eAE/BrF,OAAA,CAACH,IAAI;MAACmG,QAAQ,EAAE3F,OAAQ;MAAC4F,GAAG,EAAC,yCAAW;MAAAZ,QAAA,gBACtCrF,OAAA;QACEkG,GAAG,EAAE1F,iBAAkB;QACvBqF,KAAK,EAAE;UACLlE,KAAK,EAAE,MAAM;UACbvB,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB+F,QAAQ,EAAE;QACZ;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACvF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACiD,MAAM,KAAK,CAAC,CAAC,iBACvCnD,OAAA;QACE6F,KAAK,EAAE;UACLzF,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpBgG,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBpF,KAAK,EAAE,MAAM;UACbqF,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACrF,EAAA,CA7MIN,gBAAgB;AAAAuG,EAAA,GAAhBvG,gBAAgB;AA+MtB,eAAeA,gBAAgB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}