{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    _defineProperty(this, \"maps\", void 0);\n    // Used for cache key\n    // `useMemo` no need to update if `id` not change\n    _defineProperty(this, \"id\", 0);\n    _defineProperty(this, \"diffRecords\", new Map());\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      // Record prev value\n      this.diffRecords.set(key, this.maps[key]);\n      this.maps[key] = value;\n      this.id += 1;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n\n    /**\n     * CacheMap will record the key changed.\n     * To help to know what's update in the next render.\n     */\n  }, {\n    key: \"resetRecord\",\n    value: function resetRecord() {\n      this.diffRecords.clear();\n    }\n  }, {\n    key: \"getRecord\",\n    value: function getRecord() {\n      return this.diffRecords;\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "CacheMap", "Map", "maps", "Object", "create", "key", "value", "set", "diffRecords", "id", "get", "resetRecord", "clear", "getRecord"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-virtual-list/es/utils/CacheMap.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    _defineProperty(this, \"maps\", void 0);\n    // Used for cache key\n    // `useMemo` no need to update if `id` not change\n    _defineProperty(this, \"id\", 0);\n    _defineProperty(this, \"diffRecords\", new Map());\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      // Record prev value\n      this.diffRecords.set(key, this.maps[key]);\n      this.maps[key] = value;\n      this.id += 1;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n\n    /**\n     * CacheMap will record the key changed.\n     * To help to know what's update in the next render.\n     */\n  }, {\n    key: \"resetRecord\",\n    value: function resetRecord() {\n      this.diffRecords.clear();\n    }\n  }, {\n    key: \"getRecord\",\n    value: function getRecord() {\n      return this.diffRecords;\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;AACA,IAAIC,QAAQ,GAAG,aAAa,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG;IAClBH,eAAe,CAAC,IAAI,EAAEG,QAAQ,CAAC;IAC/BD,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC;IACA;IACAA,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9BA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,IAAIE,GAAG,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACjC;EACAN,YAAY,CAACE,QAAQ,EAAE,CAAC;IACtBK,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;MAC9B;MACA,IAAI,CAACE,WAAW,CAACD,GAAG,CAACF,GAAG,EAAE,IAAI,CAACH,IAAI,CAACG,GAAG,CAAC,CAAC;MACzC,IAAI,CAACH,IAAI,CAACG,GAAG,CAAC,GAAGC,KAAK;MACtB,IAAI,CAACG,EAAE,IAAI,CAAC;IACd;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASI,GAAGA,CAACL,GAAG,EAAE;MACvB,OAAO,IAAI,CAACH,IAAI,CAACG,GAAG,CAAC;IACvB;;IAEA;AACJ;AACA;AACA;EACE,CAAC,EAAE;IACDA,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASK,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,SAASO,SAASA,CAAA,EAAG;MAC1B,OAAO,IAAI,CAACL,WAAW;IACzB;EACF,CAAC,CAAC,CAAC;EACH,OAAOR,QAAQ;AACjB,CAAC,CAAC,CAAC;AACH,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}