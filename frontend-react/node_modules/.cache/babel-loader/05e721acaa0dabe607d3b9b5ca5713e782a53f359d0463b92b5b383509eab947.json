{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ShoppingOutlinedSvg from \"@ant-design/icons-svg/es/asn/ShoppingOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ShoppingOutlined = function ShoppingOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ShoppingOutlinedSvg\n  }));\n};\n\n/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAzMTJINjk2di0xNmMwLTEwMS42LTgyLjQtMTg0LTE4NC0xODRzLTE4NCA4Mi40LTE4NCAxODR2MTZIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjM0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQzMi0xNmMwLTYxLjkgNTAuMS0xMTIgMTEyLTExMnMxMTIgNTAuMSAxMTIgMTEydjE2SDQwMHYtMTZ6bTM5MiA1NDRIMjMyVjM4NGg5NnY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGgyMjR2ODhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtODhoOTZ2NDU2eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ShoppingOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ShoppingOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ShoppingOutlinedSvg", "AntdIcon", "ShoppingOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/ShoppingOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ShoppingOutlinedSvg from \"@ant-design/icons-svg/es/asn/ShoppingOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ShoppingOutlined = function ShoppingOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ShoppingOutlinedSvg\n  }));\n};\n\n/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAzMTJINjk2di0xNmMwLTEwMS42LTgyLjQtMTg0LTE4NC0xODRzLTE4NCA4Mi40LTE4NCAxODR2MTZIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjM0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQzMi0xNmMwLTYxLjkgNTAuMS0xMTIgMTEyLTExMnMxMTIgNTAuMSAxMTIgMTEydjE2SDQwMHYtMTZ6bTM5MiA1NDRIMjMyVjM4NGg5NnY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGgyMjR2ODhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtODhoOTZ2NDU2eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ShoppingOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ShoppingOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}