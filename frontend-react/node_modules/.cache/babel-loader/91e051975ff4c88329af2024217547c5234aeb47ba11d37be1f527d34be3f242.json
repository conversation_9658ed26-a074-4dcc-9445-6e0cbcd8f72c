{"ast": null, "code": "import * as React from 'react';\nexport function Item(_ref) {\n  var children = _ref.children,\n    setRef = _ref.setRef;\n  var refFunc = React.useCallback(function (node) {\n    setRef(node);\n  }, []);\n  return /*#__PURE__*/React.cloneElement(children, {\n    ref: refFunc\n  });\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "_ref", "children", "setRef", "refFunc", "useCallback", "node", "cloneElement", "ref"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-virtual-list/es/Item.js"], "sourcesContent": ["import * as React from 'react';\nexport function Item(_ref) {\n  var children = _ref.children,\n    setRef = _ref.setRef;\n  var refFunc = React.useCallback(function (node) {\n    setRef(node);\n  }, []);\n  return /*#__PURE__*/React.cloneElement(children, {\n    ref: refFunc\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,IAAIA,CAACC,IAAI,EAAE;EACzB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;EACtB,IAAIC,OAAO,GAAGL,KAAK,CAACM,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC9CH,MAAM,CAACG,IAAI,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaP,KAAK,CAACQ,YAAY,CAACL,QAAQ,EAAE;IAC/CM,GAAG,EAAEJ;EACP,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}