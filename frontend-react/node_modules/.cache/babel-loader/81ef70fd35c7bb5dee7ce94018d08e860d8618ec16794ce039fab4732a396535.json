{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SnippetsFilledSvg from \"@ant-design/icons-svg/es/asn/SnippetsFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SnippetsFilled = function SnippetsFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SnippetsFilledSvg\n  }));\n};\n\n/**![snippets](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAxMTJINzI0VjcyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY0MEg1MDBWNzJjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djQwSDMyMGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTIwaC05NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NjMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUxMmMxNy43IDAgMzItMTQuMyAzMi0zMnYtOTZoOTZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyek02NjQgNDg2SDUxNFYzMzZoLjJMNjY0IDQ4NS44di4yem0xMjggMjc0aC01NlY0NTZMNTQ0IDI2NEgzNjB2LTgwaDY4djMyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTMyaDE1MnYzMmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di0zMmg2OHY1NzZ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SnippetsFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SnippetsFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SnippetsFilledSvg", "AntdIcon", "SnippetsFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/SnippetsFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SnippetsFilledSvg from \"@ant-design/icons-svg/es/asn/SnippetsFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SnippetsFilled = function SnippetsFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SnippetsFilledSvg\n  }));\n};\n\n/**![snippets](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAxMTJINzI0VjcyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY0MEg1MDBWNzJjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djQwSDMyMGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTIwaC05NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NjMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUxMmMxNy43IDAgMzItMTQuMyAzMi0zMnYtOTZoOTZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyek02NjQgNDg2SDUxNFYzMzZoLjJMNjY0IDQ4NS44di4yem0xMjggMjc0aC01NlY0NTZMNTQ0IDI2NEgzNjB2LTgwaDY4djMyYzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTMyaDE1MnYzMmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di0zMmg2OHY1NzZ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SnippetsFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SnippetsFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}