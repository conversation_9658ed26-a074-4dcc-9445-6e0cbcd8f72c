{"ast": null, "code": "import * as React from 'react';\nvar OrderContext = /*#__PURE__*/React.createContext(null);\nexport default OrderContext;", "map": {"version": 3, "names": ["React", "OrderContext", "createContext"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@rc-component/portal/es/Context.js"], "sourcesContent": ["import * as React from 'react';\nvar OrderContext = /*#__PURE__*/React.createContext(null);\nexport default OrderContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACzD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}