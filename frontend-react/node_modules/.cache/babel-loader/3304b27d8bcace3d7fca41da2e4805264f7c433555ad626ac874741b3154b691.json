{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TwitchOutlinedSvg from \"@ant-design/icons-svg/es/asn/TwitchOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TwitchOutlined = function TwitchOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TwitchOutlinedSvg\n  }));\n};\n\n/**![twitch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTY2LjEzIDExMkwxMTQgMjUxLjE3djU1Ni40NmgxOTEuMlY5MTJoMTA0LjRsMTA0LjIzLTEwNC40aDE1Ni41TDg3OSA1OTlWMTEyem02OS41NCA2OS41SDgwOS41djM4Mi42M0w2ODcuNzcgNjg1Ljg3SDQ5Ni41TDM5Mi4yNyA3OTAuMVY2ODUuODdoLTE1Ni42ek00MjcgNTI5LjRoNjkuNVYzMjAuNzNINDI3em0xOTEuMTcgMGg2OS41M1YzMjAuNzNoLTY5LjUzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TwitchOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TwitchOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TwitchOutlinedSvg", "AntdIcon", "TwitchOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/TwitchOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TwitchOutlinedSvg from \"@ant-design/icons-svg/es/asn/TwitchOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TwitchOutlined = function TwitchOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TwitchOutlinedSvg\n  }));\n};\n\n/**![twitch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTY2LjEzIDExMkwxMTQgMjUxLjE3djU1Ni40NmgxOTEuMlY5MTJoMTA0LjRsMTA0LjIzLTEwNC40aDE1Ni41TDg3OSA1OTlWMTEyem02OS41NCA2OS41SDgwOS41djM4Mi42M0w2ODcuNzcgNjg1Ljg3SDQ5Ni41TDM5Mi4yNyA3OTAuMVY2ODUuODdoLTE1Ni42ek00MjcgNTI5LjRoNjkuNVYzMjAuNzNINDI3em0xOTEuMTcgMGg2OS41M1YzMjAuNzNoLTY5LjUzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TwitchOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TwitchOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}