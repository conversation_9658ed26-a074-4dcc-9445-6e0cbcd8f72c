{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { collectScroller, getVisibleArea, getWin, toNum } from \"../util\";\nfunction getUnitOffset(size) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var offsetStr = \"\".concat(offset);\n  var cells = offsetStr.match(/^(.*)\\%$/);\n  if (cells) {\n    return size * (parseFloat(cells[1]) / 100);\n  }\n  return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n  var _ref = offset || [],\n    _ref2 = _slicedToArray(_ref, 2),\n    offsetX = _ref2[0],\n    offsetY = _ref2[1];\n  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];\n}\nfunction splitPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return [points[0], points[1]];\n}\nfunction getAlignPoint(rect, points) {\n  var topBottom = points[0];\n  var leftRight = points[1];\n  var x;\n  var y;\n\n  // Top & Bottom\n  if (topBottom === 't') {\n    y = rect.y;\n  } else if (topBottom === 'b') {\n    y = rect.y + rect.height;\n  } else {\n    y = rect.y + rect.height / 2;\n  }\n\n  // Left & Right\n  if (leftRight === 'l') {\n    x = rect.x;\n  } else if (leftRight === 'r') {\n    x = rect.x + rect.width;\n  } else {\n    x = rect.x + rect.width / 2;\n  }\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction reversePoints(points, index) {\n  var reverseMap = {\n    t: 'b',\n    b: 't',\n    l: 'r',\n    r: 'l'\n  };\n  return points.map(function (point, i) {\n    if (i === index) {\n      return reverseMap[point] || 'c';\n    }\n    return point;\n  }).join('');\n}\nexport default function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n  var _React$useState = React.useState({\n      ready: false,\n      offsetX: 0,\n      offsetY: 0,\n      offsetR: 0,\n      offsetB: 0,\n      arrowX: 0,\n      arrowY: 0,\n      scaleX: 1,\n      scaleY: 1,\n      align: builtinPlacements[placement] || {}\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    offsetInfo = _React$useState2[0],\n    setOffsetInfo = _React$useState2[1];\n  var alignCountRef = React.useRef(0);\n  var scrollerList = React.useMemo(function () {\n    if (!popupEle) {\n      return [];\n    }\n    return collectScroller(popupEle);\n  }, [popupEle]);\n\n  // ========================= Flip ==========================\n  // We will memo flip info.\n  // If size change to make flip, it will memo the flip info and use it in next align.\n  var prevFlipRef = React.useRef({});\n  var resetFlipCache = function resetFlipCache() {\n    prevFlipRef.current = {};\n  };\n  if (!open) {\n    resetFlipCache();\n  }\n\n  // ========================= Align =========================\n  var onAlign = useEvent(function () {\n    if (popupEle && target && open) {\n      var _popupElement$parentE, _popupRect$x, _popupRect$y, _popupElement$parentE2;\n      var popupElement = popupEle;\n      var doc = popupElement.ownerDocument;\n      var win = getWin(popupElement);\n      var _win$getComputedStyle = win.getComputedStyle(popupElement),\n        popupPosition = _win$getComputedStyle.position;\n      var originLeft = popupElement.style.left;\n      var originTop = popupElement.style.top;\n      var originRight = popupElement.style.right;\n      var originBottom = popupElement.style.bottom;\n      var originOverflow = popupElement.style.overflow;\n\n      // Placement\n      var placementInfo = _objectSpread(_objectSpread({}, builtinPlacements[placement]), popupAlign);\n\n      // placeholder element\n      var placeholderElement = doc.createElement('div');\n      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);\n      placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n      placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n      placeholderElement.style.position = popupPosition;\n      placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n      placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n\n      // Reset first\n      popupElement.style.left = '0';\n      popupElement.style.top = '0';\n      popupElement.style.right = 'auto';\n      popupElement.style.bottom = 'auto';\n      popupElement.style.overflow = 'hidden';\n\n      // Calculate align style, we should consider `transform` case\n      var targetRect;\n      if (Array.isArray(target)) {\n        targetRect = {\n          x: target[0],\n          y: target[1],\n          width: 0,\n          height: 0\n        };\n      } else {\n        var _rect$x, _rect$y;\n        var rect = target.getBoundingClientRect();\n        rect.x = (_rect$x = rect.x) !== null && _rect$x !== void 0 ? _rect$x : rect.left;\n        rect.y = (_rect$y = rect.y) !== null && _rect$y !== void 0 ? _rect$y : rect.top;\n        targetRect = {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        };\n      }\n      var popupRect = popupElement.getBoundingClientRect();\n      var _win$getComputedStyle2 = win.getComputedStyle(popupElement),\n        height = _win$getComputedStyle2.height,\n        width = _win$getComputedStyle2.width;\n      popupRect.x = (_popupRect$x = popupRect.x) !== null && _popupRect$x !== void 0 ? _popupRect$x : popupRect.left;\n      popupRect.y = (_popupRect$y = popupRect.y) !== null && _popupRect$y !== void 0 ? _popupRect$y : popupRect.top;\n      var _doc$documentElement = doc.documentElement,\n        clientWidth = _doc$documentElement.clientWidth,\n        clientHeight = _doc$documentElement.clientHeight,\n        scrollWidth = _doc$documentElement.scrollWidth,\n        scrollHeight = _doc$documentElement.scrollHeight,\n        scrollTop = _doc$documentElement.scrollTop,\n        scrollLeft = _doc$documentElement.scrollLeft;\n      var popupHeight = popupRect.height;\n      var popupWidth = popupRect.width;\n      var targetHeight = targetRect.height;\n      var targetWidth = targetRect.width;\n\n      // Get bounding of visible area\n      var visibleRegion = {\n        left: 0,\n        top: 0,\n        right: clientWidth,\n        bottom: clientHeight\n      };\n      var scrollRegion = {\n        left: -scrollLeft,\n        top: -scrollTop,\n        right: scrollWidth - scrollLeft,\n        bottom: scrollHeight - scrollTop\n      };\n      var htmlRegion = placementInfo.htmlRegion;\n      var VISIBLE = 'visible';\n      var VISIBLE_FIRST = 'visibleFirst';\n      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {\n        htmlRegion = VISIBLE;\n      }\n      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);\n      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);\n      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n\n      // When set to `visibleFirst`,\n      // the check `adjust` logic will use `visibleRegion` for check first.\n      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n\n      // Record right & bottom align data\n      popupElement.style.left = 'auto';\n      popupElement.style.top = 'auto';\n      popupElement.style.right = '0';\n      popupElement.style.bottom = '0';\n      var popupMirrorRect = popupElement.getBoundingClientRect();\n\n      // Reset back\n      popupElement.style.left = originLeft;\n      popupElement.style.top = originTop;\n      popupElement.style.right = originRight;\n      popupElement.style.bottom = originBottom;\n      popupElement.style.overflow = originOverflow;\n      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);\n\n      // Calculate scale\n      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n\n      // No need to align since it's not visible in view\n      if (_scaleX === 0 || _scaleY === 0 || isDOM(target) && !isVisible(target)) {\n        return;\n      }\n\n      // Offset\n      var offset = placementInfo.offset,\n        targetOffset = placementInfo.targetOffset;\n      var _getNumberOffset = getNumberOffset(popupRect, offset),\n        _getNumberOffset2 = _slicedToArray(_getNumberOffset, 2),\n        popupOffsetX = _getNumberOffset2[0],\n        popupOffsetY = _getNumberOffset2[1];\n      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),\n        _getNumberOffset4 = _slicedToArray(_getNumberOffset3, 2),\n        targetOffsetX = _getNumberOffset4[0],\n        targetOffsetY = _getNumberOffset4[1];\n      targetRect.x -= targetOffsetX;\n      targetRect.y -= targetOffsetY;\n\n      // Points\n      var _ref3 = placementInfo.points || [],\n        _ref4 = _slicedToArray(_ref3, 2),\n        popupPoint = _ref4[0],\n        targetPoint = _ref4[1];\n      var targetPoints = splitPoints(targetPoint);\n      var popupPoints = splitPoints(popupPoint);\n      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n\n      // Real align info may not same as origin one\n      var nextAlignInfo = _objectSpread({}, placementInfo);\n\n      // Next Offset\n      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n\n      // ============== Intersection ===============\n      // Get area by position. Used for check if flip area is better\n      function getIntersectionVisibleArea(offsetX, offsetY) {\n        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n        var l = popupRect.x + offsetX;\n        var t = popupRect.y + offsetY;\n        var r = l + popupWidth;\n        var b = t + popupHeight;\n        var visibleL = Math.max(l, area.left);\n        var visibleT = Math.max(t, area.top);\n        var visibleR = Math.min(r, area.right);\n        var visibleB = Math.min(b, area.bottom);\n        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n      }\n      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n\n      // As `visibleFirst`, we prepare this for check\n      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n\n      // ========================== Overflow ===========================\n      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);\n      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);\n      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);\n      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);\n      var overflow = placementInfo.overflow || {};\n      var adjustX = overflow.adjustX,\n        adjustY = overflow.adjustY,\n        shiftX = overflow.shiftX,\n        shiftY = overflow.shiftY;\n      var supportAdjust = function supportAdjust(val) {\n        if (typeof val === 'boolean') {\n          return val;\n        }\n        return val >= 0;\n      };\n\n      // Prepare position\n      var nextPopupY;\n      var nextPopupBottom;\n      var nextPopupX;\n      var nextPopupRight;\n      function syncNextPopupPosition() {\n        nextPopupY = popupRect.y + nextOffsetY;\n        nextPopupBottom = nextPopupY + popupHeight;\n        nextPopupX = popupRect.x + nextOffsetX;\n        nextPopupRight = nextPopupX + popupWidth;\n      }\n      syncNextPopupPosition();\n\n      // >>>>>>>>>> Top & Bottom\n      var needAdjustY = supportAdjust(adjustY);\n      var sameTB = popupPoints[0] === targetPoints[0];\n\n      // Bottom to Top\n      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n        var tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          tmpNextOffsetY -= popupHeight - targetHeight;\n        } else {\n          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n        }\n        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.bt = true;\n          nextOffsetY = tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.bt = false;\n        }\n      }\n\n      // Top to Bottom\n      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n        var _tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          _tmpNextOffsetY += popupHeight - targetHeight;\n        } else {\n          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n        }\n        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.tb = true;\n          nextOffsetY = _tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.tb = false;\n        }\n      }\n\n      // >>>>>>>>>> Left & Right\n      var needAdjustX = supportAdjust(adjustX);\n\n      // >>>>> Flip\n      var sameLR = popupPoints[1] === targetPoints[1];\n\n      // Right to Left\n      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n        var tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          tmpNextOffsetX -= popupWidth - targetWidth;\n        } else {\n          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n        }\n        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.rl = true;\n          nextOffsetX = tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.rl = false;\n        }\n      }\n\n      // Left to Right\n      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n        var _tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          _tmpNextOffsetX += popupWidth - targetWidth;\n        } else {\n          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n        }\n        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.lr = true;\n          nextOffsetX = _tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.lr = false;\n        }\n      }\n\n      // ============================ Shift ============================\n      syncNextPopupPosition();\n      var numShiftX = shiftX === true ? 0 : shiftX;\n      if (typeof numShiftX === 'number') {\n        // Left\n        if (nextPopupX < visibleRegionArea.left) {\n          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n          }\n        }\n\n        // Right\n        if (nextPopupRight > visibleRegionArea.right) {\n          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n          if (targetRect.x > visibleRegionArea.right - numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n          }\n        }\n      }\n      var numShiftY = shiftY === true ? 0 : shiftY;\n      if (typeof numShiftY === 'number') {\n        // Top\n        if (nextPopupY < visibleRegionArea.top) {\n          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n\n          // When target if far away from visible area\n          // Stop shift\n          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n          }\n        }\n\n        // Bottom\n        if (nextPopupBottom > visibleRegionArea.bottom) {\n          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n          }\n        }\n      }\n\n      // ============================ Arrow ============================\n      // Arrow center align\n      var popupLeft = popupRect.x + nextOffsetX;\n      var popupRight = popupLeft + popupWidth;\n      var popupTop = popupRect.y + nextOffsetY;\n      var popupBottom = popupTop + popupHeight;\n      var targetLeft = targetRect.x;\n      var targetRight = targetLeft + targetWidth;\n      var targetTop = targetRect.y;\n      var targetBottom = targetTop + targetHeight;\n      var maxLeft = Math.max(popupLeft, targetLeft);\n      var minRight = Math.min(popupRight, targetRight);\n      var xCenter = (maxLeft + minRight) / 2;\n      var nextArrowX = xCenter - popupLeft;\n      var maxTop = Math.max(popupTop, targetTop);\n      var minBottom = Math.min(popupBottom, targetBottom);\n      var yCenter = (maxTop + minBottom) / 2;\n      var nextArrowY = yCenter - popupTop;\n      onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);\n\n      // Additional calculate right & bottom position\n      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n      if (_scaleX === 1) {\n        nextOffsetX = Math.round(nextOffsetX);\n        offsetX4Right = Math.round(offsetX4Right);\n      }\n      if (_scaleY === 1) {\n        nextOffsetY = Math.round(nextOffsetY);\n        offsetY4Bottom = Math.round(offsetY4Bottom);\n      }\n      var nextOffsetInfo = {\n        ready: true,\n        offsetX: nextOffsetX / _scaleX,\n        offsetY: nextOffsetY / _scaleY,\n        offsetR: offsetX4Right / _scaleX,\n        offsetB: offsetY4Bottom / _scaleY,\n        arrowX: nextArrowX / _scaleX,\n        arrowY: nextArrowY / _scaleY,\n        scaleX: _scaleX,\n        scaleY: _scaleY,\n        align: nextAlignInfo\n      };\n      setOffsetInfo(nextOffsetInfo);\n    }\n  });\n  var triggerAlign = function triggerAlign() {\n    alignCountRef.current += 1;\n    var id = alignCountRef.current;\n\n    // Merge all align requirement into one frame\n    Promise.resolve().then(function () {\n      if (alignCountRef.current === id) {\n        onAlign();\n      }\n    });\n  };\n\n  // Reset ready status when placement & open changed\n  var resetReady = function resetReady() {\n    setOffsetInfo(function (ori) {\n      return _objectSpread(_objectSpread({}, ori), {}, {\n        ready: false\n      });\n    });\n  };\n  useLayoutEffect(resetReady, [placement]);\n  useLayoutEffect(function () {\n    if (!open) {\n      resetReady();\n    }\n  }, [open]);\n  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "isDOM", "isVisible", "useEvent", "useLayoutEffect", "React", "collectScroller", "getVisibleArea", "getWin", "to<PERSON>um", "getUnitOffset", "size", "offset", "arguments", "length", "undefined", "offsetStr", "concat", "cells", "match", "parseFloat", "getNumberOffset", "rect", "_ref", "_ref2", "offsetX", "offsetY", "width", "height", "splitPoints", "points", "getAlignPoint", "topBottom", "leftRight", "x", "y", "reversePoints", "index", "reverseMap", "t", "b", "l", "r", "map", "point", "i", "join", "useAlign", "open", "popup<PERSON>le", "target", "placement", "builtinPlacements", "popupAlign", "onPopupAlign", "_React$useState", "useState", "ready", "offsetR", "offsetB", "arrowX", "arrowY", "scaleX", "scaleY", "align", "_React$useState2", "offsetInfo", "setOffsetInfo", "alignCountRef", "useRef", "scrollerList", "useMemo", "prevFlipRef", "resetFlipCache", "current", "onAlign", "_popupElement$parentE", "_popupRect$x", "_popupRect$y", "_popupElement$parentE2", "popupElement", "doc", "ownerDocument", "win", "_win$getComputedStyle", "getComputedStyle", "popupPosition", "position", "originLeft", "style", "left", "originTop", "top", "originRight", "right", "originBottom", "bottom", "originOverflow", "overflow", "placementInfo", "placeholderElement", "createElement", "parentElement", "append<PERSON><PERSON><PERSON>", "offsetLeft", "offsetTop", "offsetHeight", "offsetWidth", "targetRect", "Array", "isArray", "_rect$x", "_rect$y", "getBoundingClientRect", "popupRect", "_win$getComputedStyle2", "_doc$documentElement", "documentElement", "clientWidth", "clientHeight", "scrollWidth", "scrollHeight", "scrollTop", "scrollLeft", "popupHeight", "popup<PERSON><PERSON><PERSON>", "targetHeight", "targetWidth", "visibleRegion", "scrollRegion", "htmlRegion", "VISIBLE", "VISIBLE_FIRST", "isVisibleFirst", "scrollRegionArea", "visibleRegionArea", "visibleArea", "adjustCheckVisibleArea", "popupMirrorRect", "<PERSON><PERSON><PERSON><PERSON>", "_scaleX", "Math", "round", "_scaleY", "targetOffset", "_getNumberOffset", "_getNumberOffset2", "popupOffsetX", "popupOffsetY", "_getNumberOffset3", "_getNumberOffset4", "targetOffsetX", "targetOffsetY", "_ref3", "_ref4", "popupPoint", "targetPoint", "targetPoints", "popupPoints", "targetAlignPoint", "popupAlignPoint", "nextAlignInfo", "nextOffsetX", "nextOffsetY", "getIntersectionVisibleArea", "area", "visibleL", "max", "visibleT", "visibleR", "min", "visibleB", "originIntersectionVisibleArea", "originIntersectionRecommendArea", "targetAlignPointTL", "popupAlignPointTL", "targetAlignPointBR", "popupAlignPointBR", "adjustX", "adjustY", "shiftX", "shiftY", "supportAdjust", "val", "nextPopupY", "nextPopupBottom", "nextPopupX", "nextPopupRight", "syncNextPopupPosition", "needAdjustY", "sameTB", "bt", "tmpNextOffsetY", "newVisibleArea", "newVisibleRecommendArea", "tb", "_tmpNextOffsetY", "_newVisibleArea", "_newVisibleRecommendArea", "needAdjustX", "sameLR", "rl", "tmpNextOffsetX", "_newVisibleArea2", "_newVisibleRecommendArea2", "lr", "_tmpNextOffsetX", "_newVisibleArea3", "_newVisibleRecommendArea3", "numShiftX", "numShiftY", "popupLeft", "popupRight", "popupTop", "popupBottom", "targetLeft", "targetRight", "targetTop", "targetBottom", "maxLeft", "minRight", "xCenter", "nextArrowX", "maxTop", "minBottom", "yCenter", "nextArrowY", "offsetX4Right", "offsetY4Bottom", "nextOffsetInfo", "triggerAlign", "id", "Promise", "resolve", "then", "resetReady", "ori"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@rc-component/trigger/es/hooks/useAlign.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { collectScroller, getVisibleArea, getWin, toNum } from \"../util\";\nfunction getUnitOffset(size) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var offsetStr = \"\".concat(offset);\n  var cells = offsetStr.match(/^(.*)\\%$/);\n  if (cells) {\n    return size * (parseFloat(cells[1]) / 100);\n  }\n  return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n  var _ref = offset || [],\n    _ref2 = _slicedToArray(_ref, 2),\n    offsetX = _ref2[0],\n    offsetY = _ref2[1];\n  return [getUnitOffset(rect.width, offsetX), getUnitOffset(rect.height, offsetY)];\n}\nfunction splitPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  return [points[0], points[1]];\n}\nfunction getAlignPoint(rect, points) {\n  var topBottom = points[0];\n  var leftRight = points[1];\n  var x;\n  var y;\n\n  // Top & Bottom\n  if (topBottom === 't') {\n    y = rect.y;\n  } else if (topBottom === 'b') {\n    y = rect.y + rect.height;\n  } else {\n    y = rect.y + rect.height / 2;\n  }\n\n  // Left & Right\n  if (leftRight === 'l') {\n    x = rect.x;\n  } else if (leftRight === 'r') {\n    x = rect.x + rect.width;\n  } else {\n    x = rect.x + rect.width / 2;\n  }\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction reversePoints(points, index) {\n  var reverseMap = {\n    t: 'b',\n    b: 't',\n    l: 'r',\n    r: 'l'\n  };\n  return points.map(function (point, i) {\n    if (i === index) {\n      return reverseMap[point] || 'c';\n    }\n    return point;\n  }).join('');\n}\nexport default function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n  var _React$useState = React.useState({\n      ready: false,\n      offsetX: 0,\n      offsetY: 0,\n      offsetR: 0,\n      offsetB: 0,\n      arrowX: 0,\n      arrowY: 0,\n      scaleX: 1,\n      scaleY: 1,\n      align: builtinPlacements[placement] || {}\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    offsetInfo = _React$useState2[0],\n    setOffsetInfo = _React$useState2[1];\n  var alignCountRef = React.useRef(0);\n  var scrollerList = React.useMemo(function () {\n    if (!popupEle) {\n      return [];\n    }\n    return collectScroller(popupEle);\n  }, [popupEle]);\n\n  // ========================= Flip ==========================\n  // We will memo flip info.\n  // If size change to make flip, it will memo the flip info and use it in next align.\n  var prevFlipRef = React.useRef({});\n  var resetFlipCache = function resetFlipCache() {\n    prevFlipRef.current = {};\n  };\n  if (!open) {\n    resetFlipCache();\n  }\n\n  // ========================= Align =========================\n  var onAlign = useEvent(function () {\n    if (popupEle && target && open) {\n      var _popupElement$parentE, _popupRect$x, _popupRect$y, _popupElement$parentE2;\n      var popupElement = popupEle;\n      var doc = popupElement.ownerDocument;\n      var win = getWin(popupElement);\n      var _win$getComputedStyle = win.getComputedStyle(popupElement),\n        popupPosition = _win$getComputedStyle.position;\n      var originLeft = popupElement.style.left;\n      var originTop = popupElement.style.top;\n      var originRight = popupElement.style.right;\n      var originBottom = popupElement.style.bottom;\n      var originOverflow = popupElement.style.overflow;\n\n      // Placement\n      var placementInfo = _objectSpread(_objectSpread({}, builtinPlacements[placement]), popupAlign);\n\n      // placeholder element\n      var placeholderElement = doc.createElement('div');\n      (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);\n      placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n      placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n      placeholderElement.style.position = popupPosition;\n      placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n      placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n\n      // Reset first\n      popupElement.style.left = '0';\n      popupElement.style.top = '0';\n      popupElement.style.right = 'auto';\n      popupElement.style.bottom = 'auto';\n      popupElement.style.overflow = 'hidden';\n\n      // Calculate align style, we should consider `transform` case\n      var targetRect;\n      if (Array.isArray(target)) {\n        targetRect = {\n          x: target[0],\n          y: target[1],\n          width: 0,\n          height: 0\n        };\n      } else {\n        var _rect$x, _rect$y;\n        var rect = target.getBoundingClientRect();\n        rect.x = (_rect$x = rect.x) !== null && _rect$x !== void 0 ? _rect$x : rect.left;\n        rect.y = (_rect$y = rect.y) !== null && _rect$y !== void 0 ? _rect$y : rect.top;\n        targetRect = {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        };\n      }\n      var popupRect = popupElement.getBoundingClientRect();\n      var _win$getComputedStyle2 = win.getComputedStyle(popupElement),\n        height = _win$getComputedStyle2.height,\n        width = _win$getComputedStyle2.width;\n      popupRect.x = (_popupRect$x = popupRect.x) !== null && _popupRect$x !== void 0 ? _popupRect$x : popupRect.left;\n      popupRect.y = (_popupRect$y = popupRect.y) !== null && _popupRect$y !== void 0 ? _popupRect$y : popupRect.top;\n      var _doc$documentElement = doc.documentElement,\n        clientWidth = _doc$documentElement.clientWidth,\n        clientHeight = _doc$documentElement.clientHeight,\n        scrollWidth = _doc$documentElement.scrollWidth,\n        scrollHeight = _doc$documentElement.scrollHeight,\n        scrollTop = _doc$documentElement.scrollTop,\n        scrollLeft = _doc$documentElement.scrollLeft;\n      var popupHeight = popupRect.height;\n      var popupWidth = popupRect.width;\n      var targetHeight = targetRect.height;\n      var targetWidth = targetRect.width;\n\n      // Get bounding of visible area\n      var visibleRegion = {\n        left: 0,\n        top: 0,\n        right: clientWidth,\n        bottom: clientHeight\n      };\n      var scrollRegion = {\n        left: -scrollLeft,\n        top: -scrollTop,\n        right: scrollWidth - scrollLeft,\n        bottom: scrollHeight - scrollTop\n      };\n      var htmlRegion = placementInfo.htmlRegion;\n      var VISIBLE = 'visible';\n      var VISIBLE_FIRST = 'visibleFirst';\n      if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {\n        htmlRegion = VISIBLE;\n      }\n      var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n      var scrollRegionArea = getVisibleArea(scrollRegion, scrollerList);\n      var visibleRegionArea = getVisibleArea(visibleRegion, scrollerList);\n      var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n\n      // When set to `visibleFirst`,\n      // the check `adjust` logic will use `visibleRegion` for check first.\n      var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n\n      // Record right & bottom align data\n      popupElement.style.left = 'auto';\n      popupElement.style.top = 'auto';\n      popupElement.style.right = '0';\n      popupElement.style.bottom = '0';\n      var popupMirrorRect = popupElement.getBoundingClientRect();\n\n      // Reset back\n      popupElement.style.left = originLeft;\n      popupElement.style.top = originTop;\n      popupElement.style.right = originRight;\n      popupElement.style.bottom = originBottom;\n      popupElement.style.overflow = originOverflow;\n      (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);\n\n      // Calculate scale\n      var _scaleX = toNum(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n      var _scaleY = toNum(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n\n      // No need to align since it's not visible in view\n      if (_scaleX === 0 || _scaleY === 0 || isDOM(target) && !isVisible(target)) {\n        return;\n      }\n\n      // Offset\n      var offset = placementInfo.offset,\n        targetOffset = placementInfo.targetOffset;\n      var _getNumberOffset = getNumberOffset(popupRect, offset),\n        _getNumberOffset2 = _slicedToArray(_getNumberOffset, 2),\n        popupOffsetX = _getNumberOffset2[0],\n        popupOffsetY = _getNumberOffset2[1];\n      var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset),\n        _getNumberOffset4 = _slicedToArray(_getNumberOffset3, 2),\n        targetOffsetX = _getNumberOffset4[0],\n        targetOffsetY = _getNumberOffset4[1];\n      targetRect.x -= targetOffsetX;\n      targetRect.y -= targetOffsetY;\n\n      // Points\n      var _ref3 = placementInfo.points || [],\n        _ref4 = _slicedToArray(_ref3, 2),\n        popupPoint = _ref4[0],\n        targetPoint = _ref4[1];\n      var targetPoints = splitPoints(targetPoint);\n      var popupPoints = splitPoints(popupPoint);\n      var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n      var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n\n      // Real align info may not same as origin one\n      var nextAlignInfo = _objectSpread({}, placementInfo);\n\n      // Next Offset\n      var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n      var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n\n      // ============== Intersection ===============\n      // Get area by position. Used for check if flip area is better\n      function getIntersectionVisibleArea(offsetX, offsetY) {\n        var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n        var l = popupRect.x + offsetX;\n        var t = popupRect.y + offsetY;\n        var r = l + popupWidth;\n        var b = t + popupHeight;\n        var visibleL = Math.max(l, area.left);\n        var visibleT = Math.max(t, area.top);\n        var visibleR = Math.min(r, area.right);\n        var visibleB = Math.min(b, area.bottom);\n        return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n      }\n      var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n\n      // As `visibleFirst`, we prepare this for check\n      var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n\n      // ========================== Overflow ===========================\n      var targetAlignPointTL = getAlignPoint(targetRect, ['t', 'l']);\n      var popupAlignPointTL = getAlignPoint(popupRect, ['t', 'l']);\n      var targetAlignPointBR = getAlignPoint(targetRect, ['b', 'r']);\n      var popupAlignPointBR = getAlignPoint(popupRect, ['b', 'r']);\n      var overflow = placementInfo.overflow || {};\n      var adjustX = overflow.adjustX,\n        adjustY = overflow.adjustY,\n        shiftX = overflow.shiftX,\n        shiftY = overflow.shiftY;\n      var supportAdjust = function supportAdjust(val) {\n        if (typeof val === 'boolean') {\n          return val;\n        }\n        return val >= 0;\n      };\n\n      // Prepare position\n      var nextPopupY;\n      var nextPopupBottom;\n      var nextPopupX;\n      var nextPopupRight;\n      function syncNextPopupPosition() {\n        nextPopupY = popupRect.y + nextOffsetY;\n        nextPopupBottom = nextPopupY + popupHeight;\n        nextPopupX = popupRect.x + nextOffsetX;\n        nextPopupRight = nextPopupX + popupWidth;\n      }\n      syncNextPopupPosition();\n\n      // >>>>>>>>>> Top & Bottom\n      var needAdjustY = supportAdjust(adjustY);\n      var sameTB = popupPoints[0] === targetPoints[0];\n\n      // Bottom to Top\n      if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n        var tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          tmpNextOffsetY -= popupHeight - targetHeight;\n        } else {\n          tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n        }\n        var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n        var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.bt = true;\n          nextOffsetY = tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.bt = false;\n        }\n      }\n\n      // Top to Bottom\n      if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n        var _tmpNextOffsetY = nextOffsetY;\n        if (sameTB) {\n          _tmpNextOffsetY += popupHeight - targetHeight;\n        } else {\n          _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n        }\n        var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n        var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.tb = true;\n          nextOffsetY = _tmpNextOffsetY;\n          popupOffsetY = -popupOffsetY;\n          nextAlignInfo.points = [reversePoints(popupPoints, 0), reversePoints(targetPoints, 0)];\n        } else {\n          prevFlipRef.current.tb = false;\n        }\n      }\n\n      // >>>>>>>>>> Left & Right\n      var needAdjustX = supportAdjust(adjustX);\n\n      // >>>>> Flip\n      var sameLR = popupPoints[1] === targetPoints[1];\n\n      // Right to Left\n      if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n        var tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          tmpNextOffsetX -= popupWidth - targetWidth;\n        } else {\n          tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n        }\n        var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.rl = true;\n          nextOffsetX = tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.rl = false;\n        }\n      }\n\n      // Left to Right\n      if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n        var _tmpNextOffsetX = nextOffsetX;\n        if (sameLR) {\n          _tmpNextOffsetX += popupWidth - targetWidth;\n        } else {\n          _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n        }\n        var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n        var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n        if (\n        // Of course use larger one\n        _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst ||\n        // Choose recommend one\n        _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n          prevFlipRef.current.lr = true;\n          nextOffsetX = _tmpNextOffsetX;\n          popupOffsetX = -popupOffsetX;\n          nextAlignInfo.points = [reversePoints(popupPoints, 1), reversePoints(targetPoints, 1)];\n        } else {\n          prevFlipRef.current.lr = false;\n        }\n      }\n\n      // ============================ Shift ============================\n      syncNextPopupPosition();\n      var numShiftX = shiftX === true ? 0 : shiftX;\n      if (typeof numShiftX === 'number') {\n        // Left\n        if (nextPopupX < visibleRegionArea.left) {\n          nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n          if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n          }\n        }\n\n        // Right\n        if (nextPopupRight > visibleRegionArea.right) {\n          nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n          if (targetRect.x > visibleRegionArea.right - numShiftX) {\n            nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n          }\n        }\n      }\n      var numShiftY = shiftY === true ? 0 : shiftY;\n      if (typeof numShiftY === 'number') {\n        // Top\n        if (nextPopupY < visibleRegionArea.top) {\n          nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n\n          // When target if far away from visible area\n          // Stop shift\n          if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n          }\n        }\n\n        // Bottom\n        if (nextPopupBottom > visibleRegionArea.bottom) {\n          nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n          if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n            nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n          }\n        }\n      }\n\n      // ============================ Arrow ============================\n      // Arrow center align\n      var popupLeft = popupRect.x + nextOffsetX;\n      var popupRight = popupLeft + popupWidth;\n      var popupTop = popupRect.y + nextOffsetY;\n      var popupBottom = popupTop + popupHeight;\n      var targetLeft = targetRect.x;\n      var targetRight = targetLeft + targetWidth;\n      var targetTop = targetRect.y;\n      var targetBottom = targetTop + targetHeight;\n      var maxLeft = Math.max(popupLeft, targetLeft);\n      var minRight = Math.min(popupRight, targetRight);\n      var xCenter = (maxLeft + minRight) / 2;\n      var nextArrowX = xCenter - popupLeft;\n      var maxTop = Math.max(popupTop, targetTop);\n      var minBottom = Math.min(popupBottom, targetBottom);\n      var yCenter = (maxTop + minBottom) / 2;\n      var nextArrowY = yCenter - popupTop;\n      onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);\n\n      // Additional calculate right & bottom position\n      var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n      var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n      if (_scaleX === 1) {\n        nextOffsetX = Math.round(nextOffsetX);\n        offsetX4Right = Math.round(offsetX4Right);\n      }\n      if (_scaleY === 1) {\n        nextOffsetY = Math.round(nextOffsetY);\n        offsetY4Bottom = Math.round(offsetY4Bottom);\n      }\n      var nextOffsetInfo = {\n        ready: true,\n        offsetX: nextOffsetX / _scaleX,\n        offsetY: nextOffsetY / _scaleY,\n        offsetR: offsetX4Right / _scaleX,\n        offsetB: offsetY4Bottom / _scaleY,\n        arrowX: nextArrowX / _scaleX,\n        arrowY: nextArrowY / _scaleY,\n        scaleX: _scaleX,\n        scaleY: _scaleY,\n        align: nextAlignInfo\n      };\n      setOffsetInfo(nextOffsetInfo);\n    }\n  });\n  var triggerAlign = function triggerAlign() {\n    alignCountRef.current += 1;\n    var id = alignCountRef.current;\n\n    // Merge all align requirement into one frame\n    Promise.resolve().then(function () {\n      if (alignCountRef.current === id) {\n        onAlign();\n      }\n    });\n  };\n\n  // Reset ready status when placement & open changed\n  var resetReady = function resetReady() {\n    setOffsetInfo(function (ori) {\n      return _objectSpread(_objectSpread({}, ori), {}, {\n        ready: false\n      });\n    });\n  };\n  useLayoutEffect(resetReady, [placement]);\n  useLayoutEffect(function () {\n    if (!open) {\n      resetReady();\n    }\n  }, [open]);\n  return [offsetInfo.ready, offsetInfo.offsetX, offsetInfo.offsetY, offsetInfo.offsetR, offsetInfo.offsetB, offsetInfo.arrowX, offsetInfo.arrowY, offsetInfo.scaleX, offsetInfo.scaleY, offsetInfo.align, triggerAlign];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,KAAK,QAAQ,4BAA4B;AAClD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,cAAc,EAAEC,MAAM,EAAEC,KAAK,QAAQ,SAAS;AACxE,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIG,SAAS,GAAG,EAAE,CAACC,MAAM,CAACL,MAAM,CAAC;EACjC,IAAIM,KAAK,GAAGF,SAAS,CAACG,KAAK,CAAC,UAAU,CAAC;EACvC,IAAID,KAAK,EAAE;IACT,OAAOP,IAAI,IAAIS,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EAC5C;EACA,OAAOE,UAAU,CAACJ,SAAS,CAAC;AAC9B;AACA,SAASK,eAAeA,CAACC,IAAI,EAAEV,MAAM,EAAE;EACrC,IAAIW,IAAI,GAAGX,MAAM,IAAI,EAAE;IACrBY,KAAK,GAAGxB,cAAc,CAACuB,IAAI,EAAE,CAAC,CAAC;IAC/BE,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;IAClBE,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC;EACpB,OAAO,CAACd,aAAa,CAACY,IAAI,CAACK,KAAK,EAAEF,OAAO,CAAC,EAAEf,aAAa,CAACY,IAAI,CAACM,MAAM,EAAEF,OAAO,CAAC,CAAC;AAClF;AACA,SAASG,WAAWA,CAAA,EAAG;EACrB,IAAIC,MAAM,GAAGjB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,OAAO,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,SAASC,aAAaA,CAACT,IAAI,EAAEQ,MAAM,EAAE;EACnC,IAAIE,SAAS,GAAGF,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIG,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC;EACzB,IAAII,CAAC;EACL,IAAIC,CAAC;;EAEL;EACA,IAAIH,SAAS,KAAK,GAAG,EAAE;IACrBG,CAAC,GAAGb,IAAI,CAACa,CAAC;EACZ,CAAC,MAAM,IAAIH,SAAS,KAAK,GAAG,EAAE;IAC5BG,CAAC,GAAGb,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACM,MAAM;EAC1B,CAAC,MAAM;IACLO,CAAC,GAAGb,IAAI,CAACa,CAAC,GAAGb,IAAI,CAACM,MAAM,GAAG,CAAC;EAC9B;;EAEA;EACA,IAAIK,SAAS,KAAK,GAAG,EAAE;IACrBC,CAAC,GAAGZ,IAAI,CAACY,CAAC;EACZ,CAAC,MAAM,IAAID,SAAS,KAAK,GAAG,EAAE;IAC5BC,CAAC,GAAGZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACK,KAAK;EACzB,CAAC,MAAM;IACLO,CAAC,GAAGZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACK,KAAK,GAAG,CAAC;EAC7B;EACA,OAAO;IACLO,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAASC,aAAaA,CAACN,MAAM,EAAEO,KAAK,EAAE;EACpC,IAAIC,UAAU,GAAG;IACfC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE;EACL,CAAC;EACD,OAAOZ,MAAM,CAACa,GAAG,CAAC,UAAUC,KAAK,EAAEC,CAAC,EAAE;IACpC,IAAIA,CAAC,KAAKR,KAAK,EAAE;MACf,OAAOC,UAAU,CAACM,KAAK,CAAC,IAAI,GAAG;IACjC;IACA,OAAOA,KAAK;EACd,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACb;AACA,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC/G,IAAIC,eAAe,GAAGlD,KAAK,CAACmD,QAAQ,CAAC;MACjCC,KAAK,EAAE,KAAK;MACZhC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVgC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAEZ,iBAAiB,CAACD,SAAS,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IACFc,gBAAgB,GAAGjE,cAAc,CAACuD,eAAe,EAAE,CAAC,CAAC;IACrDW,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,aAAa,GAAG/D,KAAK,CAACgE,MAAM,CAAC,CAAC,CAAC;EACnC,IAAIC,YAAY,GAAGjE,KAAK,CAACkE,OAAO,CAAC,YAAY;IAC3C,IAAI,CAACtB,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IACA,OAAO3C,eAAe,CAAC2C,QAAQ,CAAC;EAClC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA;EACA;EACA,IAAIuB,WAAW,GAAGnE,KAAK,CAACgE,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,IAAII,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CD,WAAW,CAACE,OAAO,GAAG,CAAC,CAAC;EAC1B,CAAC;EACD,IAAI,CAAC1B,IAAI,EAAE;IACTyB,cAAc,CAAC,CAAC;EAClB;;EAEA;EACA,IAAIE,OAAO,GAAGxE,QAAQ,CAAC,YAAY;IACjC,IAAI8C,QAAQ,IAAIC,MAAM,IAAIF,IAAI,EAAE;MAC9B,IAAI4B,qBAAqB,EAAEC,YAAY,EAAEC,YAAY,EAAEC,sBAAsB;MAC7E,IAAIC,YAAY,GAAG/B,QAAQ;MAC3B,IAAIgC,GAAG,GAAGD,YAAY,CAACE,aAAa;MACpC,IAAIC,GAAG,GAAG3E,MAAM,CAACwE,YAAY,CAAC;MAC9B,IAAII,qBAAqB,GAAGD,GAAG,CAACE,gBAAgB,CAACL,YAAY,CAAC;QAC5DM,aAAa,GAAGF,qBAAqB,CAACG,QAAQ;MAChD,IAAIC,UAAU,GAAGR,YAAY,CAACS,KAAK,CAACC,IAAI;MACxC,IAAIC,SAAS,GAAGX,YAAY,CAACS,KAAK,CAACG,GAAG;MACtC,IAAIC,WAAW,GAAGb,YAAY,CAACS,KAAK,CAACK,KAAK;MAC1C,IAAIC,YAAY,GAAGf,YAAY,CAACS,KAAK,CAACO,MAAM;MAC5C,IAAIC,cAAc,GAAGjB,YAAY,CAACS,KAAK,CAACS,QAAQ;;MAEhD;MACA,IAAIC,aAAa,GAAGpG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,iBAAiB,CAACD,SAAS,CAAC,CAAC,EAAEE,UAAU,CAAC;;MAE9F;MACA,IAAI+C,kBAAkB,GAAGnB,GAAG,CAACoB,aAAa,CAAC,KAAK,CAAC;MACjD,CAACzB,qBAAqB,GAAGI,YAAY,CAACsB,aAAa,MAAM,IAAI,IAAI1B,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAAC2B,WAAW,CAACH,kBAAkB,CAAC;MAC1JA,kBAAkB,CAACX,KAAK,CAACC,IAAI,GAAG,EAAE,CAACzE,MAAM,CAAC+D,YAAY,CAACwB,UAAU,EAAE,IAAI,CAAC;MACxEJ,kBAAkB,CAACX,KAAK,CAACG,GAAG,GAAG,EAAE,CAAC3E,MAAM,CAAC+D,YAAY,CAACyB,SAAS,EAAE,IAAI,CAAC;MACtEL,kBAAkB,CAACX,KAAK,CAACF,QAAQ,GAAGD,aAAa;MACjDc,kBAAkB,CAACX,KAAK,CAAC7D,MAAM,GAAG,EAAE,CAACX,MAAM,CAAC+D,YAAY,CAAC0B,YAAY,EAAE,IAAI,CAAC;MAC5EN,kBAAkB,CAACX,KAAK,CAAC9D,KAAK,GAAG,EAAE,CAACV,MAAM,CAAC+D,YAAY,CAAC2B,WAAW,EAAE,IAAI,CAAC;;MAE1E;MACA3B,YAAY,CAACS,KAAK,CAACC,IAAI,GAAG,GAAG;MAC7BV,YAAY,CAACS,KAAK,CAACG,GAAG,GAAG,GAAG;MAC5BZ,YAAY,CAACS,KAAK,CAACK,KAAK,GAAG,MAAM;MACjCd,YAAY,CAACS,KAAK,CAACO,MAAM,GAAG,MAAM;MAClChB,YAAY,CAACS,KAAK,CAACS,QAAQ,GAAG,QAAQ;;MAEtC;MACA,IAAIU,UAAU;MACd,IAAIC,KAAK,CAACC,OAAO,CAAC5D,MAAM,CAAC,EAAE;QACzB0D,UAAU,GAAG;UACX1E,CAAC,EAAEgB,MAAM,CAAC,CAAC,CAAC;UACZf,CAAC,EAAEe,MAAM,CAAC,CAAC,CAAC;UACZvB,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,MAAM;QACL,IAAImF,OAAO,EAAEC,OAAO;QACpB,IAAI1F,IAAI,GAAG4B,MAAM,CAAC+D,qBAAqB,CAAC,CAAC;QACzC3F,IAAI,CAACY,CAAC,GAAG,CAAC6E,OAAO,GAAGzF,IAAI,CAACY,CAAC,MAAM,IAAI,IAAI6E,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGzF,IAAI,CAACoE,IAAI;QAChFpE,IAAI,CAACa,CAAC,GAAG,CAAC6E,OAAO,GAAG1F,IAAI,CAACa,CAAC,MAAM,IAAI,IAAI6E,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG1F,IAAI,CAACsE,GAAG;QAC/EgB,UAAU,GAAG;UACX1E,CAAC,EAAEZ,IAAI,CAACY,CAAC;UACTC,CAAC,EAAEb,IAAI,CAACa,CAAC;UACTR,KAAK,EAAEL,IAAI,CAACK,KAAK;UACjBC,MAAM,EAAEN,IAAI,CAACM;QACf,CAAC;MACH;MACA,IAAIsF,SAAS,GAAGlC,YAAY,CAACiC,qBAAqB,CAAC,CAAC;MACpD,IAAIE,sBAAsB,GAAGhC,GAAG,CAACE,gBAAgB,CAACL,YAAY,CAAC;QAC7DpD,MAAM,GAAGuF,sBAAsB,CAACvF,MAAM;QACtCD,KAAK,GAAGwF,sBAAsB,CAACxF,KAAK;MACtCuF,SAAS,CAAChF,CAAC,GAAG,CAAC2C,YAAY,GAAGqC,SAAS,CAAChF,CAAC,MAAM,IAAI,IAAI2C,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGqC,SAAS,CAACxB,IAAI;MAC9GwB,SAAS,CAAC/E,CAAC,GAAG,CAAC2C,YAAY,GAAGoC,SAAS,CAAC/E,CAAC,MAAM,IAAI,IAAI2C,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGoC,SAAS,CAACtB,GAAG;MAC7G,IAAIwB,oBAAoB,GAAGnC,GAAG,CAACoC,eAAe;QAC5CC,WAAW,GAAGF,oBAAoB,CAACE,WAAW;QAC9CC,YAAY,GAAGH,oBAAoB,CAACG,YAAY;QAChDC,WAAW,GAAGJ,oBAAoB,CAACI,WAAW;QAC9CC,YAAY,GAAGL,oBAAoB,CAACK,YAAY;QAChDC,SAAS,GAAGN,oBAAoB,CAACM,SAAS;QAC1CC,UAAU,GAAGP,oBAAoB,CAACO,UAAU;MAC9C,IAAIC,WAAW,GAAGV,SAAS,CAACtF,MAAM;MAClC,IAAIiG,UAAU,GAAGX,SAAS,CAACvF,KAAK;MAChC,IAAImG,YAAY,GAAGlB,UAAU,CAAChF,MAAM;MACpC,IAAImG,WAAW,GAAGnB,UAAU,CAACjF,KAAK;;MAElC;MACA,IAAIqG,aAAa,GAAG;QAClBtC,IAAI,EAAE,CAAC;QACPE,GAAG,EAAE,CAAC;QACNE,KAAK,EAAEwB,WAAW;QAClBtB,MAAM,EAAEuB;MACV,CAAC;MACD,IAAIU,YAAY,GAAG;QACjBvC,IAAI,EAAE,CAACiC,UAAU;QACjB/B,GAAG,EAAE,CAAC8B,SAAS;QACf5B,KAAK,EAAE0B,WAAW,GAAGG,UAAU;QAC/B3B,MAAM,EAAEyB,YAAY,GAAGC;MACzB,CAAC;MACD,IAAIQ,UAAU,GAAG/B,aAAa,CAAC+B,UAAU;MACzC,IAAIC,OAAO,GAAG,SAAS;MACvB,IAAIC,aAAa,GAAG,cAAc;MAClC,IAAIF,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAKE,aAAa,EAAE;QAC3DF,UAAU,GAAGC,OAAO;MACtB;MACA,IAAIE,cAAc,GAAGH,UAAU,KAAKE,aAAa;MACjD,IAAIE,gBAAgB,GAAG/H,cAAc,CAAC0H,YAAY,EAAE3D,YAAY,CAAC;MACjE,IAAIiE,iBAAiB,GAAGhI,cAAc,CAACyH,aAAa,EAAE1D,YAAY,CAAC;MACnE,IAAIkE,WAAW,GAAGN,UAAU,KAAKC,OAAO,GAAGI,iBAAiB,GAAGD,gBAAgB;;MAE/E;MACA;MACA,IAAIG,sBAAsB,GAAGJ,cAAc,GAAGE,iBAAiB,GAAGC,WAAW;;MAE7E;MACAxD,YAAY,CAACS,KAAK,CAACC,IAAI,GAAG,MAAM;MAChCV,YAAY,CAACS,KAAK,CAACG,GAAG,GAAG,MAAM;MAC/BZ,YAAY,CAACS,KAAK,CAACK,KAAK,GAAG,GAAG;MAC9Bd,YAAY,CAACS,KAAK,CAACO,MAAM,GAAG,GAAG;MAC/B,IAAI0C,eAAe,GAAG1D,YAAY,CAACiC,qBAAqB,CAAC,CAAC;;MAE1D;MACAjC,YAAY,CAACS,KAAK,CAACC,IAAI,GAAGF,UAAU;MACpCR,YAAY,CAACS,KAAK,CAACG,GAAG,GAAGD,SAAS;MAClCX,YAAY,CAACS,KAAK,CAACK,KAAK,GAAGD,WAAW;MACtCb,YAAY,CAACS,KAAK,CAACO,MAAM,GAAGD,YAAY;MACxCf,YAAY,CAACS,KAAK,CAACS,QAAQ,GAAGD,cAAc;MAC5C,CAAClB,sBAAsB,GAAGC,YAAY,CAACsB,aAAa,MAAM,IAAI,IAAIvB,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAAC4D,WAAW,CAACvC,kBAAkB,CAAC;;MAE7J;MACA,IAAIwC,OAAO,GAAGnI,KAAK,CAACoI,IAAI,CAACC,KAAK,CAACjB,UAAU,GAAGzG,UAAU,CAACO,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;MAC7E,IAAIoH,OAAO,GAAGtI,KAAK,CAACoI,IAAI,CAACC,KAAK,CAAClB,WAAW,GAAGxG,UAAU,CAACQ,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;;MAE/E;MACA,IAAIgH,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAI9I,KAAK,CAACiD,MAAM,CAAC,IAAI,CAAChD,SAAS,CAACgD,MAAM,CAAC,EAAE;QACzE;MACF;;MAEA;MACA,IAAItC,MAAM,GAAGuF,aAAa,CAACvF,MAAM;QAC/BoI,YAAY,GAAG7C,aAAa,CAAC6C,YAAY;MAC3C,IAAIC,gBAAgB,GAAG5H,eAAe,CAAC6F,SAAS,EAAEtG,MAAM,CAAC;QACvDsI,iBAAiB,GAAGlJ,cAAc,CAACiJ,gBAAgB,EAAE,CAAC,CAAC;QACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;QACnCE,YAAY,GAAGF,iBAAiB,CAAC,CAAC,CAAC;MACrC,IAAIG,iBAAiB,GAAGhI,eAAe,CAACuF,UAAU,EAAEoC,YAAY,CAAC;QAC/DM,iBAAiB,GAAGtJ,cAAc,CAACqJ,iBAAiB,EAAE,CAAC,CAAC;QACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;QACpCE,aAAa,GAAGF,iBAAiB,CAAC,CAAC,CAAC;MACtC1C,UAAU,CAAC1E,CAAC,IAAIqH,aAAa;MAC7B3C,UAAU,CAACzE,CAAC,IAAIqH,aAAa;;MAE7B;MACA,IAAIC,KAAK,GAAGtD,aAAa,CAACrE,MAAM,IAAI,EAAE;QACpC4H,KAAK,GAAG1J,cAAc,CAACyJ,KAAK,EAAE,CAAC,CAAC;QAChCE,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrBE,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIG,YAAY,GAAGhI,WAAW,CAAC+H,WAAW,CAAC;MAC3C,IAAIE,WAAW,GAAGjI,WAAW,CAAC8H,UAAU,CAAC;MACzC,IAAII,gBAAgB,GAAGhI,aAAa,CAAC6E,UAAU,EAAEiD,YAAY,CAAC;MAC9D,IAAIG,eAAe,GAAGjI,aAAa,CAACmF,SAAS,EAAE4C,WAAW,CAAC;;MAE3D;MACA,IAAIG,aAAa,GAAGlK,aAAa,CAAC,CAAC,CAAC,EAAEoG,aAAa,CAAC;;MAEpD;MACA,IAAI+D,WAAW,GAAGH,gBAAgB,CAAC7H,CAAC,GAAG8H,eAAe,CAAC9H,CAAC,GAAGiH,YAAY;MACvE,IAAIgB,WAAW,GAAGJ,gBAAgB,CAAC5H,CAAC,GAAG6H,eAAe,CAAC7H,CAAC,GAAGiH,YAAY;;MAEvE;MACA;MACA,SAASgB,0BAA0BA,CAAC3I,OAAO,EAAEC,OAAO,EAAE;QACpD,IAAI2I,IAAI,GAAGxJ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG2H,WAAW;QAC1F,IAAI/F,CAAC,GAAGyE,SAAS,CAAChF,CAAC,GAAGT,OAAO;QAC7B,IAAIc,CAAC,GAAG2E,SAAS,CAAC/E,CAAC,GAAGT,OAAO;QAC7B,IAAIgB,CAAC,GAAGD,CAAC,GAAGoF,UAAU;QACtB,IAAIrF,CAAC,GAAGD,CAAC,GAAGqF,WAAW;QACvB,IAAI0C,QAAQ,GAAGzB,IAAI,CAAC0B,GAAG,CAAC9H,CAAC,EAAE4H,IAAI,CAAC3E,IAAI,CAAC;QACrC,IAAI8E,QAAQ,GAAG3B,IAAI,CAAC0B,GAAG,CAAChI,CAAC,EAAE8H,IAAI,CAACzE,GAAG,CAAC;QACpC,IAAI6E,QAAQ,GAAG5B,IAAI,CAAC6B,GAAG,CAAChI,CAAC,EAAE2H,IAAI,CAACvE,KAAK,CAAC;QACtC,IAAI6E,QAAQ,GAAG9B,IAAI,CAAC6B,GAAG,CAAClI,CAAC,EAAE6H,IAAI,CAACrE,MAAM,CAAC;QACvC,OAAO6C,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAACE,QAAQ,GAAGH,QAAQ,KAAKK,QAAQ,GAAGH,QAAQ,CAAC,CAAC;MACnE;MACA,IAAII,6BAA6B,GAAGR,0BAA0B,CAACF,WAAW,EAAEC,WAAW,CAAC;;MAExF;MACA,IAAIU,+BAA+B,GAAGT,0BAA0B,CAACF,WAAW,EAAEC,WAAW,EAAE5B,iBAAiB,CAAC;;MAE7G;MACA,IAAIuC,kBAAkB,GAAG/I,aAAa,CAAC6E,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC9D,IAAImE,iBAAiB,GAAGhJ,aAAa,CAACmF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC5D,IAAI8D,kBAAkB,GAAGjJ,aAAa,CAAC6E,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC9D,IAAIqE,iBAAiB,GAAGlJ,aAAa,CAACmF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC5D,IAAIhB,QAAQ,GAAGC,aAAa,CAACD,QAAQ,IAAI,CAAC,CAAC;MAC3C,IAAIgF,OAAO,GAAGhF,QAAQ,CAACgF,OAAO;QAC5BC,OAAO,GAAGjF,QAAQ,CAACiF,OAAO;QAC1BC,MAAM,GAAGlF,QAAQ,CAACkF,MAAM;QACxBC,MAAM,GAAGnF,QAAQ,CAACmF,MAAM;MAC1B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAE;QAC9C,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;UAC5B,OAAOA,GAAG;QACZ;QACA,OAAOA,GAAG,IAAI,CAAC;MACjB,CAAC;;MAED;MACA,IAAIC,UAAU;MACd,IAAIC,eAAe;MACnB,IAAIC,UAAU;MACd,IAAIC,cAAc;MAClB,SAASC,qBAAqBA,CAAA,EAAG;QAC/BJ,UAAU,GAAGtE,SAAS,CAAC/E,CAAC,GAAGgI,WAAW;QACtCsB,eAAe,GAAGD,UAAU,GAAG5D,WAAW;QAC1C8D,UAAU,GAAGxE,SAAS,CAAChF,CAAC,GAAGgI,WAAW;QACtCyB,cAAc,GAAGD,UAAU,GAAG7D,UAAU;MAC1C;MACA+D,qBAAqB,CAAC,CAAC;;MAEvB;MACA,IAAIC,WAAW,GAAGP,aAAa,CAACH,OAAO,CAAC;MACxC,IAAIW,MAAM,GAAGhC,WAAW,CAAC,CAAC,CAAC,KAAKD,YAAY,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAIgC,WAAW,IAAI/B,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK2B,eAAe,GAAGhD,sBAAsB,CAACzC,MAAM,IAAIxB,WAAW,CAACE,OAAO,CAACqH,EAAE,CAAC,EAAE;QACxH,IAAIC,cAAc,GAAG7B,WAAW;QAChC,IAAI2B,MAAM,EAAE;UACVE,cAAc,IAAIpE,WAAW,GAAGE,YAAY;QAC9C,CAAC,MAAM;UACLkE,cAAc,GAAGlB,kBAAkB,CAAC3I,CAAC,GAAG8I,iBAAiB,CAAC9I,CAAC,GAAGiH,YAAY;QAC5E;QACA,IAAI6C,cAAc,GAAG7B,0BAA0B,CAACF,WAAW,EAAE8B,cAAc,CAAC;QAC5E,IAAIE,uBAAuB,GAAG9B,0BAA0B,CAACF,WAAW,EAAE8B,cAAc,EAAEzD,iBAAiB,CAAC;QACxG;QACA;QACA0D,cAAc,GAAGrB,6BAA6B,IAAIqB,cAAc,KAAKrB,6BAA6B,KAAK,CAACvC,cAAc;QACtH;QACA6D,uBAAuB,IAAIrB,+BAA+B,CAAC,EAAE;UAC3DrG,WAAW,CAACE,OAAO,CAACqH,EAAE,GAAG,IAAI;UAC7B5B,WAAW,GAAG6B,cAAc;UAC5B5C,YAAY,GAAG,CAACA,YAAY;UAC5Ba,aAAa,CAACnI,MAAM,GAAG,CAACM,aAAa,CAAC0H,WAAW,EAAE,CAAC,CAAC,EAAE1H,aAAa,CAACyH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACLrF,WAAW,CAACE,OAAO,CAACqH,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAIF,WAAW,IAAI/B,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK0B,UAAU,GAAG/C,sBAAsB,CAAC7C,GAAG,IAAIpB,WAAW,CAACE,OAAO,CAACyH,EAAE,CAAC,EAAE;QAChH,IAAIC,eAAe,GAAGjC,WAAW;QACjC,IAAI2B,MAAM,EAAE;UACVM,eAAe,IAAIxE,WAAW,GAAGE,YAAY;QAC/C,CAAC,MAAM;UACLsE,eAAe,GAAGpB,kBAAkB,CAAC7I,CAAC,GAAG4I,iBAAiB,CAAC5I,CAAC,GAAGiH,YAAY;QAC7E;QACA,IAAIiD,eAAe,GAAGjC,0BAA0B,CAACF,WAAW,EAAEkC,eAAe,CAAC;QAC9E,IAAIE,wBAAwB,GAAGlC,0BAA0B,CAACF,WAAW,EAAEkC,eAAe,EAAE7D,iBAAiB,CAAC;QAC1G;QACA;QACA8D,eAAe,GAAGzB,6BAA6B,IAAIyB,eAAe,KAAKzB,6BAA6B,KAAK,CAACvC,cAAc;QACxH;QACAiE,wBAAwB,IAAIzB,+BAA+B,CAAC,EAAE;UAC5DrG,WAAW,CAACE,OAAO,CAACyH,EAAE,GAAG,IAAI;UAC7BhC,WAAW,GAAGiC,eAAe;UAC7BhD,YAAY,GAAG,CAACA,YAAY;UAC5Ba,aAAa,CAACnI,MAAM,GAAG,CAACM,aAAa,CAAC0H,WAAW,EAAE,CAAC,CAAC,EAAE1H,aAAa,CAACyH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACLrF,WAAW,CAACE,OAAO,CAACyH,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAII,WAAW,GAAGjB,aAAa,CAACJ,OAAO,CAAC;;MAExC;MACA,IAAIsB,MAAM,GAAG1C,WAAW,CAAC,CAAC,CAAC,KAAKD,YAAY,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAI0C,WAAW,IAAIzC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK6B,cAAc,GAAGlD,sBAAsB,CAAC3C,KAAK,IAAItB,WAAW,CAACE,OAAO,CAAC+H,EAAE,CAAC,EAAE;QACtH,IAAIC,cAAc,GAAGxC,WAAW;QAChC,IAAIsC,MAAM,EAAE;UACVE,cAAc,IAAI7E,UAAU,GAAGE,WAAW;QAC5C,CAAC,MAAM;UACL2E,cAAc,GAAG5B,kBAAkB,CAAC5I,CAAC,GAAG+I,iBAAiB,CAAC/I,CAAC,GAAGiH,YAAY;QAC5E;QACA,IAAIwD,gBAAgB,GAAGvC,0BAA0B,CAACsC,cAAc,EAAEvC,WAAW,CAAC;QAC9E,IAAIyC,yBAAyB,GAAGxC,0BAA0B,CAACsC,cAAc,EAAEvC,WAAW,EAAE5B,iBAAiB,CAAC;QAC1G;QACA;QACAoE,gBAAgB,GAAG/B,6BAA6B,IAAI+B,gBAAgB,KAAK/B,6BAA6B,KAAK,CAACvC,cAAc;QAC1H;QACAuE,yBAAyB,IAAI/B,+BAA+B,CAAC,EAAE;UAC7DrG,WAAW,CAACE,OAAO,CAAC+H,EAAE,GAAG,IAAI;UAC7BvC,WAAW,GAAGwC,cAAc;UAC5BvD,YAAY,GAAG,CAACA,YAAY;UAC5Bc,aAAa,CAACnI,MAAM,GAAG,CAACM,aAAa,CAAC0H,WAAW,EAAE,CAAC,CAAC,EAAE1H,aAAa,CAACyH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACLrF,WAAW,CAACE,OAAO,CAAC+H,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACA,IAAIF,WAAW,IAAIzC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK4B,UAAU,GAAGjD,sBAAsB,CAAC/C,IAAI,IAAIlB,WAAW,CAACE,OAAO,CAACmI,EAAE,CAAC,EAAE;QACjH,IAAIC,eAAe,GAAG5C,WAAW;QACjC,IAAIsC,MAAM,EAAE;UACVM,eAAe,IAAIjF,UAAU,GAAGE,WAAW;QAC7C,CAAC,MAAM;UACL+E,eAAe,GAAG9B,kBAAkB,CAAC9I,CAAC,GAAG6I,iBAAiB,CAAC7I,CAAC,GAAGiH,YAAY;QAC7E;QACA,IAAI4D,gBAAgB,GAAG3C,0BAA0B,CAAC0C,eAAe,EAAE3C,WAAW,CAAC;QAC/E,IAAI6C,yBAAyB,GAAG5C,0BAA0B,CAAC0C,eAAe,EAAE3C,WAAW,EAAE5B,iBAAiB,CAAC;QAC3G;QACA;QACAwE,gBAAgB,GAAGnC,6BAA6B,IAAImC,gBAAgB,KAAKnC,6BAA6B,KAAK,CAACvC,cAAc;QAC1H;QACA2E,yBAAyB,IAAInC,+BAA+B,CAAC,EAAE;UAC7DrG,WAAW,CAACE,OAAO,CAACmI,EAAE,GAAG,IAAI;UAC7B3C,WAAW,GAAG4C,eAAe;UAC7B3D,YAAY,GAAG,CAACA,YAAY;UAC5Bc,aAAa,CAACnI,MAAM,GAAG,CAACM,aAAa,CAAC0H,WAAW,EAAE,CAAC,CAAC,EAAE1H,aAAa,CAACyH,YAAY,EAAE,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM;UACLrF,WAAW,CAACE,OAAO,CAACmI,EAAE,GAAG,KAAK;QAChC;MACF;;MAEA;MACAjB,qBAAqB,CAAC,CAAC;MACvB,IAAIqB,SAAS,GAAG7B,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM;MAC5C,IAAI,OAAO6B,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAIvB,UAAU,GAAGnD,iBAAiB,CAAC7C,IAAI,EAAE;UACvCwE,WAAW,IAAIwB,UAAU,GAAGnD,iBAAiB,CAAC7C,IAAI,GAAGyD,YAAY;UACjE,IAAIvC,UAAU,CAAC1E,CAAC,GAAG6F,WAAW,GAAGQ,iBAAiB,CAAC7C,IAAI,GAAGuH,SAAS,EAAE;YACnE/C,WAAW,IAAItD,UAAU,CAAC1E,CAAC,GAAGqG,iBAAiB,CAAC7C,IAAI,GAAGqC,WAAW,GAAGkF,SAAS;UAChF;QACF;;QAEA;QACA,IAAItB,cAAc,GAAGpD,iBAAiB,CAACzC,KAAK,EAAE;UAC5CoE,WAAW,IAAIyB,cAAc,GAAGpD,iBAAiB,CAACzC,KAAK,GAAGqD,YAAY;UACtE,IAAIvC,UAAU,CAAC1E,CAAC,GAAGqG,iBAAiB,CAACzC,KAAK,GAAGmH,SAAS,EAAE;YACtD/C,WAAW,IAAItD,UAAU,CAAC1E,CAAC,GAAGqG,iBAAiB,CAACzC,KAAK,GAAGmH,SAAS;UACnE;QACF;MACF;MACA,IAAIC,SAAS,GAAG7B,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM;MAC5C,IAAI,OAAO6B,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAI1B,UAAU,GAAGjD,iBAAiB,CAAC3C,GAAG,EAAE;UACtCuE,WAAW,IAAIqB,UAAU,GAAGjD,iBAAiB,CAAC3C,GAAG,GAAGwD,YAAY;;UAEhE;UACA;UACA,IAAIxC,UAAU,CAACzE,CAAC,GAAG2F,YAAY,GAAGS,iBAAiB,CAAC3C,GAAG,GAAGsH,SAAS,EAAE;YACnE/C,WAAW,IAAIvD,UAAU,CAACzE,CAAC,GAAGoG,iBAAiB,CAAC3C,GAAG,GAAGkC,YAAY,GAAGoF,SAAS;UAChF;QACF;;QAEA;QACA,IAAIzB,eAAe,GAAGlD,iBAAiB,CAACvC,MAAM,EAAE;UAC9CmE,WAAW,IAAIsB,eAAe,GAAGlD,iBAAiB,CAACvC,MAAM,GAAGoD,YAAY;UACxE,IAAIxC,UAAU,CAACzE,CAAC,GAAGoG,iBAAiB,CAACvC,MAAM,GAAGkH,SAAS,EAAE;YACvD/C,WAAW,IAAIvD,UAAU,CAACzE,CAAC,GAAGoG,iBAAiB,CAACvC,MAAM,GAAGkH,SAAS;UACpE;QACF;MACF;;MAEA;MACA;MACA,IAAIC,SAAS,GAAGjG,SAAS,CAAChF,CAAC,GAAGgI,WAAW;MACzC,IAAIkD,UAAU,GAAGD,SAAS,GAAGtF,UAAU;MACvC,IAAIwF,QAAQ,GAAGnG,SAAS,CAAC/E,CAAC,GAAGgI,WAAW;MACxC,IAAImD,WAAW,GAAGD,QAAQ,GAAGzF,WAAW;MACxC,IAAI2F,UAAU,GAAG3G,UAAU,CAAC1E,CAAC;MAC7B,IAAIsL,WAAW,GAAGD,UAAU,GAAGxF,WAAW;MAC1C,IAAI0F,SAAS,GAAG7G,UAAU,CAACzE,CAAC;MAC5B,IAAIuL,YAAY,GAAGD,SAAS,GAAG3F,YAAY;MAC3C,IAAI6F,OAAO,GAAG9E,IAAI,CAAC0B,GAAG,CAAC4C,SAAS,EAAEI,UAAU,CAAC;MAC7C,IAAIK,QAAQ,GAAG/E,IAAI,CAAC6B,GAAG,CAAC0C,UAAU,EAAEI,WAAW,CAAC;MAChD,IAAIK,OAAO,GAAG,CAACF,OAAO,GAAGC,QAAQ,IAAI,CAAC;MACtC,IAAIE,UAAU,GAAGD,OAAO,GAAGV,SAAS;MACpC,IAAIY,MAAM,GAAGlF,IAAI,CAAC0B,GAAG,CAAC8C,QAAQ,EAAEI,SAAS,CAAC;MAC1C,IAAIO,SAAS,GAAGnF,IAAI,CAAC6B,GAAG,CAAC4C,WAAW,EAAEI,YAAY,CAAC;MACnD,IAAIO,OAAO,GAAG,CAACF,MAAM,GAAGC,SAAS,IAAI,CAAC;MACtC,IAAIE,UAAU,GAAGD,OAAO,GAAGZ,QAAQ;MACnC/J,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACL,QAAQ,EAAEgH,aAAa,CAAC;;MAEzF;MACA,IAAIkE,aAAa,GAAGzF,eAAe,CAAC5C,KAAK,GAAGoB,SAAS,CAAChF,CAAC,IAAIgI,WAAW,GAAGhD,SAAS,CAACvF,KAAK,CAAC;MACzF,IAAIyM,cAAc,GAAG1F,eAAe,CAAC1C,MAAM,GAAGkB,SAAS,CAAC/E,CAAC,IAAIgI,WAAW,GAAGjD,SAAS,CAACtF,MAAM,CAAC;MAC5F,IAAIgH,OAAO,KAAK,CAAC,EAAE;QACjBsB,WAAW,GAAGrB,IAAI,CAACC,KAAK,CAACoB,WAAW,CAAC;QACrCiE,aAAa,GAAGtF,IAAI,CAACC,KAAK,CAACqF,aAAa,CAAC;MAC3C;MACA,IAAIpF,OAAO,KAAK,CAAC,EAAE;QACjBoB,WAAW,GAAGtB,IAAI,CAACC,KAAK,CAACqB,WAAW,CAAC;QACrCiE,cAAc,GAAGvF,IAAI,CAACC,KAAK,CAACsF,cAAc,CAAC;MAC7C;MACA,IAAIC,cAAc,GAAG;QACnB5K,KAAK,EAAE,IAAI;QACXhC,OAAO,EAAEyI,WAAW,GAAGtB,OAAO;QAC9BlH,OAAO,EAAEyI,WAAW,GAAGpB,OAAO;QAC9BrF,OAAO,EAAEyK,aAAa,GAAGvF,OAAO;QAChCjF,OAAO,EAAEyK,cAAc,GAAGrF,OAAO;QACjCnF,MAAM,EAAEkK,UAAU,GAAGlF,OAAO;QAC5B/E,MAAM,EAAEqK,UAAU,GAAGnF,OAAO;QAC5BjF,MAAM,EAAE8E,OAAO;QACf7E,MAAM,EAAEgF,OAAO;QACf/E,KAAK,EAAEiG;MACT,CAAC;MACD9F,aAAa,CAACkK,cAAc,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzClK,aAAa,CAACM,OAAO,IAAI,CAAC;IAC1B,IAAI6J,EAAE,GAAGnK,aAAa,CAACM,OAAO;;IAE9B;IACA8J,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;MACjC,IAAItK,aAAa,CAACM,OAAO,KAAK6J,EAAE,EAAE;QAChC5J,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIgK,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrCxK,aAAa,CAAC,UAAUyK,GAAG,EAAE;MAC3B,OAAO7O,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6O,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/CnL,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDrD,eAAe,CAACuO,UAAU,EAAE,CAACxL,SAAS,CAAC,CAAC;EACxC/C,eAAe,CAAC,YAAY;IAC1B,IAAI,CAAC4C,IAAI,EAAE;MACT2L,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3L,IAAI,CAAC,CAAC;EACV,OAAO,CAACkB,UAAU,CAACT,KAAK,EAAES,UAAU,CAACzC,OAAO,EAAEyC,UAAU,CAACxC,OAAO,EAAEwC,UAAU,CAACR,OAAO,EAAEQ,UAAU,CAACP,OAAO,EAAEO,UAAU,CAACN,MAAM,EAAEM,UAAU,CAACL,MAAM,EAAEK,UAAU,CAACJ,MAAM,EAAEI,UAAU,CAACH,MAAM,EAAEG,UAAU,CAACF,KAAK,EAAEsK,YAAY,CAAC;AACvN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}