{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Row, Col, Card, List, Button, Select, Spin, message, Typography, Space, Tag, Statistic } from 'antd';\nimport { ReloadOutlined, StockOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport SimpleChart from '../components/SimpleChart';\nimport { stockAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst DataManagement = () => {\n  _s();\n  var _stats$latest_date;\n  const [stockList, setStockList] = useState([]);\n  const [selectedStock, setSelectedStock] = useState(null);\n  const [stockData, setStockData] = useState(null);\n  const [marketFilter, setMarketFilter] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [chartLoading, setChartLoading] = useState(false);\n  const [stats, setStats] = useState(null);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await stockAPI.getStockList(marketFilter === 'all' ? null : marketFilter);\n      if (response.data.success) {\n        setStockList(response.data.data);\n        message.success(`加载了 ${response.data.data.length} 只股票`);\n      } else {\n        message.error('加载股票列表失败');\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n      message.error('网络请求失败');\n    } finally {\n      setLoading(false);\n    }\n  }, [marketFilter]);\n\n  // 加载系统统计\n  const loadStats = async () => {\n    try {\n      const response = await stockAPI.getDataStats();\n      if (response.data.success) {\n        setStats(response.data.stats);\n      }\n    } catch (error) {\n      console.error('加载统计信息失败:', error);\n    }\n  };\n\n  // 选择股票\n  const handleStockSelect = async stock => {\n    setSelectedStock(stock);\n    setChartLoading(true);\n    setStockData(null);\n    try {\n      const response = await stockAPI.getStockDetail(stock.code);\n      if (response.data.success) {\n        setStockData(response.data);\n        message.success(`加载 ${stock.name} 数据成功`);\n      } else {\n        message.error(`加载 ${stock.name} 数据失败`);\n      }\n    } catch (error) {\n      console.error('加载股票数据失败:', error);\n      message.error('加载股票数据失败');\n    } finally {\n      setChartLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadStats();\n  }, []);\n\n  // 市场筛选变化\n  useEffect(() => {\n    loadStockList();\n  }, [marketFilter]);\n\n  // 筛选股票列表\n  const filteredStocks = stockList.filter(stock => {\n    if (marketFilter === 'all') return true;\n    return stock.market === marketFilter;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), \"\\u80A1\\u7968\\u5217\\u8868\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              value: marketFilter,\n              onChange: setMarketFilter,\n              style: {\n                width: 100\n              },\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"all\",\n                children: \"\\u5168\\u90E8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"SH\",\n                children: \"\\u4E0A\\u6D77\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"SZ\",\n                children: \"\\u6DF1\\u5733\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 25\n              }, this),\n              onClick: loadStockList,\n              loading: loading,\n              size: \"small\",\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: filteredStocks,\n              renderItem: stock => /*#__PURE__*/_jsxDEV(List.Item, {\n                onClick: () => handleStockSelect(stock),\n                style: {\n                  cursor: 'pointer',\n                  backgroundColor: (selectedStock === null || selectedStock === void 0 ? void 0 : selectedStock.code) === stock.code ? '#f0f8ff' : 'transparent',\n                  borderRadius: '4px',\n                  padding: '8px',\n                  margin: '4px 0',\n                  border: (selectedStock === null || selectedStock === void 0 ? void 0 : selectedStock.code) === stock.code ? '1px solid #1890ff' : '1px solid transparent'\n                },\n                className: \"stock-list-item\",\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: /*#__PURE__*/_jsxDEV(StockOutlined, {\n                    style: {\n                      color: '#1890ff'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 31\n                  }, this),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: stock.code\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                      color: stock.market === 'SH' ? 'red' : 'green',\n                      children: stock.market\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 25\n                  }, this),\n                  description: stock.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this),\n              locale: {\n                emptyText: '暂无股票数据'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: selectedStock ? /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          size: \"large\",\n          children: [stockData && /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u80A1\\u7968\\u4EE3\\u7801\",\n                  value: stockData.stock_info.code\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u80A1\\u7968\\u540D\\u79F0\",\n                  value: stockData.stock_info.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u6240\\u5C5E\\u5E02\\u573A\",\n                  value: stockData.stock_info.market\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u6570\\u636E\\u91CF\",\n                  value: stockData.data_stats.count,\n                  suffix: \"\\u6761\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(SimpleChart, {\n            data: (stockData === null || stockData === void 0 ? void 0 : stockData.recent_data) || [],\n            title: selectedStock ? `${selectedStock.name} (${selectedStock.code}) K线图` : 'K线图',\n            height: 600,\n            loading: chartLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '60px 0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(StockOutlined, {\n              style: {\n                fontSize: '64px',\n                color: '#ccc',\n                marginBottom: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              type: \"secondary\",\n              children: \"\\u8BF7\\u9009\\u62E9\\u80A1\\u7968\\u67E5\\u770B\\u8BE6\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u70B9\\u51FB\\u5DE6\\u4FA7\\u80A1\\u7968\\u5217\\u8868\\u4E2D\\u7684\\u4EFB\\u610F\\u80A1\\u7968\\u67E5\\u770BK\\u7EBF\\u56FE\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), stats && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '40px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 5,\n                children: \"\\u7CFB\\u7EDF\\u6982\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: 16,\n                justify: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 6,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u80A1\\u7968\\u6570\\u91CF\",\n                    value: stats.stock_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 6,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u6570\\u636E\\u91CF\",\n                    value: stats.daily_data_count,\n                    formatter: value => `${(value / 10000).toFixed(1)}万`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 6,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u7279\\u5F81\\u5411\\u91CF\",\n                    value: stats.feature_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 6,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u6700\\u65B0\\u6570\\u636E\",\n                    value: ((_stats$latest_date = stats.latest_date) === null || _stats$latest_date === void 0 ? void 0 : _stats$latest_date.slice(0, 10)) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .stock-list-item:hover {\n          background-color: #f5f5f5 !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(DataManagement, \"/M/uJZjt+WzFOX9Be7i0BPFt3Y8=\");\n_c = DataManagement;\nexport default DataManagement;\nvar _c;\n$RefreshReg$(_c, \"DataManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Row", "Col", "Card", "List", "<PERSON><PERSON>", "Select", "Spin", "message", "Typography", "Space", "Tag", "Statistic", "ReloadOutlined", "StockOutlined", "DatabaseOutlined", "<PERSON><PERSON><PERSON>", "stockAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "DataManagement", "_s", "_stats$latest_date", "stockList", "setStockList", "selectedStock", "setSelectedStock", "stockData", "setStockData", "marketFilter", "setMarketFilter", "loading", "setLoading", "chartLoading", "set<PERSON>hartLoading", "stats", "setStats", "loadStockList", "response", "getStockList", "data", "success", "length", "error", "console", "loadStats", "getDataStats", "handleStockSelect", "stock", "getStockDetail", "code", "name", "filteredStocks", "filter", "market", "children", "gutter", "xs", "lg", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "extra", "value", "onChange", "style", "width", "icon", "onClick", "size", "spinning", "dataSource", "renderItem", "<PERSON><PERSON>", "cursor", "backgroundColor", "borderRadius", "padding", "margin", "border", "className", "Meta", "avatar", "color", "strong", "description", "locale", "emptyText", "direction", "span", "stock_info", "data_stats", "count", "suffix", "recent_data", "height", "textAlign", "fontSize", "marginBottom", "level", "type", "marginTop", "justify", "stock_count", "daily_data_count", "formatter", "toFixed", "feature_count", "latest_date", "slice", "jsx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/DataManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  List,\n  Button,\n  Select,\n  Spin,\n  message,\n  Typography,\n  Space,\n  Tag,\n  Statistic,\n} from 'antd';\nimport {\n  ReloadOutlined,\n  StockOutlined,\n  DatabaseOutlined,\n} from '@ant-design/icons';\nimport SimpleChart from '../components/SimpleChart';\nimport { stockAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst DataManagement = () => {\n  const [stockList, setStockList] = useState([]);\n  const [selectedStock, setSelectedStock] = useState(null);\n  const [stockData, setStockData] = useState(null);\n  const [marketFilter, setMarketFilter] = useState('all');\n  const [loading, setLoading] = useState(false);\n  const [chartLoading, setChartLoading] = useState(false);\n  const [stats, setStats] = useState(null);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await stockAPI.getStockList(marketFilter === 'all' ? null : marketFilter);\n      if (response.data.success) {\n        setStockList(response.data.data);\n        message.success(`加载了 ${response.data.data.length} 只股票`);\n      } else {\n        message.error('加载股票列表失败');\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n      message.error('网络请求失败');\n    } finally {\n      setLoading(false);\n    }\n  }, [marketFilter]);\n\n  // 加载系统统计\n  const loadStats = async () => {\n    try {\n      const response = await stockAPI.getDataStats();\n      if (response.data.success) {\n        setStats(response.data.stats);\n      }\n    } catch (error) {\n      console.error('加载统计信息失败:', error);\n    }\n  };\n\n  // 选择股票\n  const handleStockSelect = async (stock) => {\n    setSelectedStock(stock);\n    setChartLoading(true);\n    setStockData(null);\n\n    try {\n      const response = await stockAPI.getStockDetail(stock.code);\n      if (response.data.success) {\n        setStockData(response.data);\n        message.success(`加载 ${stock.name} 数据成功`);\n      } else {\n        message.error(`加载 ${stock.name} 数据失败`);\n      }\n    } catch (error) {\n      console.error('加载股票数据失败:', error);\n      message.error('加载股票数据失败');\n    } finally {\n      setChartLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadStats();\n  }, []);\n\n  // 市场筛选变化\n  useEffect(() => {\n    loadStockList();\n  }, [marketFilter]);\n\n  // 筛选股票列表\n  const filteredStocks = stockList.filter(stock => {\n    if (marketFilter === 'all') return true;\n    return stock.market === marketFilter;\n  });\n\n  return (\n    <div>\n      <Row gutter={[16, 16]}>\n        {/* 左侧股票列表 */}\n        <Col xs={24} lg={8}>\n          <Card\n            title={\n              <Space>\n                <DatabaseOutlined />\n                股票列表\n              </Space>\n            }\n            extra={\n              <Space>\n                <Select\n                  value={marketFilter}\n                  onChange={setMarketFilter}\n                  style={{ width: 100 }}\n                >\n                  <Option value=\"all\">全部</Option>\n                  <Option value=\"SH\">上海</Option>\n                  <Option value=\"SZ\">深圳</Option>\n                </Select>\n                <Button\n                  icon={<ReloadOutlined />}\n                  onClick={loadStockList}\n                  loading={loading}\n                  size=\"small\"\n                >\n                  刷新\n                </Button>\n              </Space>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={filteredStocks}\n                renderItem={(stock) => (\n                  <List.Item\n                    onClick={() => handleStockSelect(stock)}\n                    style={{\n                      cursor: 'pointer',\n                      backgroundColor: selectedStock?.code === stock.code ? '#f0f8ff' : 'transparent',\n                      borderRadius: '4px',\n                      padding: '8px',\n                      margin: '4px 0',\n                      border: selectedStock?.code === stock.code ? '1px solid #1890ff' : '1px solid transparent',\n                    }}\n                    className=\"stock-list-item\"\n                  >\n                    <List.Item.Meta\n                      avatar={<StockOutlined style={{ color: '#1890ff' }} />}\n                      title={\n                        <Space>\n                          <Text strong>{stock.code}</Text>\n                          <Tag color={stock.market === 'SH' ? 'red' : 'green'}>\n                            {stock.market}\n                          </Tag>\n                        </Space>\n                      }\n                      description={stock.name}\n                    />\n                  </List.Item>\n                )}\n                locale={{ emptyText: '暂无股票数据' }}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 右侧详情和图表 */}\n        <Col xs={24} lg={16}>\n          {selectedStock ? (\n            <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n              {/* 股票信息卡片 */}\n              {stockData && (\n                <Card>\n                  <Row gutter={16}>\n                    <Col span={6}>\n                      <Statistic title=\"股票代码\" value={stockData.stock_info.code} />\n                    </Col>\n                    <Col span={6}>\n                      <Statistic title=\"股票名称\" value={stockData.stock_info.name} />\n                    </Col>\n                    <Col span={6}>\n                      <Statistic title=\"所属市场\" value={stockData.stock_info.market} />\n                    </Col>\n                    <Col span={6}>\n                      <Statistic title=\"数据量\" value={stockData.data_stats.count} suffix=\"条\" />\n                    </Col>\n                  </Row>\n                </Card>\n              )}\n\n              {/* K线图表 */}\n              <SimpleChart\n                data={stockData?.recent_data || []}\n                title={selectedStock ? `${selectedStock.name} (${selectedStock.code}) K线图` : 'K线图'}\n                height={600}\n                loading={chartLoading}\n              />\n            </Space>\n          ) : (\n            <Card>\n              <div style={{ textAlign: 'center', padding: '60px 0' }}>\n                <StockOutlined style={{ fontSize: '64px', color: '#ccc', marginBottom: '16px' }} />\n                <Title level={4} type=\"secondary\">请选择股票查看详情</Title>\n                <Text type=\"secondary\">点击左侧股票列表中的任意股票查看K线图表</Text>\n                \n                {/* 系统统计 */}\n                {stats && (\n                  <div style={{ marginTop: '40px' }}>\n                    <Title level={5}>系统概览</Title>\n                    <Row gutter={16} justify=\"center\">\n                      <Col span={6}>\n                        <Statistic title=\"股票数量\" value={stats.stock_count} />\n                      </Col>\n                      <Col span={6}>\n                        <Statistic title=\"数据量\" value={stats.daily_data_count} formatter={(value) => `${(value / 10000).toFixed(1)}万`} />\n                      </Col>\n                      <Col span={6}>\n                        <Statistic title=\"特征向量\" value={stats.feature_count} />\n                      </Col>\n                      <Col span={6}>\n                        <Statistic title=\"最新数据\" value={stats.latest_date?.slice(0, 10) || 'N/A'} />\n                      </Col>\n                    </Row>\n                  </div>\n                )}\n              </div>\n            </Card>\n          )}\n        </Col>\n      </Row>\n\n      <style jsx>{`\n        .stock-list-item:hover {\n          background-color: #f5f5f5 !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default DataManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,cAAc,EACdC,aAAa,EACbC,gBAAgB,QACX,mBAAmB;AAC1B,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGZ,UAAU;AAClC,MAAM;EAAEa;AAAO,CAAC,GAAGhB,MAAM;AAEzB,MAAMiB,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM0C,aAAa,GAAGxC,WAAW,CAAC,YAAY;IAC5CmC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMxB,QAAQ,CAACyB,YAAY,CAACV,YAAY,KAAK,KAAK,GAAG,IAAI,GAAGA,YAAY,CAAC;MAC1F,IAAIS,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBjB,YAAY,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QAChCnC,OAAO,CAACoC,OAAO,CAAC,OAAOH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,MAAM,MAAM,CAAC;MACzD,CAAC,MAAM;QACLrC,OAAO,CAACsC,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtC,OAAO,CAACsC,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMxB,QAAQ,CAACgC,YAAY,CAAC,CAAC;MAC9C,IAAIR,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBL,QAAQ,CAACE,QAAQ,CAACE,IAAI,CAACL,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAG,MAAOC,KAAK,IAAK;IACzCtB,gBAAgB,CAACsB,KAAK,CAAC;IACvBd,eAAe,CAAC,IAAI,CAAC;IACrBN,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMxB,QAAQ,CAACmC,cAAc,CAACD,KAAK,CAACE,IAAI,CAAC;MAC1D,IAAIZ,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBb,YAAY,CAACU,QAAQ,CAACE,IAAI,CAAC;QAC3BnC,OAAO,CAACoC,OAAO,CAAC,MAAMO,KAAK,CAACG,IAAI,OAAO,CAAC;MAC1C,CAAC,MAAM;QACL9C,OAAO,CAACsC,KAAK,CAAC,MAAMK,KAAK,CAACG,IAAI,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtC,OAAO,CAACsC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRT,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAtC,SAAS,CAAC,MAAM;IACdyC,aAAa,CAAC,CAAC;IACfQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjD,SAAS,CAAC,MAAM;IACdyC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMuB,cAAc,GAAG7B,SAAS,CAAC8B,MAAM,CAACL,KAAK,IAAI;IAC/C,IAAInB,YAAY,KAAK,KAAK,EAAE,OAAO,IAAI;IACvC,OAAOmB,KAAK,CAACM,MAAM,KAAKzB,YAAY;EACtC,CAAC,CAAC;EAEF,oBACEb,OAAA;IAAAuC,QAAA,gBACEvC,OAAA,CAAClB,GAAG;MAAC0D,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAD,QAAA,gBAEpBvC,OAAA,CAACjB,GAAG;QAAC0D,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eACjBvC,OAAA,CAAChB,IAAI;UACH2D,KAAK,eACH3C,OAAA,CAACT,KAAK;YAAAgD,QAAA,gBACJvC,OAAA,CAACJ,gBAAgB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDC,KAAK,eACHhD,OAAA,CAACT,KAAK;YAAAgD,QAAA,gBACJvC,OAAA,CAACb,MAAM;cACL8D,KAAK,EAAEpC,YAAa;cACpBqC,QAAQ,EAAEpC,eAAgB;cAC1BqC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAb,QAAA,gBAEtBvC,OAAA,CAACG,MAAM;gBAAC8C,KAAK,EAAC,KAAK;gBAAAV,QAAA,EAAC;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/B/C,OAAA,CAACG,MAAM;gBAAC8C,KAAK,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B/C,OAAA,CAACG,MAAM;gBAAC8C,KAAK,EAAC,IAAI;gBAAAV,QAAA,EAAC;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACT/C,OAAA,CAACd,MAAM;cACLmE,IAAI,eAAErD,OAAA,CAACN,cAAc;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBO,OAAO,EAAEjC,aAAc;cACvBN,OAAO,EAAEA,OAAQ;cACjBwC,IAAI,EAAC,OAAO;cAAAhB,QAAA,EACb;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAR,QAAA,eAEDvC,OAAA,CAACZ,IAAI;YAACoE,QAAQ,EAAEzC,OAAQ;YAAAwB,QAAA,eACtBvC,OAAA,CAACf,IAAI;cACHwE,UAAU,EAAErB,cAAe;cAC3BsB,UAAU,EAAG1B,KAAK,iBAChBhC,OAAA,CAACf,IAAI,CAAC0E,IAAI;gBACRL,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAACC,KAAK,CAAE;gBACxCmB,KAAK,EAAE;kBACLS,MAAM,EAAE,SAAS;kBACjBC,eAAe,EAAE,CAAApD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyB,IAAI,MAAKF,KAAK,CAACE,IAAI,GAAG,SAAS,GAAG,aAAa;kBAC/E4B,YAAY,EAAE,KAAK;kBACnBC,OAAO,EAAE,KAAK;kBACdC,MAAM,EAAE,OAAO;kBACfC,MAAM,EAAE,CAAAxD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyB,IAAI,MAAKF,KAAK,CAACE,IAAI,GAAG,mBAAmB,GAAG;gBACrE,CAAE;gBACFgC,SAAS,EAAC,iBAAiB;gBAAA3B,QAAA,eAE3BvC,OAAA,CAACf,IAAI,CAAC0E,IAAI,CAACQ,IAAI;kBACbC,MAAM,eAAEpE,OAAA,CAACL,aAAa;oBAACwD,KAAK,EAAE;sBAAEkB,KAAK,EAAE;oBAAU;kBAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvDJ,KAAK,eACH3C,OAAA,CAACT,KAAK;oBAAAgD,QAAA,gBACJvC,OAAA,CAACE,IAAI;sBAACoE,MAAM;sBAAA/B,QAAA,EAAEP,KAAK,CAACE;oBAAI;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChC/C,OAAA,CAACR,GAAG;sBAAC6E,KAAK,EAAErC,KAAK,CAACM,MAAM,KAAK,IAAI,GAAG,KAAK,GAAG,OAAQ;sBAAAC,QAAA,EACjDP,KAAK,CAACM;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR;kBACDwB,WAAW,EAAEvC,KAAK,CAACG;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CACX;cACFyB,MAAM,EAAE;gBAAEC,SAAS,EAAE;cAAS;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN/C,OAAA,CAACjB,GAAG;QAAC0D,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAH,QAAA,EACjB9B,aAAa,gBACZT,OAAA,CAACT,KAAK;UAACmF,SAAS,EAAC,UAAU;UAACvB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAACG,IAAI,EAAC,OAAO;UAAAhB,QAAA,GAE/D5B,SAAS,iBACRX,OAAA,CAAChB,IAAI;YAAAuD,QAAA,eACHvC,OAAA,CAAClB,GAAG;cAAC0D,MAAM,EAAE,EAAG;cAAAD,QAAA,gBACdvC,OAAA,CAACjB,GAAG;gBAAC4F,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;kBAACkD,KAAK,EAAC,0BAAM;kBAACM,KAAK,EAAEtC,SAAS,CAACiE,UAAU,CAAC1C;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACN/C,OAAA,CAACjB,GAAG;gBAAC4F,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;kBAACkD,KAAK,EAAC,0BAAM;kBAACM,KAAK,EAAEtC,SAAS,CAACiE,UAAU,CAACzC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACN/C,OAAA,CAACjB,GAAG;gBAAC4F,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;kBAACkD,KAAK,EAAC,0BAAM;kBAACM,KAAK,EAAEtC,SAAS,CAACiE,UAAU,CAACtC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACN/C,OAAA,CAACjB,GAAG;gBAAC4F,IAAI,EAAE,CAAE;gBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;kBAACkD,KAAK,EAAC,oBAAK;kBAACM,KAAK,EAAEtC,SAAS,CAACkE,UAAU,CAACC,KAAM;kBAACC,MAAM,EAAC;gBAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACP,eAGD/C,OAAA,CAACH,WAAW;YACV2B,IAAI,EAAE,CAAAb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEqE,WAAW,KAAI,EAAG;YACnCrC,KAAK,EAAElC,aAAa,GAAG,GAAGA,aAAa,CAAC0B,IAAI,KAAK1B,aAAa,CAACyB,IAAI,OAAO,GAAG,KAAM;YACnF+C,MAAM,EAAE,GAAI;YACZlE,OAAO,EAAEE;UAAa;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,gBAER/C,OAAA,CAAChB,IAAI;UAAAuD,QAAA,eACHvC,OAAA;YAAKmD,KAAK,EAAE;cAAE+B,SAAS,EAAE,QAAQ;cAAEnB,OAAO,EAAE;YAAS,CAAE;YAAAxB,QAAA,gBACrDvC,OAAA,CAACL,aAAa;cAACwD,KAAK,EAAE;gBAAEgC,QAAQ,EAAE,MAAM;gBAAEd,KAAK,EAAE,MAAM;gBAAEe,YAAY,EAAE;cAAO;YAAE;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnF/C,OAAA,CAACC,KAAK;cAACoF,KAAK,EAAE,CAAE;cAACC,IAAI,EAAC,WAAW;cAAA/C,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD/C,OAAA,CAACE,IAAI;cAACoF,IAAI,EAAC,WAAW;cAAA/C,QAAA,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAGjD5B,KAAK,iBACJnB,OAAA;cAAKmD,KAAK,EAAE;gBAAEoC,SAAS,EAAE;cAAO,CAAE;cAAAhD,QAAA,gBAChCvC,OAAA,CAACC,KAAK;gBAACoF,KAAK,EAAE,CAAE;gBAAA9C,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7B/C,OAAA,CAAClB,GAAG;gBAAC0D,MAAM,EAAE,EAAG;gBAACgD,OAAO,EAAC,QAAQ;gBAAAjD,QAAA,gBAC/BvC,OAAA,CAACjB,GAAG;kBAAC4F,IAAI,EAAE,CAAE;kBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;oBAACkD,KAAK,EAAC,0BAAM;oBAACM,KAAK,EAAE9B,KAAK,CAACsE;kBAAY;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN/C,OAAA,CAACjB,GAAG;kBAAC4F,IAAI,EAAE,CAAE;kBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;oBAACkD,KAAK,EAAC,oBAAK;oBAACM,KAAK,EAAE9B,KAAK,CAACuE,gBAAiB;oBAACC,SAAS,EAAG1C,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAE2C,OAAO,CAAC,CAAC,CAAC;kBAAI;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACN/C,OAAA,CAACjB,GAAG;kBAAC4F,IAAI,EAAE,CAAE;kBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;oBAACkD,KAAK,EAAC,0BAAM;oBAACM,KAAK,EAAE9B,KAAK,CAAC0E;kBAAc;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN/C,OAAA,CAACjB,GAAG;kBAAC4F,IAAI,EAAE,CAAE;kBAAApC,QAAA,eACXvC,OAAA,CAACP,SAAS;oBAACkD,KAAK,EAAC,0BAAM;oBAACM,KAAK,EAAE,EAAA3C,kBAAA,GAAAa,KAAK,CAAC2E,WAAW,cAAAxF,kBAAA,uBAAjBA,kBAAA,CAAmByF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI;kBAAM;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/C,OAAA;MAAOgG,GAAG;MAAAzD,QAAA,EAAE;AAClB;AACA;AACA;AACA;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA7NID,cAAc;AAAA6F,EAAA,GAAd7F,cAAc;AA+NpB,eAAeA,cAAc;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}