{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, theme, Typography, Space } from 'antd';\nimport { DatabaseOutlined, SettingOutlined, SearchOutlined, InfoCircleOutlined, StockOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Title\n} = Typography;\nconst Layout = ({\n  children,\n  currentPage,\n  onPageChange\n}) => {\n  _s();\n  var _menuItems$find;\n  const [collapsed, setCollapsed] = useState(false);\n  const {\n    token: {\n      colorBgContainer,\n      borderRadiusLG\n    }\n  } = theme.useToken();\n  const menuItems = [{\n    key: 'data-management',\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this),\n    label: '数据管理'\n  }, {\n    key: 'feature-engineering',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this),\n    label: '特征工程'\n  }, {\n    key: 'similarity-search',\n    icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this),\n    label: '相似性搜索'\n  }, {\n    key: 'system-status',\n    icon: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this),\n    label: '系统状态'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    onPageChange(key);\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      width: 250,\n      style: {\n        background: colorBgContainer,\n        borderRight: '1px solid #f0f0f0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #f0f0f0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUpOutlined, {\n            style: {\n              fontSize: collapsed ? '24px' : '32px',\n              color: '#1890ff',\n              transition: 'all 0.3s'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u80A1\\u7968\\u5206\\u6790\\u7CFB\\u7EDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [currentPage],\n        items: menuItems,\n        onClick: handleMenuClick,\n        style: {\n          border: 'none',\n          height: 'calc(100vh - 120px)',\n          overflow: 'auto'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: colorBgContainer,\n          borderBottom: '1px solid #f0f0f0',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              fontSize: '16px',\n              width: 32,\n              height: 32,\n              border: 'none',\n              background: 'transparent',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              borderRadius: '4px'\n            },\n            children: collapsed ? '→' : '←'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 3,\n            style: {\n              margin: 0\n            },\n            children: ((_menuItems$find = menuItems.find(item => item.key === currentPage)) === null || _menuItems$find === void 0 ? void 0 : _menuItems$find.label) || '股票数据分析系统'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666'\n            },\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '16px',\n          padding: '24px',\n          minHeight: 'calc(100vh - 112px)',\n          background: colorBgContainer,\n          borderRadius: borderRadiusLG,\n          overflow: 'auto'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"DR3fWPICoJKEGRT1nRV2plPsu70=\", false, function () {\n  return [theme.useToken];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "AntLayout", "<PERSON><PERSON>", "theme", "Typography", "Space", "DatabaseOutlined", "SettingOutlined", "SearchOutlined", "InfoCircleOutlined", "StockOutlined", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "children", "currentPage", "onPageChange", "_s", "_menuItems$find", "collapsed", "setCollapsed", "token", "colorBgContainer", "borderRadiusLG", "useToken", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "style", "minHeight", "trigger", "collapsible", "width", "background", "borderRight", "padding", "borderBottom", "textAlign", "direction", "size", "TrendingUpOutlined", "fontSize", "color", "transition", "level", "margin", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "onClick", "border", "height", "overflow", "display", "alignItems", "justifyContent", "type", "cursor", "borderRadius", "find", "item", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/Layout.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, theme, Typography, Space } from 'antd';\nimport {\n  DatabaseOutlined,\n  SettingOutlined,\n  SearchOutlined,\n  InfoCircleOutlined,\n  StockOutlined,\n} from '@ant-design/icons';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Title } = Typography;\n\nconst Layout = ({ children, currentPage, onPageChange }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  const menuItems = [\n    {\n      key: 'data-management',\n      icon: <DatabaseOutlined />,\n      label: '数据管理',\n    },\n    {\n      key: 'feature-engineering',\n      icon: <SettingOutlined />,\n      label: '特征工程',\n    },\n    {\n      key: 'similarity-search',\n      icon: <SearchOutlined />,\n      label: '相似性搜索',\n    },\n    {\n      key: 'system-status',\n      icon: <InfoCircleOutlined />,\n      label: '系统状态',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    onPageChange(key);\n  };\n\n  return (\n    <AntLayout style={{ minHeight: '100vh' }}>\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        width={250}\n        style={{\n          background: colorBgContainer,\n          borderRight: '1px solid #f0f0f0',\n        }}\n      >\n        <div style={{ \n          padding: '16px', \n          borderBottom: '1px solid #f0f0f0',\n          textAlign: 'center'\n        }}>\n          <Space direction=\"vertical\" size=\"small\">\n            <TrendingUpOutlined \n              style={{ \n                fontSize: collapsed ? '24px' : '32px', \n                color: '#1890ff',\n                transition: 'all 0.3s'\n              }} \n            />\n            {!collapsed && (\n              <Title level={4} style={{ margin: 0, color: '#1890ff' }}>\n                股票分析系统\n              </Title>\n            )}\n          </Space>\n        </div>\n        \n        <Menu\n          mode=\"inline\"\n          selectedKeys={[currentPage]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          style={{\n            border: 'none',\n            height: 'calc(100vh - 120px)',\n            overflow: 'auto',\n          }}\n        />\n      </Sider>\n      \n      <AntLayout>\n        <Header\n          style={{\n            padding: '0 24px',\n            background: colorBgContainer,\n            borderBottom: '1px solid #f0f0f0',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          <Space>\n            <button\n              type=\"button\"\n              onClick={() => setCollapsed(!collapsed)}\n              style={{\n                fontSize: '16px',\n                width: 32,\n                height: 32,\n                border: 'none',\n                background: 'transparent',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: '4px',\n              }}\n            >\n              {collapsed ? '→' : '←'}\n            </button>\n            <Title level={3} style={{ margin: 0 }}>\n              {menuItems.find(item => item.key === currentPage)?.label || '股票数据分析系统'}\n            </Title>\n          </Space>\n          \n          <Space>\n            <span style={{ color: '#666' }}>\n              v1.0.0\n            </span>\n          </Space>\n        </Header>\n        \n        <Content\n          style={{\n            margin: '16px',\n            padding: '24px',\n            minHeight: 'calc(100vh - 112px)',\n            background: colorBgContainer,\n            borderRadius: borderRadiusLG,\n            overflow: 'auto',\n          }}\n        >\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAC1E,SACEC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGd,SAAS;AAC5C,MAAM;EAAEe;AAAM,CAAC,GAAGZ,UAAU;AAE5B,MAAMJ,MAAM,GAAGA,CAAC;EAAEiB,QAAQ;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IACJyB,KAAK,EAAE;MAAEC,gBAAgB;MAAEC;IAAe;EAC5C,CAAC,GAAGvB,KAAK,CAACwB,QAAQ,CAAC,CAAC;EAEpB,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACN,gBAAgB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,qBAAqB;IAC1BC,IAAI,eAAElB,OAAA,CAACL,eAAe;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAElB,OAAA,CAACJ,cAAc;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAElB,OAAA,CAACH,kBAAkB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAC;IAAEP;EAAI,CAAC,KAAK;IACnCV,YAAY,CAACU,GAAG,CAAC;EACnB,CAAC;EAED,oBACEjB,OAAA,CAACX,SAAS;IAACoC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAArB,QAAA,gBACvCL,OAAA,CAACE,KAAK;MACJyB,OAAO,EAAE,IAAK;MACdC,WAAW;MACXlB,SAAS,EAAEA,SAAU;MACrBmB,KAAK,EAAE,GAAI;MACXJ,KAAK,EAAE;QACLK,UAAU,EAAEjB,gBAAgB;QAC5BkB,WAAW,EAAE;MACf,CAAE;MAAA1B,QAAA,gBAEFL,OAAA;QAAKyB,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE,mBAAmB;UACjCC,SAAS,EAAE;QACb,CAAE;QAAA7B,QAAA,eACAL,OAAA,CAACP,KAAK;UAAC0C,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAAA/B,QAAA,gBACtCL,OAAA,CAACqC,kBAAkB;YACjBZ,KAAK,EAAE;cACLa,QAAQ,EAAE5B,SAAS,GAAG,MAAM,GAAG,MAAM;cACrC6B,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACd;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD,CAACZ,SAAS,iBACTV,OAAA,CAACI,KAAK;YAACqC,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEiB,MAAM,EAAE,CAAC;cAAEH,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAAC;UAEzD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENtB,OAAA,CAACV,IAAI;QACHqD,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACtC,WAAW,CAAE;QAC5BuC,KAAK,EAAE7B,SAAU;QACjB8B,OAAO,EAAEtB,eAAgB;QACzBC,KAAK,EAAE;UACLsB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,qBAAqB;UAC7BC,QAAQ,EAAE;QACZ;MAAE;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERtB,OAAA,CAACX,SAAS;MAAAgB,QAAA,gBACRL,OAAA,CAACC,MAAM;QACLwB,KAAK,EAAE;UACLO,OAAO,EAAE,QAAQ;UACjBF,UAAU,EAAEjB,gBAAgB;UAC5BoB,YAAY,EAAE,mBAAmB;UACjCiB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAA/C,QAAA,gBAEFL,OAAA,CAACP,KAAK;UAAAY,QAAA,gBACJL,OAAA;YACEqD,IAAI,EAAC,QAAQ;YACbP,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCe,KAAK,EAAE;cACLa,QAAQ,EAAE,MAAM;cAChBT,KAAK,EAAE,EAAE;cACTmB,MAAM,EAAE,EAAE;cACVD,MAAM,EAAE,MAAM;cACdjB,UAAU,EAAE,aAAa;cACzBwB,MAAM,EAAE,SAAS;cACjBJ,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBG,YAAY,EAAE;YAChB,CAAE;YAAAlD,QAAA,EAEDK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACTtB,OAAA,CAACI,KAAK;YAACqC,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEiB,MAAM,EAAE;YAAE,CAAE;YAAArC,QAAA,EACnC,EAAAI,eAAA,GAAAO,SAAS,CAACwC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxC,GAAG,KAAKX,WAAW,CAAC,cAAAG,eAAA,uBAAhDA,eAAA,CAAkDc,KAAK,KAAI;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAERtB,OAAA,CAACP,KAAK;UAAAY,QAAA,eACJL,OAAA;YAAMyB,KAAK,EAAE;cAAEc,KAAK,EAAE;YAAO,CAAE;YAAAlC,QAAA,EAAC;UAEhC;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETtB,OAAA,CAACG,OAAO;QACNsB,KAAK,EAAE;UACLiB,MAAM,EAAE,MAAM;UACdV,OAAO,EAAE,MAAM;UACfN,SAAS,EAAE,qBAAqB;UAChCI,UAAU,EAAEjB,gBAAgB;UAC5B0C,YAAY,EAAEzC,cAAc;UAC5BmC,QAAQ,EAAE;QACZ,CAAE;QAAA5C,QAAA,EAEDA;MAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACd,EAAA,CAxIIpB,MAAM;EAAA,QAING,KAAK,CAACwB,QAAQ;AAAA;AAAA2C,EAAA,GAJdtE,MAAM;AA0IZ,eAAeA,MAAM;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}