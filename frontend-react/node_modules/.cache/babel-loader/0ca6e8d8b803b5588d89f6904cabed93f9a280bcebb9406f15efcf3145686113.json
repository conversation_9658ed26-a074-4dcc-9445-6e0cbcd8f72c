{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Row, Col, Card, Button, message, Typography, Space, Statistic, Alert, Progress, Tag, Descriptions, Table } from 'antd';\nimport { InfoCircleOutlined, ReloadOutlined, CheckCircleOutlined, ExclamationCircleOutlined, DatabaseOutlined, CloudServerOutlined, RocketOutlined } from '@ant-design/icons';\nimport { systemAPI, stockAPI, featureAPI } from '../services/api';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst SystemStatus = () => {\n  _s();\n  var _systemStats$latest_d;\n  const [healthStatus, setHealthStatus] = useState(null);\n  const [systemStats, setSystemStats] = useState(null);\n  const [indexes, setIndexes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [healthLoading, setHealthLoading] = useState(false);\n\n  // 健康检查\n  const checkHealth = useCallback(async () => {\n    setHealthLoading(true);\n    try {\n      const response = await systemAPI.healthCheck();\n      if (response.status === 200) {\n        setHealthStatus(response.data);\n        message.success('✅ 系统健康检查通过');\n      } else {\n        setHealthStatus({\n          status: 'error',\n          message: '健康检查失败'\n        });\n        message.error('❌ 系统健康检查失败');\n      }\n    } catch (error) {\n      console.error('健康检查失败:', error);\n      setHealthStatus({\n        status: 'error',\n        message: error.message\n      });\n      message.error('❌ 无法连接到后端服务');\n    } finally {\n      setHealthLoading(false);\n    }\n  }, []);\n\n  // 加载系统统计\n  const loadSystemStats = useCallback(async () => {\n    try {\n      const response = await stockAPI.getDataStats();\n      if (response.data.success) {\n        setSystemStats(response.data.stats);\n      }\n    } catch (error) {\n      console.error('加载系统统计失败:', error);\n    }\n  }, []);\n\n  // 加载索引信息\n  const loadIndexes = useCallback(async () => {\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      }\n    } catch (error) {\n      console.error('加载索引信息失败:', error);\n    }\n  }, []);\n\n  // 刷新所有数据\n  const refreshAll = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([checkHealth(), loadSystemStats(), loadIndexes()]);\n      message.success('✅ 数据刷新完成');\n    } catch (error) {\n      message.error('❌ 数据刷新失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    refreshAll();\n  }, []);\n\n  // 索引表格列定义\n  const indexColumns = [{\n    title: '索引名称',\n    dataIndex: 'index_name',\n    key: 'index_name',\n    render: name => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      children: name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '向量数量',\n    dataIndex: 'total_vectors',\n    key: 'total_vectors',\n    render: count => count.toLocaleString()\n  }, {\n    title: '特征维度',\n    dataIndex: 'feature_dim',\n    key: 'feature_dim'\n  }, {\n    title: '窗口大小',\n    dataIndex: 'window_size',\n    key: 'window_size',\n    render: size => `${size} 天`\n  }, {\n    title: '构建时间',\n    dataIndex: 'build_time',\n    key: 'build_time',\n    render: time => dayjs(time).format('YYYY-MM-DD HH:mm:ss')\n  }, {\n    title: '状态',\n    key: 'status',\n    render: () => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"green\",\n      children: \"\\u6B63\\u5E38\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 21\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), \"\\u7CFB\\u7EDF\\u5065\\u5EB7\\u72B6\\u6001\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 23\n            }, this),\n            onClick: refreshAll,\n            loading: loading,\n            children: \"\\u5237\\u65B0\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"API\\u670D\\u52A1\\u72B6\\u6001\",\n                  value: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.status) === 'healthy' ? '正常' : '异常',\n                  prefix: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.status) === 'healthy' ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n                    style: {\n                      color: '#ff4d4f'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 25\n                  }, this),\n                  valueStyle: {\n                    color: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.status) === 'healthy' ? '#52c41a' : '#ff4d4f'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n                  value: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.database) === 'connected' ? '已连接' : '断开',\n                  prefix: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.database) === 'connected' ? /*#__PURE__*/_jsxDEV(DatabaseOutlined, {\n                    style: {\n                      color: '#52c41a'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n                    style: {\n                      color: '#ff4d4f'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this),\n                  valueStyle: {\n                    color: (healthStatus === null || healthStatus === void 0 ? void 0 : healthStatus.database) === 'connected' ? '#52c41a' : '#ff4d4f'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(CloudServerOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 27\n                  }, this),\n                  onClick: checkHealth,\n                  loading: healthLoading,\n                  block: true,\n                  children: \"\\u5065\\u5EB7\\u68C0\\u67E5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), healthStatus && healthStatus.status !== 'healthy' && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u7CFB\\u7EDF\\u5F02\\u5E38\",\n            description: healthStatus.message || '系统运行异常，请检查服务状态',\n            type: \"error\",\n            showIcon: true,\n            style: {\n              marginTop: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), \"\\u6570\\u636E\\u7EDF\\u8BA1\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this),\n          children: systemStats ? /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u80A1\\u7968\\u6570\\u91CF\",\n                  value: systemStats.stock_count,\n                  suffix: \"\\u53EA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u65E5\\u7EBF\\u6570\\u636E\",\n                  value: systemStats.daily_data_count,\n                  formatter: value => `${(value / 10000).toFixed(1)}万`,\n                  suffix: \"\\u6761\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u7279\\u5F81\\u5411\\u91CF\",\n                  value: systemStats.feature_count,\n                  suffix: \"\\u4E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u6700\\u65B0\\u6570\\u636E\",\n                  value: ((_systemStats$latest_d = systemStats.latest_date) === null || _systemStats$latest_d === void 0 ? void 0 : _systemStats$latest_d.slice(0, 10)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6570\\u636E\\u5B8C\\u6574\\u6027\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.min(100, systemStats.daily_data_count / 30000 * 100),\n                status: \"active\",\n                strokeColor: {\n                  '0%': '#108ee9',\n                  '100%': '#87d068'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: \"\\u57FA\\u4E8E\\u9884\\u671F\\u6570\\u636E\\u91CF\\u8BC4\\u4F30\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px 0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u52A0\\u8F7D\\u7EDF\\u8BA1\\u6570\\u636E\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(RocketOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), \"\\u7CFB\\u7EDF\\u4FE1\\u606F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 1,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7CFB\\u7EDF\\u7248\\u672C\",\n              children: \"v1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u524D\\u7AEF\\u6280\\u672F\",\n              children: \"React + Ant Design\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u540E\\u7AEF\\u6280\\u672F\",\n              children: \"FastAPI + Python\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6570\\u636E\\u5E93\",\n              children: \"MySQL 8.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u56FE\\u8868\\u5F15\\u64CE\",\n              children: \"TradingView Lightweight Charts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5411\\u91CF\\u68C0\\u7D22\",\n              children: \"FAISS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6570\\u636E\\u6E90\",\n              children: \"AkShare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u90E8\\u7F72\\u65F6\\u95F4\",\n              children: dayjs().format('YYYY-MM-DD HH:mm:ss')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\",\n            description: \"\\u6240\\u6709\\u6838\\u5FC3\\u529F\\u80FD\\u6A21\\u5757\\u8FD0\\u884C\\u7A33\\u5B9A\\uFF0C\\u6570\\u636E\\u66F4\\u65B0\\u53CA\\u65F6\",\n            type: \"success\",\n            showIcon: true,\n            style: {\n              marginTop: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), \"FAISS\\u7D22\\u5F15\\u72B6\\u6001\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this),\n          children: indexes.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: indexes,\n            columns: indexColumns,\n            pagination: false,\n            rowKey: \"index_name\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px 0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u6682\\u65E0\\u7D22\\u5F15\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemStatus, \"IRqboenns2wF5YdnqakatrfHSK0=\");\n_c = SystemStatus;\nexport default SystemStatus;\nvar _c;\n$RefreshReg$(_c, \"SystemStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Row", "Col", "Card", "<PERSON><PERSON>", "message", "Typography", "Space", "Statistic", "<PERSON><PERSON>", "Progress", "Tag", "Descriptions", "Table", "InfoCircleOutlined", "ReloadOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "DatabaseOutlined", "CloudServerOutlined", "RocketOutlined", "systemAPI", "stockAPI", "featureAPI", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "SystemStatus", "_s", "_systemStats$latest_d", "healthStatus", "setHealthStatus", "systemStats", "setSystemStats", "indexes", "setIndexes", "loading", "setLoading", "healthLoading", "setHealthLoading", "checkHealth", "response", "healthCheck", "status", "data", "success", "error", "console", "loadSystemStats", "getDataStats", "stats", "loadIndexes", "getIndexes", "refreshAll", "Promise", "all", "indexColumns", "title", "dataIndex", "key", "render", "name", "strong", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "count", "toLocaleString", "size", "time", "format", "color", "gutter", "span", "extra", "icon", "onClick", "xs", "sm", "md", "value", "prefix", "style", "valueStyle", "database", "type", "block", "description", "showIcon", "marginTop", "lg", "direction", "width", "stock_count", "suffix", "daily_data_count", "formatter", "toFixed", "feature_count", "latest_date", "slice", "percent", "Math", "min", "strokeColor", "fontSize", "textAlign", "padding", "column", "<PERSON><PERSON>", "label", "length", "dataSource", "columns", "pagination", "<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/SystemStatus.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Button,\n  message,\n  Typography,\n  Space,\n  Statistic,\n  Alert,\n  Progress,\n  Tag,\n  Descriptions,\n  Table,\n} from 'antd';\nimport {\n  InfoCircleOutlined,\n  ReloadOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  DatabaseOutlined,\n  CloudServerOutlined,\n  RocketOutlined,\n} from '@ant-design/icons';\nimport { systemAPI, stockAPI, featureAPI } from '../services/api';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n\nconst SystemStatus = () => {\n  const [healthStatus, setHealthStatus] = useState(null);\n  const [systemStats, setSystemStats] = useState(null);\n  const [indexes, setIndexes] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [healthLoading, setHealthLoading] = useState(false);\n\n  // 健康检查\n  const checkHealth = useCallback(async () => {\n    setHealthLoading(true);\n    try {\n      const response = await systemAPI.healthCheck();\n      if (response.status === 200) {\n        setHealthStatus(response.data);\n        message.success('✅ 系统健康检查通过');\n      } else {\n        setHealthStatus({ status: 'error', message: '健康检查失败' });\n        message.error('❌ 系统健康检查失败');\n      }\n    } catch (error) {\n      console.error('健康检查失败:', error);\n      setHealthStatus({ status: 'error', message: error.message });\n      message.error('❌ 无法连接到后端服务');\n    } finally {\n      setHealthLoading(false);\n    }\n  }, []);\n\n  // 加载系统统计\n  const loadSystemStats = useCallback(async () => {\n    try {\n      const response = await stockAPI.getDataStats();\n      if (response.data.success) {\n        setSystemStats(response.data.stats);\n      }\n    } catch (error) {\n      console.error('加载系统统计失败:', error);\n    }\n  }, []);\n\n  // 加载索引信息\n  const loadIndexes = useCallback(async () => {\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      }\n    } catch (error) {\n      console.error('加载索引信息失败:', error);\n    }\n  }, []);\n\n  // 刷新所有数据\n  const refreshAll = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([\n        checkHealth(),\n        loadSystemStats(),\n        loadIndexes(),\n      ]);\n      message.success('✅ 数据刷新完成');\n    } catch (error) {\n      message.error('❌ 数据刷新失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    refreshAll();\n  }, []);\n\n  // 索引表格列定义\n  const indexColumns = [\n    {\n      title: '索引名称',\n      dataIndex: 'index_name',\n      key: 'index_name',\n      render: (name) => <Text strong>{name}</Text>,\n    },\n    {\n      title: '向量数量',\n      dataIndex: 'total_vectors',\n      key: 'total_vectors',\n      render: (count) => count.toLocaleString(),\n    },\n    {\n      title: '特征维度',\n      dataIndex: 'feature_dim',\n      key: 'feature_dim',\n    },\n    {\n      title: '窗口大小',\n      dataIndex: 'window_size',\n      key: 'window_size',\n      render: (size) => `${size} 天`,\n    },\n    {\n      title: '构建时间',\n      dataIndex: 'build_time',\n      key: 'build_time',\n      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),\n    },\n    {\n      title: '状态',\n      key: 'status',\n      render: () => <Tag color=\"green\">正常</Tag>,\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={[16, 16]}>\n        {/* 系统健康状态 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <InfoCircleOutlined />\n                系统健康状态\n              </Space>\n            }\n            extra={\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={refreshAll}\n                loading={loading}\n              >\n                刷新状态\n              </Button>\n            }\n          >\n            <Row gutter={16}>\n              <Col xs={24} sm={12} md={8}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"API服务状态\"\n                    value={healthStatus?.status === 'healthy' ? '正常' : '异常'}\n                    prefix={\n                      healthStatus?.status === 'healthy' ? (\n                        <CheckCircleOutlined style={{ color: '#52c41a' }} />\n                      ) : (\n                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n                      )\n                    }\n                    valueStyle={{\n                      color: healthStatus?.status === 'healthy' ? '#52c41a' : '#ff4d4f',\n                    }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"数据库连接\"\n                    value={healthStatus?.database === 'connected' ? '已连接' : '断开'}\n                    prefix={\n                      healthStatus?.database === 'connected' ? (\n                        <DatabaseOutlined style={{ color: '#52c41a' }} />\n                      ) : (\n                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n                      )\n                    }\n                    valueStyle={{\n                      color: healthStatus?.database === 'connected' ? '#52c41a' : '#ff4d4f',\n                    }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card size=\"small\">\n                  <Button\n                    type=\"primary\"\n                    icon={<CloudServerOutlined />}\n                    onClick={checkHealth}\n                    loading={healthLoading}\n                    block\n                  >\n                    健康检查\n                  </Button>\n                </Card>\n              </Col>\n            </Row>\n\n            {healthStatus && healthStatus.status !== 'healthy' && (\n              <Alert\n                message=\"系统异常\"\n                description={healthStatus.message || '系统运行异常，请检查服务状态'}\n                type=\"error\"\n                showIcon\n                style={{ marginTop: '16px' }}\n              />\n            )}\n          </Card>\n        </Col>\n\n        {/* 数据统计 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <DatabaseOutlined />\n                数据统计\n              </Space>\n            }\n          >\n            {systemStats ? (\n              <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"股票数量\"\n                      value={systemStats.stock_count}\n                      suffix=\"只\"\n                    />\n                  </Col>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"日线数据\"\n                      value={systemStats.daily_data_count}\n                      formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n                      suffix=\"条\"\n                    />\n                  </Col>\n                </Row>\n\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"特征向量\"\n                      value={systemStats.feature_count}\n                      suffix=\"个\"\n                    />\n                  </Col>\n                  <Col span={12}>\n                    <Statistic\n                      title=\"最新数据\"\n                      value={systemStats.latest_date?.slice(0, 10) || 'N/A'}\n                    />\n                  </Col>\n                </Row>\n\n                {/* 数据完整性进度 */}\n                <div>\n                  <Text strong>数据完整性</Text>\n                  <Progress\n                    percent={Math.min(100, (systemStats.daily_data_count / 30000) * 100)}\n                    status=\"active\"\n                    strokeColor={{\n                      '0%': '#108ee9',\n                      '100%': '#87d068',\n                    }}\n                  />\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    基于预期数据量评估\n                  </Text>\n                </div>\n              </Space>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '40px 0' }}>\n                <Text type=\"secondary\">加载统计数据中...</Text>\n              </div>\n            )}\n          </Card>\n        </Col>\n\n        {/* 系统信息 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <RocketOutlined />\n                系统信息\n              </Space>\n            }\n          >\n            <Descriptions column={1} size=\"small\">\n              <Descriptions.Item label=\"系统版本\">v1.0.0</Descriptions.Item>\n              <Descriptions.Item label=\"前端技术\">React + Ant Design</Descriptions.Item>\n              <Descriptions.Item label=\"后端技术\">FastAPI + Python</Descriptions.Item>\n              <Descriptions.Item label=\"数据库\">MySQL 8.0</Descriptions.Item>\n              <Descriptions.Item label=\"图表引擎\">TradingView Lightweight Charts</Descriptions.Item>\n              <Descriptions.Item label=\"向量检索\">FAISS</Descriptions.Item>\n              <Descriptions.Item label=\"数据源\">AkShare</Descriptions.Item>\n              <Descriptions.Item label=\"部署时间\">\n                {dayjs().format('YYYY-MM-DD HH:mm:ss')}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Alert\n              message=\"系统运行正常\"\n              description=\"所有核心功能模块运行稳定，数据更新及时\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: '16px' }}\n            />\n          </Card>\n        </Col>\n\n        {/* 索引状态 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <DatabaseOutlined />\n                FAISS索引状态\n              </Space>\n            }\n          >\n            {indexes.length > 0 ? (\n              <Table\n                dataSource={indexes}\n                columns={indexColumns}\n                pagination={false}\n                rowKey=\"index_name\"\n                size=\"small\"\n              />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '40px 0' }}>\n                <Text type=\"secondary\">暂无索引数据</Text>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default SystemStatus;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,KAAK,QACA,MAAM;AACb,SACEC,kBAAkB,EAClBC,cAAc,EACdC,mBAAmB,EACnBC,yBAAyB,EACzBC,gBAAgB,EAChBC,mBAAmB,EACnBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;AACjE,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAElC,MAAMuB,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM4C,WAAW,GAAG1C,WAAW,CAAC,YAAY;IAC1CyC,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMtB,SAAS,CAACuB,WAAW,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QAC3BZ,eAAe,CAACU,QAAQ,CAACG,IAAI,CAAC;QAC9BzC,OAAO,CAAC0C,OAAO,CAAC,YAAY,CAAC;MAC/B,CAAC,MAAM;QACLd,eAAe,CAAC;UAAEY,MAAM,EAAE,OAAO;UAAExC,OAAO,EAAE;QAAS,CAAC,CAAC;QACvDA,OAAO,CAAC2C,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/Bf,eAAe,CAAC;QAAEY,MAAM,EAAE,OAAO;QAAExC,OAAO,EAAE2C,KAAK,CAAC3C;MAAQ,CAAC,CAAC;MAC5DA,OAAO,CAAC2C,KAAK,CAAC,aAAa,CAAC;IAC9B,CAAC,SAAS;MACRP,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,eAAe,GAAGlD,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMrB,QAAQ,CAAC6B,YAAY,CAAC,CAAC;MAC9C,IAAIR,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACzBZ,cAAc,CAACQ,QAAQ,CAACG,IAAI,CAACM,KAAK,CAAC;MACrC;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,WAAW,GAAGrD,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAMpB,UAAU,CAAC+B,UAAU,CAAC,CAAC;MAC9C,IAAIX,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QACzBV,UAAU,CAACM,QAAQ,CAACG,IAAI,CAACV,OAAO,CAAC;MACnC;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BhB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMiB,OAAO,CAACC,GAAG,CAAC,CAChBf,WAAW,CAAC,CAAC,EACbQ,eAAe,CAAC,CAAC,EACjBG,WAAW,CAAC,CAAC,CACd,CAAC;MACFhD,OAAO,CAAC0C,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACdwD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGC,IAAI,iBAAKrC,OAAA,CAACE,IAAI;MAACoC,MAAM;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAC7C,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAGQ,KAAK,IAAKA,KAAK,CAACC,cAAc,CAAC;EAC1C,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGU,IAAI,IAAK,GAAGA,IAAI;EAC3B,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGW,IAAI,IAAKjD,KAAK,CAACiD,IAAI,CAAC,CAACC,MAAM,CAAC,qBAAqB;EAC5D,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAAA,kBAAMpC,OAAA,CAACf,GAAG;MAACgE,KAAK,EAAC,OAAO;MAAAV,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAC1C,CAAC,CACF;EAED,oBACE3C,OAAA;IAAAuC,QAAA,eACEvC,OAAA,CAACzB,GAAG;MAAC2E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAX,QAAA,gBAEpBvC,OAAA,CAACxB,GAAG;QAAC2E,IAAI,EAAE,EAAG;QAAAZ,QAAA,eACZvC,OAAA,CAACvB,IAAI;UACHwD,KAAK,eACHjC,OAAA,CAACnB,KAAK;YAAA0D,QAAA,gBACJvC,OAAA,CAACZ,kBAAkB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDS,KAAK,eACHpD,OAAA,CAACtB,MAAM;YACL2E,IAAI,eAAErD,OAAA,CAACX,cAAc;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,OAAO,EAAEzB,UAAW;YACpBjB,OAAO,EAAEA,OAAQ;YAAA2B,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAJ,QAAA,gBAEDvC,OAAA,CAACzB,GAAG;YAAC2E,MAAM,EAAE,EAAG;YAAAX,QAAA,gBACdvC,OAAA,CAACxB,GAAG;cAAC+E,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACzBvC,OAAA,CAACvB,IAAI;gBAACqE,IAAI,EAAC,OAAO;gBAAAP,QAAA,eAChBvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,6BAAS;kBACfyB,KAAK,EAAE,CAAApD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,MAAM,MAAK,SAAS,GAAG,IAAI,GAAG,IAAK;kBACxDwC,MAAM,EACJ,CAAArD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,MAAM,MAAK,SAAS,gBAChCnB,OAAA,CAACV,mBAAmB;oBAACsE,KAAK,EAAE;sBAAEX,KAAK,EAAE;oBAAU;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpD3C,OAAA,CAACT,yBAAyB;oBAACqE,KAAK,EAAE;sBAAEX,KAAK,EAAE;oBAAU;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAE5D;kBACDkB,UAAU,EAAE;oBACVZ,KAAK,EAAE,CAAA3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,MAAM,MAAK,SAAS,GAAG,SAAS,GAAG;kBAC1D;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3C,OAAA,CAACxB,GAAG;cAAC+E,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACzBvC,OAAA,CAACvB,IAAI;gBAACqE,IAAI,EAAC,OAAO;gBAAAP,QAAA,eAChBvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,gCAAO;kBACbyB,KAAK,EAAE,CAAApD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,QAAQ,MAAK,WAAW,GAAG,KAAK,GAAG,IAAK;kBAC7DH,MAAM,EACJ,CAAArD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,QAAQ,MAAK,WAAW,gBACpC9D,OAAA,CAACR,gBAAgB;oBAACoE,KAAK,EAAE;sBAAEX,KAAK,EAAE;oBAAU;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEjD3C,OAAA,CAACT,yBAAyB;oBAACqE,KAAK,EAAE;sBAAEX,KAAK,EAAE;oBAAU;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAE5D;kBACDkB,UAAU,EAAE;oBACVZ,KAAK,EAAE,CAAA3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,QAAQ,MAAK,WAAW,GAAG,SAAS,GAAG;kBAC9D;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3C,OAAA,CAACxB,GAAG;cAAC+E,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACzBvC,OAAA,CAACvB,IAAI;gBAACqE,IAAI,EAAC,OAAO;gBAAAP,QAAA,eAChBvC,OAAA,CAACtB,MAAM;kBACLqF,IAAI,EAAC,SAAS;kBACdV,IAAI,eAAErD,OAAA,CAACP,mBAAmB;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC9BW,OAAO,EAAEtC,WAAY;kBACrBJ,OAAO,EAAEE,aAAc;kBACvBkD,KAAK;kBAAAzB,QAAA,EACN;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELrC,YAAY,IAAIA,YAAY,CAACa,MAAM,KAAK,SAAS,iBAChDnB,OAAA,CAACjB,KAAK;YACJJ,OAAO,EAAC,0BAAM;YACdsF,WAAW,EAAE3D,YAAY,CAAC3B,OAAO,IAAI,gBAAiB;YACtDoF,IAAI,EAAC,OAAO;YACZG,QAAQ;YACRN,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAO;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3C,OAAA,CAACxB,GAAG;QAAC+E,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,EAAG;QAAA7B,QAAA,eAClBvC,OAAA,CAACvB,IAAI;UACHwD,KAAK,eACHjC,OAAA,CAACnB,KAAK;YAAA0D,QAAA,gBACJvC,OAAA,CAACR,gBAAgB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,EAEA/B,WAAW,gBACVR,OAAA,CAACnB,KAAK;YAACwF,SAAS,EAAC,UAAU;YAACT,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAO,CAAE;YAACxB,IAAI,EAAC,OAAO;YAAAP,QAAA,gBAChEvC,OAAA,CAACzB,GAAG;cAAC2E,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdvC,OAAA,CAACxB,GAAG;gBAAC2E,IAAI,EAAE,EAAG;gBAAAZ,QAAA,eACZvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,0BAAM;kBACZyB,KAAK,EAAElD,WAAW,CAAC+D,WAAY;kBAC/BC,MAAM,EAAC;gBAAG;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3C,OAAA,CAACxB,GAAG;gBAAC2E,IAAI,EAAE,EAAG;gBAAAZ,QAAA,eACZvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,0BAAM;kBACZyB,KAAK,EAAElD,WAAW,CAACiE,gBAAiB;kBACpCC,SAAS,EAAGhB,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEiB,OAAO,CAAC,CAAC,CAAC,GAAI;kBACvDH,MAAM,EAAC;gBAAG;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3C,OAAA,CAACzB,GAAG;cAAC2E,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdvC,OAAA,CAACxB,GAAG;gBAAC2E,IAAI,EAAE,EAAG;gBAAAZ,QAAA,eACZvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,0BAAM;kBACZyB,KAAK,EAAElD,WAAW,CAACoE,aAAc;kBACjCJ,MAAM,EAAC;gBAAG;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3C,OAAA,CAACxB,GAAG;gBAAC2E,IAAI,EAAE,EAAG;gBAAAZ,QAAA,eACZvC,OAAA,CAAClB,SAAS;kBACRmD,KAAK,EAAC,0BAAM;kBACZyB,KAAK,EAAE,EAAArD,qBAAA,GAAAG,WAAW,CAACqE,WAAW,cAAAxE,qBAAA,uBAAvBA,qBAAA,CAAyByE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI;gBAAM;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA,CAACE,IAAI;gBAACoC,MAAM;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzB3C,OAAA,CAAChB,QAAQ;gBACP+F,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAGzE,WAAW,CAACiE,gBAAgB,GAAG,KAAK,GAAI,GAAG,CAAE;gBACrEtD,MAAM,EAAC,QAAQ;gBACf+D,WAAW,EAAE;kBACX,IAAI,EAAE,SAAS;kBACf,MAAM,EAAE;gBACV;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF3C,OAAA,CAACE,IAAI;gBAAC6D,IAAI,EAAC,WAAW;gBAACH,KAAK,EAAE;kBAAEuB,QAAQ,EAAE;gBAAO,CAAE;gBAAA5C,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAER3C,OAAA;YAAK4D,KAAK,EAAE;cAAEwB,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAS,CAAE;YAAA9C,QAAA,eACrDvC,OAAA,CAACE,IAAI;cAAC6D,IAAI,EAAC,WAAW;cAAAxB,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3C,OAAA,CAACxB,GAAG;QAAC+E,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,EAAG;QAAA7B,QAAA,eAClBvC,OAAA,CAACvB,IAAI;UACHwD,KAAK,eACHjC,OAAA,CAACnB,KAAK;YAAA0D,QAAA,gBACJvC,OAAA,CAACN,cAAc;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,gBAEDvC,OAAA,CAACd,YAAY;YAACoG,MAAM,EAAE,CAAE;YAACxC,IAAI,EAAC,OAAO;YAAAP,QAAA,gBACnCvC,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC1D3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACtE3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACpE3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAjD,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC5D3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAAC;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAClF3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACzD3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAjD,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC1D3C,OAAA,CAACd,YAAY,CAACqG,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAjD,QAAA,EAC5BzC,KAAK,CAAC,CAAC,CAACkD,MAAM,CAAC,qBAAqB;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEf3C,OAAA,CAACjB,KAAK;YACJJ,OAAO,EAAC,sCAAQ;YAChBsF,WAAW,EAAC,oHAAqB;YACjCF,IAAI,EAAC,SAAS;YACdG,QAAQ;YACRN,KAAK,EAAE;cAAEO,SAAS,EAAE;YAAO;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3C,OAAA,CAACxB,GAAG;QAAC2E,IAAI,EAAE,EAAG;QAAAZ,QAAA,eACZvC,OAAA,CAACvB,IAAI;UACHwD,KAAK,eACHjC,OAAA,CAACnB,KAAK;YAAA0D,QAAA,gBACJvC,OAAA,CAACR,gBAAgB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iCAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAJ,QAAA,EAEA7B,OAAO,CAAC+E,MAAM,GAAG,CAAC,gBACjBzF,OAAA,CAACb,KAAK;YACJuG,UAAU,EAAEhF,OAAQ;YACpBiF,OAAO,EAAE3D,YAAa;YACtB4D,UAAU,EAAE,KAAM;YAClBC,MAAM,EAAC,YAAY;YACnB/C,IAAI,EAAC;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,gBAEF3C,OAAA;YAAK4D,KAAK,EAAE;cAAEwB,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAS,CAAE;YAAA9C,QAAA,eACrDvC,OAAA,CAACE,IAAI;cAAC6D,IAAI,EAAC,WAAW;cAAAxB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAzUID,YAAY;AAAA2F,EAAA,GAAZ3F,YAAY;AA2UlB,eAAeA,YAAY;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}