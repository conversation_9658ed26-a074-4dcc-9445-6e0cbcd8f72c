{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PictureFilledSvg from \"@ant-design/icons-svg/es/asn/PictureFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PictureFilled = function PictureFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PictureFilledSvg\n  }));\n};\n\n/**![picture](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PictureFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PictureFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "PictureFilledSvg", "AntdIcon", "PictureFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/PictureFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PictureFilledSvg from \"@ant-design/icons-svg/es/asn/PictureFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PictureFilled = function PictureFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PictureFilledSvg\n  }));\n};\n\n/**![picture](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PictureFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PictureFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}