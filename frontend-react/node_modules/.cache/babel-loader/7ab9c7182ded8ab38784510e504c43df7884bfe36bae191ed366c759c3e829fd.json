{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle } from \"../util\";\nvar Dot = function Dot(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    included = _React$useContext.included,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd;\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd;\n\n  // ============================ Offset ============================\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n};\nexport default Dot;", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "classNames", "React", "SliderContext", "getDirectionStyle", "Dot", "props", "prefixCls", "value", "style", "activeStyle", "_React$useContext", "useContext", "min", "max", "direction", "included", "includedStart", "includedEnd", "dotClassName", "concat", "active", "mergedStyle", "createElement", "className"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-slider/es/Steps/Dot.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle } from \"../util\";\nvar Dot = function Dot(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    included = _React$useContext.included,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd;\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd;\n\n  // ============================ Offset ============================\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n};\nexport default Dot;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,KAAK,EAAE;EAC5B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;EACjC,IAAIC,iBAAiB,GAAGT,KAAK,CAACU,UAAU,CAACT,aAAa,CAAC;IACrDU,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;IACvCC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,aAAa,GAAGN,iBAAiB,CAACM,aAAa;IAC/CC,WAAW,GAAGP,iBAAiB,CAACO,WAAW;EAC7C,IAAIC,YAAY,GAAG,EAAE,CAACC,MAAM,CAACb,SAAS,EAAE,MAAM,CAAC;EAC/C,IAAIc,MAAM,GAAGL,QAAQ,IAAIC,aAAa,IAAIT,KAAK,IAAIA,KAAK,IAAIU,WAAW;;EAEvE;EACA,IAAII,WAAW,GAAGtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEI,iBAAiB,CAACW,SAAS,EAAEP,KAAK,EAAEK,GAAG,EAAEC,GAAG,CAAC,CAAC,EAAE,OAAOL,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACD,KAAK,CAAC,GAAGC,KAAK,CAAC;EACrJ,IAAIY,MAAM,EAAE;IACVC,WAAW,GAAGtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC,EAAE,OAAOZ,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACF,KAAK,CAAC,GAAGE,WAAW,CAAC;EACnI;EACA,OAAO,aAAaR,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEvB,UAAU,CAACkB,YAAY,EAAEpB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACD,YAAY,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,CAAC;IACpGZ,KAAK,EAAEa;EACT,CAAC,CAAC;AACJ,CAAC;AACD,eAAejB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}