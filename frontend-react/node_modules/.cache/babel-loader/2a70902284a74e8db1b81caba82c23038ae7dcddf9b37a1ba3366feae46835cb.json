{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport XFilledSvg from \"@ant-design/icons-svg/es/asn/XFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar XFilled = function XFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: XFilledSvg\n  }));\n};\n\n/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNODIzLjExIDkxMkgyMDAuOUE4OC45IDg4LjkgMCAwMTExMiA4MjMuMTFWMjAwLjlBODguOSA4OC45IDAgMDEyMDAuODkgMTEySDgyMy4xQTg4LjkgODguOSAwIDAxOTEyIDIwMC44OVY4MjMuMUE4OC45IDg4LjkgMCAwMTgyMy4xMSA5MTIiIC8+PHBhdGggZD0iTTc0MCA3MzVINTk2Ljk0TDI4NiAyOTFoMTQzLjA2em0tMTI2LjAxLTM3LjY1aDU2Ljk2TDQxMiAzMjguNjVoLTU2Ljk2eiIgZmlsbC1ydWxlPSJub256ZXJvIiAvPjxwYXRoIGQ9Ik0zMzEuMyA3MzVMNDkxIDU0OS43MyA0NzAuMTEgNTIyIDI4NiA3MzV6TTUyMSA0NjAuMzlMNTQxLjIxIDQ4OSA3MTUgMjg5aC00NC42N3oiIGZpbGwtcnVsZT0ibm9uemVybyIgLz48L2c+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(XFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'XFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "XFilledSvg", "AntdIcon", "XFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/XFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport XFilledSvg from \"@ant-design/icons-svg/es/asn/XFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar XFilled = function XFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: XFilledSvg\n  }));\n};\n\n/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNODIzLjExIDkxMkgyMDAuOUE4OC45IDg4LjkgMCAwMTExMiA4MjMuMTFWMjAwLjlBODguOSA4OC45IDAgMDEyMDAuODkgMTEySDgyMy4xQTg4LjkgODguOSAwIDAxOTEyIDIwMC44OVY4MjMuMUE4OC45IDg4LjkgMCAwMTgyMy4xMSA5MTIiIC8+PHBhdGggZD0iTTc0MCA3MzVINTk2Ljk0TDI4NiAyOTFoMTQzLjA2em0tMTI2LjAxLTM3LjY1aDU2Ljk2TDQxMiAzMjguNjVoLTU2Ljk2eiIgZmlsbC1ydWxlPSJub256ZXJvIiAvPjxwYXRoIGQ9Ik0zMzEuMyA3MzVMNDkxIDU0OS43MyA0NzAuMTEgNTIyIDI4NiA3MzV6TTUyMSA0NjAuMzlMNTQxLjIxIDQ4OSA3MTUgMjg5aC00NC42N3oiIGZpbGwtcnVsZT0ibm9uemVybyIgLz48L2c+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(XFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'XFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzC,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,OAAO,CAAC;AACpD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,SAAS;AACjC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}