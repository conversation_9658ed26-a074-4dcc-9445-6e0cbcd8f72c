{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nexport default function useFixedInfo(flattenColumns, stickyOffsets, direction) {\n  var fixedInfoList = flattenColumns.map(function (_, colIndex) {\n    return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n  });\n  return useMemo(function () {\n    return fixedInfoList;\n  }, [fixedInfoList], function (prev, next) {\n    return !isEqual(prev, next);\n  });\n}", "map": {"version": 3, "names": ["useMemo", "isEqual", "getCellFixedInfo", "useFixedInfo", "flattenColumns", "stickyOffsets", "direction", "fixedInfoList", "map", "_", "colIndex", "prev", "next"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-table/es/hooks/useFixedInfo.js"], "sourcesContent": ["import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nexport default function useFixedInfo(flattenColumns, stickyOffsets, direction) {\n  var fixedInfoList = flattenColumns.map(function (_, colIndex) {\n    return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction);\n  });\n  return useMemo(function () {\n    return fixedInfoList;\n  }, [fixedInfoList], function (prev, next) {\n    return !isEqual(prev, next);\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,eAAe,SAASC,YAAYA,CAACC,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAE;EAC7E,IAAIC,aAAa,GAAGH,cAAc,CAACI,GAAG,CAAC,UAAUC,CAAC,EAAEC,QAAQ,EAAE;IAC5D,OAAOR,gBAAgB,CAACQ,QAAQ,EAAEA,QAAQ,EAAEN,cAAc,EAAEC,aAAa,EAAEC,SAAS,CAAC;EACvF,CAAC,CAAC;EACF,OAAON,OAAO,CAAC,YAAY;IACzB,OAAOO,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAAC,EAAE,UAAUI,IAAI,EAAEC,IAAI,EAAE;IACxC,OAAO,CAACX,OAAO,CAACU,IAAI,EAAEC,IAAI,CAAC;EAC7B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}