{"ast": null, "code": "var camel2hyphen = function (str) {\n  return str.replace(/[A-Z]/g, function (match) {\n    return '-' + match.toLowerCase();\n  }).toLowerCase();\n};\nmodule.exports = camel2hyphen;", "map": {"version": 3, "names": ["camel2hyphen", "str", "replace", "match", "toLowerCase", "module", "exports"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/string-convert/camel2hyphen.js"], "sourcesContent": ["var camel2hyphen = function (str) {\n  return str\n          .replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n          })\n          .toLowerCase();\n};\n\nmodule.exports = camel2hyphen;"], "mappings": "AAAA,IAAIA,YAAY,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAChC,OAAOA,GAAG,CACDC,OAAO,CAAC,QAAQ,EAAE,UAAUC,KAAK,EAAE;IAClC,OAAO,GAAG,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;EAClC,CAAC,CAAC,CACDA,WAAW,CAAC,CAAC;AACxB,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}