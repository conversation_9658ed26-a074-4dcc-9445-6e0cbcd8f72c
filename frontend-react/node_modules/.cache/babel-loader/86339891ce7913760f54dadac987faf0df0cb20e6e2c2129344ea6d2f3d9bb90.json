{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RobotFilledSvg from \"@ant-design/icons-svg/es/asn/RobotFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RobotFilled = function RobotFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RobotFilledSvg\n  }));\n};\n\n/**![robot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NTIgNjRIMTcyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjBjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjgwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMDAgMzI4YzAtMzMuMSAyNi45LTYwIDYwLTYwczYwIDI2LjkgNjAgNjAtMjYuOSA2MC02MCA2MC02MC0yNi45LTYwLTYwem0zNzIgMjQ4YzAgNC40LTMuNiA4LTggOEgzNjBjLTQuNCAwLTgtMy42LTgtOHYtNjBjMC00LjQgMy42LTggOC04aDMwNGM0LjQgMCA4IDMuNiA4IDh2NjB6bS04LTE4OGMtMzMuMSAwLTYwLTI2LjktNjAtNjBzMjYuOS02MCA2MC02MCA2MCAyNi45IDYwIDYwLTI2LjkgNjAtNjAgNjB6bTEzNSA0NzZIMjI1Yy0xMy44IDAtMjUgMTQuMy0yNSAzMnY1NmMwIDQuNCAyLjggOCA2LjIgOGg2MTEuNWMzLjQgMCA2LjItMy42IDYuMi04di01NmMuMS0xNy43LTExLjEtMzItMjQuOS0zMnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RobotFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RobotFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "RobotFilledSvg", "AntdIcon", "RobotFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/RobotFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RobotFilledSvg from \"@ant-design/icons-svg/es/asn/RobotFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RobotFilled = function RobotFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RobotFilledSvg\n  }));\n};\n\n/**![robot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NTIgNjRIMTcyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjBjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjgwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMDAgMzI4YzAtMzMuMSAyNi45LTYwIDYwLTYwczYwIDI2LjkgNjAgNjAtMjYuOSA2MC02MCA2MC02MC0yNi45LTYwLTYwem0zNzIgMjQ4YzAgNC40LTMuNiA4LTggOEgzNjBjLTQuNCAwLTgtMy42LTgtOHYtNjBjMC00LjQgMy42LTggOC04aDMwNGM0LjQgMCA4IDMuNiA4IDh2NjB6bS04LTE4OGMtMzMuMSAwLTYwLTI2LjktNjAtNjBzMjYuOS02MCA2MC02MCA2MCAyNi45IDYwIDYwLTI2LjkgNjAtNjAgNjB6bTEzNSA0NzZIMjI1Yy0xMy44IDAtMjUgMTQuMy0yNSAzMnY1NmMwIDQuNCAyLjggOCA2LjIgOGg2MTEuNWMzLjQgMCA2LjItMy42IDYuMi04di01NmMuMS0xNy43LTExLjEtMzItMjQuOS0zMnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RobotFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RobotFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}