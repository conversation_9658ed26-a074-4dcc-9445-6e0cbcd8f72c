{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport Layout from './components/Layout';\nimport DataManagement from './pages/DataManagement';\nimport FeatureEngineering from './pages/FeatureEngineering';\nimport SimilaritySearch from './pages/SimilaritySearch';\nimport SystemStatus from './pages/SystemStatus';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('data-management');\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'data-management':\n        return /*#__PURE__*/_jsxDEV(DataManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 16\n        }, this);\n      case 'feature-engineering':\n        return /*#__PURE__*/_jsxDEV(FeatureEngineering, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'similarity-search':\n        return /*#__PURE__*/_jsxDEV(SimilaritySearch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'system-status':\n        return /*#__PURE__*/_jsxDEV(SystemStatus, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(DataManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      currentPage: currentPage,\n      onPageChange: setCurrentPage,\n      children: renderPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"cqUBukDgfS5DdB8lZRoYqmObd6k=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "Layout", "DataManagement", "FeatureEngineering", "SimilaritySearch", "SystemStatus", "jsxDEV", "_jsxDEV", "App", "_s", "currentPage", "setCurrentPage", "renderPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "locale", "children", "onPageChange", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport Layout from './components/Layout';\nimport DataManagement from './pages/DataManagement';\nimport FeatureEngineering from './pages/FeatureEngineering';\nimport SimilaritySearch from './pages/SimilaritySearch';\nimport SystemStatus from './pages/SystemStatus';\nimport './App.css';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('data-management');\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'data-management':\n        return <DataManagement />;\n      case 'feature-engineering':\n        return <FeatureEngineering />;\n      case 'similarity-search':\n        return <SimilaritySearch />;\n      case 'system-status':\n        return <SystemStatus />;\n      default:\n        return <DataManagement />;\n    }\n  };\n\n  return (\n    <ConfigProvider locale={zhCN}>\n      <Layout currentPage={currentPage} onPageChange={setCurrentPage}>\n        {renderPage()}\n      </Layout>\n    </ConfigProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,iBAAiB,CAAC;EAEjE,MAAMc,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQF,WAAW;MACjB,KAAK,iBAAiB;QACpB,oBAAOH,OAAA,CAACL,cAAc;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,qBAAqB;QACxB,oBAAOT,OAAA,CAACJ,kBAAkB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,mBAAmB;QACtB,oBAAOT,OAAA,CAACH,gBAAgB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,eAAe;QAClB,oBAAOT,OAAA,CAACF,YAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB;QACE,oBAAOT,OAAA,CAACL,cAAc;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC7B;EACF,CAAC;EAED,oBACET,OAAA,CAACR,cAAc;IAACkB,MAAM,EAAEjB,IAAK;IAAAkB,QAAA,eAC3BX,OAAA,CAACN,MAAM;MAACS,WAAW,EAAEA,WAAY;MAACS,YAAY,EAAER,cAAe;MAAAO,QAAA,EAC5DN,UAAU,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACP,EAAA,CAzBQD,GAAG;AAAAY,EAAA,GAAHZ,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}