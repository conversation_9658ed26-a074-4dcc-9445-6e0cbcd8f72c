{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { Color } from \"./color\";\nexport var ColorPickerPrefixCls = 'rc-color-picker';\nexport var generateColor = function generateColor(color) {\n  if (color instanceof Color) {\n    return color;\n  }\n  return new Color(color);\n};\nexport var defaultColor = generateColor('#1677ff');\nexport var calculateColor = function calculateColor(props) {\n  var offset = props.offset,\n    targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    color = props.color,\n    type = props.type;\n  var _containerRef$current = containerRef.current.getBoundingClientRect(),\n    width = _containerRef$current.width,\n    height = _containerRef$current.height;\n  var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n    targetWidth = _targetRef$current$ge.width,\n    targetHeight = _targetRef$current$ge.height;\n  var centerOffsetX = targetWidth / 2;\n  var centerOffsetY = targetHeight / 2;\n  var saturation = (offset.x + centerOffsetX) / width;\n  var bright = 1 - (offset.y + centerOffsetY) / height;\n  var hsb = color.toHsb();\n  var alphaOffset = saturation;\n  var hueOffset = (offset.x + centerOffsetX) / width * 360;\n  if (type) {\n    switch (type) {\n      case 'hue':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          h: hueOffset <= 0 ? 0 : hueOffset\n        }));\n      case 'alpha':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          a: alphaOffset <= 0 ? 0 : alphaOffset\n        }));\n    }\n  }\n  return generateColor({\n    h: hsb.h,\n    s: saturation <= 0 ? 0 : saturation,\n    b: bright >= 1 ? 1 : bright,\n    a: hsb.a\n  });\n};\nexport var calcOffset = function calcOffset(color, type) {\n  var hsb = color.toHsb();\n  switch (type) {\n    case 'hue':\n      return {\n        x: hsb.h / 360 * 100,\n        y: 50\n      };\n    case 'alpha':\n      return {\n        x: color.a * 100,\n        y: 50\n      };\n\n    // Picker panel\n    default:\n      return {\n        x: hsb.s * 100,\n        y: (1 - hsb.b) * 100\n      };\n  }\n};", "map": {"version": 3, "names": ["_objectSpread", "Color", "ColorPickerPrefixCls", "generateColor", "color", "defaultColor", "calculateColor", "props", "offset", "targetRef", "containerRef", "type", "_containerRef$current", "current", "getBoundingClientRect", "width", "height", "_targetRef$current$ge", "targetWidth", "targetHeight", "centerOffsetX", "centerOffsetY", "saturation", "x", "bright", "y", "hsb", "toHsb", "alphaOffset", "hueOffset", "h", "a", "s", "b", "calcOffset"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@rc-component/color-picker/es/util.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { Color } from \"./color\";\nexport var ColorPickerPrefixCls = 'rc-color-picker';\nexport var generateColor = function generateColor(color) {\n  if (color instanceof Color) {\n    return color;\n  }\n  return new Color(color);\n};\nexport var defaultColor = generateColor('#1677ff');\nexport var calculateColor = function calculateColor(props) {\n  var offset = props.offset,\n    targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    color = props.color,\n    type = props.type;\n  var _containerRef$current = containerRef.current.getBoundingClientRect(),\n    width = _containerRef$current.width,\n    height = _containerRef$current.height;\n  var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n    targetWidth = _targetRef$current$ge.width,\n    targetHeight = _targetRef$current$ge.height;\n  var centerOffsetX = targetWidth / 2;\n  var centerOffsetY = targetHeight / 2;\n  var saturation = (offset.x + centerOffsetX) / width;\n  var bright = 1 - (offset.y + centerOffsetY) / height;\n  var hsb = color.toHsb();\n  var alphaOffset = saturation;\n  var hueOffset = (offset.x + centerOffsetX) / width * 360;\n  if (type) {\n    switch (type) {\n      case 'hue':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          h: hueOffset <= 0 ? 0 : hueOffset\n        }));\n      case 'alpha':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          a: alphaOffset <= 0 ? 0 : alphaOffset\n        }));\n    }\n  }\n  return generateColor({\n    h: hsb.h,\n    s: saturation <= 0 ? 0 : saturation,\n    b: bright >= 1 ? 1 : bright,\n    a: hsb.a\n  });\n};\nexport var calcOffset = function calcOffset(color, type) {\n  var hsb = color.toHsb();\n  switch (type) {\n    case 'hue':\n      return {\n        x: hsb.h / 360 * 100,\n        y: 50\n      };\n    case 'alpha':\n      return {\n        x: color.a * 100,\n        y: 50\n      };\n\n    // Picker panel\n    default:\n      return {\n        x: hsb.s * 100,\n        y: (1 - hsb.b) * 100\n      };\n  }\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,IAAIC,oBAAoB,GAAG,iBAAiB;AACnD,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EACvD,IAAIA,KAAK,YAAYH,KAAK,EAAE;IAC1B,OAAOG,KAAK;EACd;EACA,OAAO,IAAIH,KAAK,CAACG,KAAK,CAAC;AACzB,CAAC;AACD,OAAO,IAAIC,YAAY,GAAGF,aAAa,CAAC,SAAS,CAAC;AAClD,OAAO,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCN,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBO,IAAI,GAAGJ,KAAK,CAACI,IAAI;EACnB,IAAIC,qBAAqB,GAAGF,YAAY,CAACG,OAAO,CAACC,qBAAqB,CAAC,CAAC;IACtEC,KAAK,GAAGH,qBAAqB,CAACG,KAAK;IACnCC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM;EACvC,IAAIC,qBAAqB,GAAGR,SAAS,CAACI,OAAO,CAACC,qBAAqB,CAAC,CAAC;IACnEI,WAAW,GAAGD,qBAAqB,CAACF,KAAK;IACzCI,YAAY,GAAGF,qBAAqB,CAACD,MAAM;EAC7C,IAAII,aAAa,GAAGF,WAAW,GAAG,CAAC;EACnC,IAAIG,aAAa,GAAGF,YAAY,GAAG,CAAC;EACpC,IAAIG,UAAU,GAAG,CAACd,MAAM,CAACe,CAAC,GAAGH,aAAa,IAAIL,KAAK;EACnD,IAAIS,MAAM,GAAG,CAAC,GAAG,CAAChB,MAAM,CAACiB,CAAC,GAAGJ,aAAa,IAAIL,MAAM;EACpD,IAAIU,GAAG,GAAGtB,KAAK,CAACuB,KAAK,CAAC,CAAC;EACvB,IAAIC,WAAW,GAAGN,UAAU;EAC5B,IAAIO,SAAS,GAAG,CAACrB,MAAM,CAACe,CAAC,GAAGH,aAAa,IAAIL,KAAK,GAAG,GAAG;EACxD,IAAIJ,IAAI,EAAE;IACR,QAAQA,IAAI;MACV,KAAK,KAAK;QACR,OAAOR,aAAa,CAACH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7DI,CAAC,EAAED,SAAS,IAAI,CAAC,GAAG,CAAC,GAAGA;QAC1B,CAAC,CAAC,CAAC;MACL,KAAK,OAAO;QACV,OAAO1B,aAAa,CAACH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7DK,CAAC,EAAEH,WAAW,IAAI,CAAC,GAAG,CAAC,GAAGA;QAC5B,CAAC,CAAC,CAAC;IACP;EACF;EACA,OAAOzB,aAAa,CAAC;IACnB2B,CAAC,EAAEJ,GAAG,CAACI,CAAC;IACRE,CAAC,EAAEV,UAAU,IAAI,CAAC,GAAG,CAAC,GAAGA,UAAU;IACnCW,CAAC,EAAET,MAAM,IAAI,CAAC,GAAG,CAAC,GAAGA,MAAM;IAC3BO,CAAC,EAAEL,GAAG,CAACK;EACT,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAAC9B,KAAK,EAAEO,IAAI,EAAE;EACvD,IAAIe,GAAG,GAAGtB,KAAK,CAACuB,KAAK,CAAC,CAAC;EACvB,QAAQhB,IAAI;IACV,KAAK,KAAK;MACR,OAAO;QACLY,CAAC,EAAEG,GAAG,CAACI,CAAC,GAAG,GAAG,GAAG,GAAG;QACpBL,CAAC,EAAE;MACL,CAAC;IACH,KAAK,OAAO;MACV,OAAO;QACLF,CAAC,EAAEnB,KAAK,CAAC2B,CAAC,GAAG,GAAG;QAChBN,CAAC,EAAE;MACL,CAAC;;IAEH;IACA;MACE,OAAO;QACLF,CAAC,EAAEG,GAAG,CAACM,CAAC,GAAG,GAAG;QACdP,CAAC,EAAE,CAAC,CAAC,GAAGC,GAAG,CAACO,CAAC,IAAI;MACnB,CAAC;EACL;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}