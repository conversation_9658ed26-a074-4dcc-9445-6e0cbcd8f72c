{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VideoCameraFilledSvg from \"@ant-design/icons-svg/es/asn/VideoCameraFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VideoCameraFilled = function VideoCameraFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VideoCameraFilledSvg\n  }));\n};\n\n/**![video-camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMiAzMDIuM0w3ODQgMzc2VjIyNGMwLTM1LjMtMjguNy02NC02NC02NEgxMjhjLTM1LjMgMC02NCAyOC43LTY0IDY0djU3NmMwIDM1LjMgMjguNyA2NCA2NCA2NGg1OTJjMzUuMyAwIDY0LTI4LjcgNjQtNjRWNjQ4bDEyOCA3My43YzIxLjMgMTIuMyA0OC0zLjEgNDgtMjcuNlYzMzBjMC0yNC42LTI2LjctNDAtNDgtMjcuN3pNMzI4IDM1MmMwIDQuNC0zLjYgOC04IDhIMjA4Yy00LjQgMC04LTMuNi04LTh2LTQ4YzAtNC40IDMuNi04IDgtOGgxMTJjNC40IDAgOCAzLjYgOCA4djQ4em01NjAgMjczbC0xMDQtNTkuOFY0NTguOUw4ODggMzk5djIyNnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VideoCameraFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VideoCameraFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "VideoCameraFilledSvg", "AntdIcon", "VideoCameraFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/VideoCameraFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport VideoCameraFilledSvg from \"@ant-design/icons-svg/es/asn/VideoCameraFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar VideoCameraFilled = function VideoCameraFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: VideoCameraFilledSvg\n  }));\n};\n\n/**![video-camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMiAzMDIuM0w3ODQgMzc2VjIyNGMwLTM1LjMtMjguNy02NC02NC02NEgxMjhjLTM1LjMgMC02NCAyOC43LTY0IDY0djU3NmMwIDM1LjMgMjguNyA2NCA2NCA2NGg1OTJjMzUuMyAwIDY0LTI4LjcgNjQtNjRWNjQ4bDEyOCA3My43YzIxLjMgMTIuMyA0OC0zLjEgNDgtMjcuNlYzMzBjMC0yNC42LTI2LjctNDAtNDgtMjcuN3pNMzI4IDM1MmMwIDQuNC0zLjYgOC04IDhIMjA4Yy00LjQgMC04LTMuNi04LTh2LTQ4YzAtNC40IDMuNi04IDgtOGgxMTJjNC40IDAgOCAzLjYgOCA4djQ4em01NjAgMjczbC0xMDQtNTkuOFY0NTguOUw4ODggMzk5djIyNnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(VideoCameraFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'VideoCameraFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}