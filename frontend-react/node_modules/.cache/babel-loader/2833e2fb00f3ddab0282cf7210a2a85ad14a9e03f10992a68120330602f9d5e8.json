{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SkinTwoToneSvg from \"@ant-design/icons-svg/es/asn/SkinTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SkinTwoTone = function SkinTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SkinTwoToneSvg\n  }));\n};\n\n/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAzMThjLTc5LjIgMC0xNDguNS00OC44LTE3Ni43LTEyMEgxODJ2MTk2aDExOXY0MzJoNDIyVjM5NGgxMTlWMTk4SDY4OC43Yy0yOC4yIDcxLjItOTcuNSAxMjAtMTc2LjcgMTIweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODcwIDEyNkg2NjMuOGMtMTcuNCAwLTMyLjkgMTEuOS0zNyAyOS4zQzYxNC4zIDIwOC4xIDU2NyAyNDYgNTEyIDI0NnMtMTAyLjMtMzcuOS0xMTQuOC05MC43YTM3LjkzIDM3LjkzIDAgMDAtMzctMjkuM0gxNTRhNDQgNDQgMCAwMC00NCA0NHYyNTJhNDQgNDQgMCAwMDQ0IDQ0aDc1djM4OGE0NCA0NCAwIDAwNDQgNDRoNDc4YTQ0IDQ0IDAgMDA0NC00NFY0NjZoNzVhNDQgNDQgMCAwMDQ0LTQ0VjE3MGE0NCA0NCAwIDAwLTQ0LTQ0em0tMjggMjY4SDcyM3Y0MzJIMzAxVjM5NEgxODJWMTk4aDE1My4zYzI4LjIgNzEuMiA5Ny41IDEyMCAxNzYuNyAxMjBzMTQ4LjUtNDguOCAxNzYuNy0xMjBIODQydjE5NnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SkinTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SkinTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SkinTwoToneSvg", "AntdIcon", "SkinTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/SkinTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SkinTwoToneSvg from \"@ant-design/icons-svg/es/asn/SkinTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SkinTwoTone = function SkinTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SkinTwoToneSvg\n  }));\n};\n\n/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAzMThjLTc5LjIgMC0xNDguNS00OC44LTE3Ni43LTEyMEgxODJ2MTk2aDExOXY0MzJoNDIyVjM5NGgxMTlWMTk4SDY4OC43Yy0yOC4yIDcxLjItOTcuNSAxMjAtMTc2LjcgMTIweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODcwIDEyNkg2NjMuOGMtMTcuNCAwLTMyLjkgMTEuOS0zNyAyOS4zQzYxNC4zIDIwOC4xIDU2NyAyNDYgNTEyIDI0NnMtMTAyLjMtMzcuOS0xMTQuOC05MC43YTM3LjkzIDM3LjkzIDAgMDAtMzctMjkuM0gxNTRhNDQgNDQgMCAwMC00NCA0NHYyNTJhNDQgNDQgMCAwMDQ0IDQ0aDc1djM4OGE0NCA0NCAwIDAwNDQgNDRoNDc4YTQ0IDQ0IDAgMDA0NC00NFY0NjZoNzVhNDQgNDQgMCAwMDQ0LTQ0VjE3MGE0NCA0NCAwIDAwLTQ0LTQ0em0tMjggMjY4SDcyM3Y0MzJIMzAxVjM5NEgxODJWMTk4aDE1My4zYzI4LjIgNzEuMiA5Ny41IDEyMCAxNzYuNyAxMjBzMTQ4LjUtNDguOCAxNzYuNy0xMjBIODQydjE5NnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SkinTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SkinTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}