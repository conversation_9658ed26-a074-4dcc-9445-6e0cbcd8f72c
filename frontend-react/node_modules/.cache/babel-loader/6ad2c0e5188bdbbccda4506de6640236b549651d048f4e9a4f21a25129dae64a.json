{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Row, Col, Card, Form, Input, Button, Select, Slider, Switch, DatePicker, Table, message, Typography, Space, Tag, Statistic, List, Popconfirm, Alert } from 'antd';\nimport { SettingOutlined, PlayCircleOutlined, DeleteOutlined, ReloadOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport { featureAPI, stockAPI } from '../services/api';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;\nconst FeatureEngineering = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [indexForm] = Form.useForm();\n  const [stockList, setStockList] = useState([]);\n  const [features, setFeatures] = useState(null);\n  const [indexes, setIndexes] = useState([]);\n  const [extractLoading, setExtractLoading] = useState(false);\n  const [indexLoading, setIndexLoading] = useState(false);\n  const [indexesLoading, setIndexesLoading] = useState(false);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    try {\n      const response = await stockAPI.getStockList();\n      if (response.data.success) {\n        setStockList(response.data.data);\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n    }\n  }, []);\n\n  // 加载索引列表\n  const loadIndexes = useCallback(async () => {\n    setIndexesLoading(true);\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      } else {\n        message.error('加载索引列表失败');\n      }\n    } catch (error) {\n      console.error('加载索引列表失败:', error);\n      message.error('网络请求失败');\n    } finally {\n      setIndexesLoading(false);\n    }\n  }, []);\n\n  // 提取特征\n  const handleExtractFeatures = async values => {\n    setExtractLoading(true);\n    try {\n      const data = {\n        stock_code: values.stockCode,\n        window_size: values.windowSize,\n        start_date: values.dateRange[0].format('YYYY-MM-DD'),\n        end_date: values.dateRange[1].format('YYYY-MM-DD'),\n        normalize: values.normalize\n      };\n      const response = await featureAPI.extractFeatures(data);\n      if (response.data.success) {\n        setFeatures(response.data);\n        message.success(`✅ 提取了 ${response.data.feature_count} 个特征向量`);\n      } else {\n        message.error('特征提取失败');\n      }\n    } catch (error) {\n      console.error('特征提取失败:', error);\n      message.error('特征提取失败');\n    } finally {\n      setExtractLoading(false);\n    }\n  };\n\n  // 构建索引\n  const handleBuildIndex = async values => {\n    setIndexLoading(true);\n    try {\n      const stockCodes = values.stockCodes.split('\\n').map(code => code.trim()).filter(code => code);\n      const response = await featureAPI.buildIndex(stockCodes, values.windowSize, values.indexName);\n      if (response.data.success) {\n        message.success(`✅ 索引构建成功！包含 ${response.data.total_vectors} 个向量`);\n        loadIndexes(); // 刷新索引列表\n        indexForm.resetFields();\n      } else {\n        message.error('索引构建失败');\n      }\n    } catch (error) {\n      console.error('索引构建失败:', error);\n      message.error('索引构建失败');\n    } finally {\n      setIndexLoading(false);\n    }\n  };\n\n  // 删除索引\n  const handleDeleteIndex = async indexName => {\n    try {\n      const response = await featureAPI.deleteIndex(indexName);\n      if (response.data.success) {\n        message.success('✅ 索引删除成功');\n        loadIndexes(); // 刷新索引列表\n      } else {\n        message.error('索引删除失败');\n      }\n    } catch (error) {\n      console.error('索引删除失败:', error);\n      message.error('索引删除失败');\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadIndexes();\n  }, [loadStockList, loadIndexes]);\n\n  // 特征表格列定义\n  const featureColumns = [{\n    title: '序号',\n    dataIndex: 'index',\n    key: 'index',\n    width: 80\n  }, {\n    title: '开始日期',\n    dataIndex: 'start_date',\n    key: 'start_date',\n    render: date => dayjs(date).format('YYYY-MM-DD')\n  }, {\n    title: '特征哈希',\n    dataIndex: 'feature_hash',\n    key: 'feature_hash',\n    render: hash => /*#__PURE__*/_jsxDEV(Text, {\n      code: true,\n      style: {\n        fontSize: '12px'\n      },\n      children: [hash === null || hash === void 0 ? void 0 : hash.slice(0, 16), \"...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), \"K\\u7EBF\\u7279\\u5F81\\u63D0\\u53D6\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: handleExtractFeatures,\n            initialValues: {\n              windowSize: 5,\n              normalize: true,\n              dateRange: [dayjs().subtract(6, 'month'), dayjs()]\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stockCode\",\n              label: \"\\u9009\\u62E9\\u80A1\\u7968\",\n              rules: [{\n                required: true,\n                message: '请选择股票'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9\\u80A1\\u7968\",\n                showSearch: true,\n                filterOption: (input, option) => option.children.toLowerCase().includes(input.toLowerCase()),\n                children: stockList.map(stock => /*#__PURE__*/_jsxDEV(Option, {\n                  value: stock.code,\n                  children: [stock.code, \" - \", stock.name]\n                }, stock.code, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"windowSize\",\n              label: \"\\u7A97\\u53E3\\u5927\\u5C0F\",\n              tooltip: \"K\\u7EBF\\u7247\\u6BB5\\u7684\\u957F\\u5EA6\\uFF08\\u5929\\u6570\\uFF09\",\n              children: /*#__PURE__*/_jsxDEV(Slider, {\n                min: 3,\n                max: 20,\n                marks: {\n                  3: '3天',\n                  5: '5天',\n                  10: '10天',\n                  20: '20天'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u65E5\\u671F\\u8303\\u56F4\",\n              rules: [{\n                required: true,\n                message: '请选择日期范围'\n              }],\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"normalize\",\n              label: \"\\u7279\\u5F81\\u6807\\u51C6\\u5316\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: extractLoading,\n                icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 25\n                }, this),\n                block: true,\n                children: \"\\u63D0\\u53D6\\u7279\\u5F81\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), features && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u7279\\u5F81\\u63D0\\u53D6\\u6210\\u529F\",\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u80A1\\u7968\\u4EE3\\u7801:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 26\n                  }, this), \" \", features.stock_code]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7A97\\u53E3\\u5927\\u5C0F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 26\n                  }, this), \" \", features.window_size, \" \\u5929\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7279\\u5F81\\u6570\\u91CF:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 26\n                  }, this), \" \", features.feature_count, \" \\u4E2A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u7279\\u5F81\\u7EF4\\u5EA6:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 26\n                  }, this), \" \", features.feature_dimension, \" \\u7EF4\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this),\n              type: \"success\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), features.features && features.features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 5,\n                children: \"\\u7279\\u5F81\\u6570\\u636E\\u9884\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Table, {\n                dataSource: features.features.slice(0, 10),\n                columns: featureColumns,\n                pagination: false,\n                size: \"small\",\n                rowKey: \"index\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), \"FAISS\\u7D22\\u5F15\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 23\n            }, this),\n            onClick: loadIndexes,\n            loading: indexesLoading,\n            size: \"small\",\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: \"\\uD83D\\uDD28 \\u6784\\u5EFA\\u65B0\\u7D22\\u5F15\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: indexForm,\n            layout: \"vertical\",\n            onFinish: handleBuildIndex,\n            initialValues: {\n              indexName: 'my_index',\n              windowSize: 5,\n              stockCodes: stockList.slice(0, 3).map(s => s.code).join('\\n')\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"indexName\",\n              label: \"\\u7D22\\u5F15\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入索引名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8F93\\u5165\\u7D22\\u5F15\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"windowSize\",\n              label: \"\\u7A97\\u53E3\\u5927\\u5C0F\",\n              children: /*#__PURE__*/_jsxDEV(Slider, {\n                min: 3,\n                max: 20,\n                marks: {\n                  3: '3',\n                  5: '5',\n                  10: '10',\n                  20: '20'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stockCodes\",\n              label: \"\\u80A1\\u7968\\u4EE3\\u7801\\u5217\\u8868\",\n              rules: [{\n                required: true,\n                message: '请输入股票代码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u80A1\\u7968\\u4EE3\\u7801\\uFF0C\\u4F8B\\u5982\\uFF1A\\nsh600519\\nsz000001\\nsh600036\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: indexLoading,\n                icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 25\n                }, this),\n                block: true,\n                children: \"\\u6784\\u5EFA\\u7D22\\u5F15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '24px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: \"\\uD83D\\uDCCB \\u73B0\\u6709\\u7D22\\u5F15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              loading: indexesLoading,\n              dataSource: indexes,\n              renderItem: index => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7D22\\u5F15\\u5417\\uFF1F\",\n                  onConfirm: () => handleDeleteIndex(index.index_name),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 33\n                    }, this),\n                    size: \"small\",\n                    children: \"\\u5220\\u9664\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: index.index_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"blue\",\n                      children: [index.window_size, \"\\u5929\\u7A97\\u53E3\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(Row, {\n                      gutter: 16,\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Statistic, {\n                          title: \"\\u5411\\u91CF\\u6570\\u91CF\",\n                          value: index.total_vectors,\n                          valueStyle: {\n                            fontSize: '14px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Statistic, {\n                          title: \"\\u7279\\u5F81\\u7EF4\\u5EA6\",\n                          value: index.feature_dim,\n                          valueStyle: {\n                            fontSize: '14px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 430,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        span: 8,\n                        children: /*#__PURE__*/_jsxDEV(Text, {\n                          type: \"secondary\",\n                          style: {\n                            fontSize: '12px'\n                          },\n                          children: dayjs(index.build_time).format('MM-DD HH:mm')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this),\n              locale: {\n                emptyText: '暂无索引，请先构建索引'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(FeatureEngineering, \"FPY/cn2Eh7AEING7INBRmA342Fo=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = FeatureEngineering;\nexport default FeatureEngineering;\nvar _c;\n$RefreshReg$(_c, \"FeatureEngineering\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Row", "Col", "Card", "Form", "Input", "<PERSON><PERSON>", "Select", "Slide<PERSON>", "Switch", "DatePicker", "Table", "message", "Typography", "Space", "Tag", "Statistic", "List", "Popconfirm", "<PERSON><PERSON>", "SettingOutlined", "PlayCircleOutlined", "DeleteOutlined", "ReloadOutlined", "DatabaseOutlined", "featureAPI", "stockAPI", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "RangePicker", "TextArea", "FeatureEngineering", "_s", "form", "useForm", "indexForm", "stockList", "setStockList", "features", "setFeatures", "indexes", "setIndexes", "extractLoading", "setExtractLoading", "indexLoading", "setIndexLoading", "indexesLoading", "setIndexesLoading", "loadStockList", "response", "getStockList", "data", "success", "error", "console", "loadIndexes", "getIndexes", "handleExtractFeatures", "values", "stock_code", "stockCode", "window_size", "windowSize", "start_date", "date<PERSON><PERSON><PERSON>", "format", "end_date", "normalize", "extractFeatures", "feature_count", "handleBuildIndex", "stockCodes", "split", "map", "code", "trim", "filter", "buildIndex", "indexName", "total_vectors", "resetFields", "handleDeleteIndex", "deleteIndex", "featureColumns", "title", "dataIndex", "key", "width", "render", "date", "hash", "style", "fontSize", "children", "slice", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "lg", "layout", "onFinish", "initialValues", "subtract", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "showSearch", "filterOption", "input", "option", "toLowerCase", "includes", "stock", "value", "tooltip", "min", "max", "marks", "valuePropName", "type", "htmlType", "loading", "icon", "block", "marginTop", "description", "feature_dimension", "showIcon", "length", "level", "dataSource", "columns", "pagination", "size", "<PERSON><PERSON><PERSON>", "extra", "onClick", "s", "join", "rows", "renderItem", "index", "actions", "onConfirm", "index_name", "okText", "cancelText", "danger", "Meta", "strong", "color", "span", "valueStyle", "feature_dim", "build_time", "locale", "emptyText", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/pages/FeatureEngineering.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Form,\n  Input,\n  Button,\n  Select,\n  Slider,\n  Switch,\n  DatePicker,\n  Table,\n  message,\n  Typography,\n  Space,\n  Tag,\n  Statistic,\n  List,\n  Popconfirm,\n  Alert,\n} from 'antd';\nimport {\n  SettingOutlined,\n  PlayCircleOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  DatabaseOutlined,\n} from '@ant-design/icons';\nimport { featureAPI, stockAPI } from '../services/api';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst FeatureEngineering = () => {\n  const [form] = Form.useForm();\n  const [indexForm] = Form.useForm();\n  const [stockList, setStockList] = useState([]);\n  const [features, setFeatures] = useState(null);\n  const [indexes, setIndexes] = useState([]);\n  const [extractLoading, setExtractLoading] = useState(false);\n  const [indexLoading, setIndexLoading] = useState(false);\n  const [indexesLoading, setIndexesLoading] = useState(false);\n\n  // 加载股票列表\n  const loadStockList = useCallback(async () => {\n    try {\n      const response = await stockAPI.getStockList();\n      if (response.data.success) {\n        setStockList(response.data.data);\n      }\n    } catch (error) {\n      console.error('加载股票列表失败:', error);\n    }\n  }, []);\n\n  // 加载索引列表\n  const loadIndexes = useCallback(async () => {\n    setIndexesLoading(true);\n    try {\n      const response = await featureAPI.getIndexes();\n      if (response.data.success) {\n        setIndexes(response.data.indexes);\n      } else {\n        message.error('加载索引列表失败');\n      }\n    } catch (error) {\n      console.error('加载索引列表失败:', error);\n      message.error('网络请求失败');\n    } finally {\n      setIndexesLoading(false);\n    }\n  }, []);\n\n  // 提取特征\n  const handleExtractFeatures = async (values) => {\n    setExtractLoading(true);\n    try {\n      const data = {\n        stock_code: values.stockCode,\n        window_size: values.windowSize,\n        start_date: values.dateRange[0].format('YYYY-MM-DD'),\n        end_date: values.dateRange[1].format('YYYY-MM-DD'),\n        normalize: values.normalize,\n      };\n\n      const response = await featureAPI.extractFeatures(data);\n      if (response.data.success) {\n        setFeatures(response.data);\n        message.success(`✅ 提取了 ${response.data.feature_count} 个特征向量`);\n      } else {\n        message.error('特征提取失败');\n      }\n    } catch (error) {\n      console.error('特征提取失败:', error);\n      message.error('特征提取失败');\n    } finally {\n      setExtractLoading(false);\n    }\n  };\n\n  // 构建索引\n  const handleBuildIndex = async (values) => {\n    setIndexLoading(true);\n    try {\n      const stockCodes = values.stockCodes\n        .split('\\n')\n        .map(code => code.trim())\n        .filter(code => code);\n\n      const response = await featureAPI.buildIndex(\n        stockCodes,\n        values.windowSize,\n        values.indexName\n      );\n\n      if (response.data.success) {\n        message.success(`✅ 索引构建成功！包含 ${response.data.total_vectors} 个向量`);\n        loadIndexes(); // 刷新索引列表\n        indexForm.resetFields();\n      } else {\n        message.error('索引构建失败');\n      }\n    } catch (error) {\n      console.error('索引构建失败:', error);\n      message.error('索引构建失败');\n    } finally {\n      setIndexLoading(false);\n    }\n  };\n\n  // 删除索引\n  const handleDeleteIndex = async (indexName) => {\n    try {\n      const response = await featureAPI.deleteIndex(indexName);\n      if (response.data.success) {\n        message.success('✅ 索引删除成功');\n        loadIndexes(); // 刷新索引列表\n      } else {\n        message.error('索引删除失败');\n      }\n    } catch (error) {\n      console.error('索引删除失败:', error);\n      message.error('索引删除失败');\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    loadStockList();\n    loadIndexes();\n  }, [loadStockList, loadIndexes]);\n\n  // 特征表格列定义\n  const featureColumns = [\n    {\n      title: '序号',\n      dataIndex: 'index',\n      key: 'index',\n      width: 80,\n    },\n    {\n      title: '开始日期',\n      dataIndex: 'start_date',\n      key: 'start_date',\n      render: (date) => dayjs(date).format('YYYY-MM-DD'),\n    },\n    {\n      title: '特征哈希',\n      dataIndex: 'feature_hash',\n      key: 'feature_hash',\n      render: (hash) => (\n        <Text code style={{ fontSize: '12px' }}>\n          {hash?.slice(0, 16)}...\n        </Text>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={[16, 16]}>\n        {/* 特征提取 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <SettingOutlined />\n                K线特征提取\n              </Space>\n            }\n          >\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleExtractFeatures}\n              initialValues={{\n                windowSize: 5,\n                normalize: true,\n                dateRange: [dayjs().subtract(6, 'month'), dayjs()],\n              }}\n            >\n              <Form.Item\n                name=\"stockCode\"\n                label=\"选择股票\"\n                rules={[{ required: true, message: '请选择股票' }]}\n              >\n                <Select\n                  placeholder=\"选择股票\"\n                  showSearch\n                  filterOption={(input, option) =>\n                    option.children.toLowerCase().includes(input.toLowerCase())\n                  }\n                >\n                  {stockList.map(stock => (\n                    <Option key={stock.code} value={stock.code}>\n                      {stock.code} - {stock.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Form.Item\n                name=\"windowSize\"\n                label=\"窗口大小\"\n                tooltip=\"K线片段的长度（天数）\"\n              >\n                <Slider\n                  min={3}\n                  max={20}\n                  marks={{\n                    3: '3天',\n                    5: '5天',\n                    10: '10天',\n                    20: '20天',\n                  }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"dateRange\"\n                label=\"日期范围\"\n                rules={[{ required: true, message: '请选择日期范围' }]}\n              >\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n\n              <Form.Item\n                name=\"normalize\"\n                label=\"特征标准化\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={extractLoading}\n                  icon={<PlayCircleOutlined />}\n                  block\n                >\n                  提取特征\n                </Button>\n              </Form.Item>\n            </Form>\n\n            {/* 特征提取结果 */}\n            {features && (\n              <div style={{ marginTop: '16px' }}>\n                <Alert\n                  message=\"特征提取成功\"\n                  description={\n                    <div>\n                      <p><strong>股票代码:</strong> {features.stock_code}</p>\n                      <p><strong>窗口大小:</strong> {features.window_size} 天</p>\n                      <p><strong>特征数量:</strong> {features.feature_count} 个</p>\n                      <p><strong>特征维度:</strong> {features.feature_dimension} 维</p>\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n\n                {features.features && features.features.length > 0 && (\n                  <div style={{ marginTop: '16px' }}>\n                    <Title level={5}>特征数据预览</Title>\n                    <Table\n                      dataSource={features.features.slice(0, 10)}\n                      columns={featureColumns}\n                      pagination={false}\n                      size=\"small\"\n                      rowKey=\"index\"\n                    />\n                  </div>\n                )}\n              </div>\n            )}\n          </Card>\n        </Col>\n\n        {/* 索引管理 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title={\n              <Space>\n                <DatabaseOutlined />\n                FAISS索引管理\n              </Space>\n            }\n            extra={\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={loadIndexes}\n                loading={indexesLoading}\n                size=\"small\"\n              >\n                刷新\n              </Button>\n            }\n          >\n            {/* 构建新索引 */}\n            <Title level={5}>🔨 构建新索引</Title>\n            <Form\n              form={indexForm}\n              layout=\"vertical\"\n              onFinish={handleBuildIndex}\n              initialValues={{\n                indexName: 'my_index',\n                windowSize: 5,\n                stockCodes: stockList.slice(0, 3).map(s => s.code).join('\\n'),\n              }}\n            >\n              <Form.Item\n                name=\"indexName\"\n                label=\"索引名称\"\n                rules={[{ required: true, message: '请输入索引名称' }]}\n              >\n                <Input placeholder=\"输入索引名称\" />\n              </Form.Item>\n\n              <Form.Item\n                name=\"windowSize\"\n                label=\"窗口大小\"\n              >\n                <Slider\n                  min={3}\n                  max={20}\n                  marks={{\n                    3: '3',\n                    5: '5',\n                    10: '10',\n                    20: '20',\n                  }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"stockCodes\"\n                label=\"股票代码列表\"\n                rules={[{ required: true, message: '请输入股票代码' }]}\n              >\n                <TextArea\n                  rows={4}\n                  placeholder=\"每行一个股票代码，例如：&#10;sh600519&#10;sz000001&#10;sh600036\"\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={indexLoading}\n                  icon={<PlayCircleOutlined />}\n                  block\n                >\n                  构建索引\n                </Button>\n              </Form.Item>\n            </Form>\n\n            {/* 现有索引列表 */}\n            <div style={{ marginTop: '24px' }}>\n              <Title level={5}>📋 现有索引</Title>\n              <List\n                loading={indexesLoading}\n                dataSource={indexes}\n                renderItem={(index) => (\n                  <List.Item\n                    actions={[\n                      <Popconfirm\n                        title=\"确定要删除这个索引吗？\"\n                        onConfirm={() => handleDeleteIndex(index.index_name)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          size=\"small\"\n                        >\n                          删除\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      title={\n                        <Space>\n                          <Text strong>{index.index_name}</Text>\n                          <Tag color=\"blue\">{index.window_size}天窗口</Tag>\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Row gutter={16}>\n                            <Col span={8}>\n                              <Statistic\n                                title=\"向量数量\"\n                                value={index.total_vectors}\n                                valueStyle={{ fontSize: '14px' }}\n                              />\n                            </Col>\n                            <Col span={8}>\n                              <Statistic\n                                title=\"特征维度\"\n                                value={index.feature_dim}\n                                valueStyle={{ fontSize: '14px' }}\n                              />\n                            </Col>\n                            <Col span={8}>\n                              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                                {dayjs(index.build_time).format('MM-DD HH:mm')}\n                              </Text>\n                            </Col>\n                          </Row>\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n                locale={{ emptyText: '暂无索引，请先构建索引' }}\n              />\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default FeatureEngineering;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,KAAK,QACA,MAAM;AACb,SACEC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,QACX,mBAAmB;AAC1B,SAASC,UAAU,EAAEC,QAAQ,QAAQ,iBAAiB;AACtD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGlB,UAAU;AAClC,MAAM;EAAEmB;AAAO,CAAC,GAAGzB,MAAM;AACzB,MAAM;EAAE0B;AAAY,CAAC,GAAGvB,UAAU;AAClC,MAAM;EAAEwB;AAAS,CAAC,GAAG7B,KAAK;AAE1B,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,SAAS,CAAC,GAAGnC,IAAI,CAACkC,OAAO,CAAC,CAAC;EAClC,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMsD,aAAa,GAAGpD,WAAW,CAAC,YAAY;IAC5C,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAM3B,QAAQ,CAAC4B,YAAY,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBf,YAAY,CAACY,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,WAAW,GAAG3D,WAAW,CAAC,YAAY;IAC1CmD,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM5B,UAAU,CAACmC,UAAU,CAAC,CAAC;MAC9C,IAAIP,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBX,UAAU,CAACQ,QAAQ,CAACE,IAAI,CAACX,OAAO,CAAC;MACnC,CAAC,MAAM;QACLhC,OAAO,CAAC6C,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRN,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,qBAAqB,GAAG,MAAOC,MAAM,IAAK;IAC9Cf,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI;MACF,MAAMQ,IAAI,GAAG;QACXQ,UAAU,EAAED,MAAM,CAACE,SAAS;QAC5BC,WAAW,EAAEH,MAAM,CAACI,UAAU;QAC9BC,UAAU,EAAEL,MAAM,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;QACpDC,QAAQ,EAAER,MAAM,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;QAClDE,SAAS,EAAET,MAAM,CAACS;MACpB,CAAC;MAED,MAAMlB,QAAQ,GAAG,MAAM5B,UAAU,CAAC+C,eAAe,CAACjB,IAAI,CAAC;MACvD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBb,WAAW,CAACU,QAAQ,CAACE,IAAI,CAAC;QAC1B3C,OAAO,CAAC4C,OAAO,CAAC,SAASH,QAAQ,CAACE,IAAI,CAACkB,aAAa,QAAQ,CAAC;MAC/D,CAAC,MAAM;QACL7D,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRV,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM2B,gBAAgB,GAAG,MAAOZ,MAAM,IAAK;IACzCb,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM0B,UAAU,GAAGb,MAAM,CAACa,UAAU,CACjCC,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CACxBC,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC;MAEvB,MAAMzB,QAAQ,GAAG,MAAM5B,UAAU,CAACwD,UAAU,CAC1CN,UAAU,EACVb,MAAM,CAACI,UAAU,EACjBJ,MAAM,CAACoB,SACT,CAAC;MAED,IAAI7B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB5C,OAAO,CAAC4C,OAAO,CAAC,eAAeH,QAAQ,CAACE,IAAI,CAAC4B,aAAa,MAAM,CAAC;QACjExB,WAAW,CAAC,CAAC,CAAC,CAAC;QACfpB,SAAS,CAAC6C,WAAW,CAAC,CAAC;MACzB,CAAC,MAAM;QACLxE,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRR,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMoC,iBAAiB,GAAG,MAAOH,SAAS,IAAK;IAC7C,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM5B,UAAU,CAAC6D,WAAW,CAACJ,SAAS,CAAC;MACxD,IAAI7B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB5C,OAAO,CAAC4C,OAAO,CAAC,UAAU,CAAC;QAC3BG,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,MAAM;QACL/C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACdqD,aAAa,CAAC,CAAC;IACfO,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACP,aAAa,EAAEO,WAAW,CAAC,CAAC;;EAEhC;EACA,MAAM4B,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGC,IAAI,IAAKlE,KAAK,CAACkE,IAAI,CAAC,CAACxB,MAAM,CAAC,YAAY;EACnD,CAAC,EACD;IACEmB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBE,MAAM,EAAGE,IAAI,iBACXjE,OAAA,CAACE,IAAI;MAAC+C,IAAI;MAACiB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAC,QAAA,GACpCH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACtB;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEV,CAAC,CACF;EAED,oBACEzE,OAAA;IAAAoE,QAAA,eACEpE,OAAA,CAAC5B,GAAG;MAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAN,QAAA,gBAEpBpE,OAAA,CAAC3B,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,eAClBpE,OAAA,CAAC1B,IAAI;UACHqF,KAAK,eACH3D,OAAA,CAACf,KAAK;YAAAmF,QAAA,gBACJpE,OAAA,CAACT,eAAe;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAL,QAAA,gBAEDpE,OAAA,CAACzB,IAAI;YACHiC,IAAI,EAAEA,IAAK;YACXqE,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE9C,qBAAsB;YAChC+C,aAAa,EAAE;cACb1C,UAAU,EAAE,CAAC;cACbK,SAAS,EAAE,IAAI;cACfH,SAAS,EAAE,CAACzC,KAAK,CAAC,CAAC,CAACkF,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAElF,KAAK,CAAC,CAAC;YACnD,CAAE;YAAAsE,QAAA,gBAEFpE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtG,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAqF,QAAA,eAE9CpE,OAAA,CAACtB,MAAM;gBACL4G,WAAW,EAAC,0BAAM;gBAClBC,UAAU;gBACVC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACtB,QAAQ,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAC3D;gBAAAvB,QAAA,EAEAzD,SAAS,CAACqC,GAAG,CAAC6C,KAAK,iBAClB7F,OAAA,CAACG,MAAM;kBAAkB2F,KAAK,EAAED,KAAK,CAAC5C,IAAK;kBAAAmB,QAAA,GACxCyB,KAAK,CAAC5C,IAAI,EAAC,KAAG,EAAC4C,KAAK,CAACX,IAAI;gBAAA,GADfW,KAAK,CAAC5C,IAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,0BAAM;cACZY,OAAO,EAAC,+DAAa;cAAA3B,QAAA,eAErBpE,OAAA,CAACrB,MAAM;gBACLqH,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRC,KAAK,EAAE;kBACL,CAAC,EAAE,IAAI;kBACP,CAAC,EAAE,IAAI;kBACP,EAAE,EAAE,KAAK;kBACT,EAAE,EAAE;gBACN;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDpE,OAAA,CAACI,WAAW;gBAAC8D,KAAK,EAAE;kBAAEJ,KAAK,EAAE;gBAAO;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,gCAAO;cACbgB,aAAa,EAAC,SAAS;cAAA/B,QAAA,eAEvBpE,OAAA,CAACpB,MAAM;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cAAAb,QAAA,eACRpE,OAAA,CAACvB,MAAM;gBACL2H,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBC,OAAO,EAAErF,cAAe;gBACxBsF,IAAI,eAAEvG,OAAA,CAACR,kBAAkB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7B+B,KAAK;gBAAApC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EAGN5D,QAAQ,iBACPb,OAAA;YAAKkE,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAArC,QAAA,gBAChCpE,OAAA,CAACV,KAAK;cACJP,OAAO,EAAC,sCAAQ;cAChB2H,WAAW,eACT1G,OAAA;gBAAAoE,QAAA,gBACEpE,OAAA;kBAAAoE,QAAA,gBAAGpE,OAAA;oBAAAoE,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACqB,UAAU;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDzE,OAAA;kBAAAoE,QAAA,gBAAGpE,OAAA;oBAAAoE,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACuB,WAAW,EAAC,SAAE;gBAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtDzE,OAAA;kBAAAoE,QAAA,gBAAGpE,OAAA;oBAAAoE,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAAC+B,aAAa,EAAC,SAAE;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDzE,OAAA;kBAAAoE,QAAA,gBAAGpE,OAAA;oBAAAoE,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAAC8F,iBAAiB,EAAC,SAAE;gBAAA;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CACN;cACD2B,IAAI,EAAC,SAAS;cACdQ,QAAQ;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EAED5D,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACgG,MAAM,GAAG,CAAC,iBAChD7G,OAAA;cAAKkE,KAAK,EAAE;gBAAEuC,SAAS,EAAE;cAAO,CAAE;cAAArC,QAAA,gBAChCpE,OAAA,CAACC,KAAK;gBAAC6G,KAAK,EAAE,CAAE;gBAAA1C,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/BzE,OAAA,CAAClB,KAAK;gBACJiI,UAAU,EAAElG,QAAQ,CAACA,QAAQ,CAACwD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE;gBAC3C2C,OAAO,EAAEtD,cAAe;gBACxBuD,UAAU,EAAE,KAAM;gBAClBC,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAC;cAAO;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNzE,OAAA,CAAC3B,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,eAClBpE,OAAA,CAAC1B,IAAI;UACHqF,KAAK,eACH3D,OAAA,CAACf,KAAK;YAAAmF,QAAA,gBACJpE,OAAA,CAACL,gBAAgB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iCAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACD2C,KAAK,eACHpH,OAAA,CAACvB,MAAM;YACL8H,IAAI,eAAEvG,OAAA,CAACN,cAAc;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB4C,OAAO,EAAEvF,WAAY;YACrBwE,OAAO,EAAEjF,cAAe;YACxB6F,IAAI,EAAC,OAAO;YAAA9C,QAAA,EACb;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAL,QAAA,gBAGDpE,OAAA,CAACC,KAAK;YAAC6G,KAAK,EAAE,CAAE;YAAA1C,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjCzE,OAAA,CAACzB,IAAI;YACHiC,IAAI,EAAEE,SAAU;YAChBmE,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAEjC,gBAAiB;YAC3BkC,aAAa,EAAE;cACb1B,SAAS,EAAE,UAAU;cACrBhB,UAAU,EAAE,CAAC;cACbS,UAAU,EAAEnC,SAAS,CAAC0D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAACsE,CAAC,IAAIA,CAAC,CAACrE,IAAI,CAAC,CAACsE,IAAI,CAAC,IAAI;YAC9D,CAAE;YAAAnD,QAAA,gBAEFpE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDpE,OAAA,CAACxB,KAAK;gBAAC8G,WAAW,EAAC;cAAQ;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,0BAAM;cAAAf,QAAA,eAEZpE,OAAA,CAACrB,MAAM;gBACLqH,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRC,KAAK,EAAE;kBACL,CAAC,EAAE,GAAG;kBACN,CAAC,EAAE,GAAG;kBACN,EAAE,EAAE,IAAI;kBACR,EAAE,EAAE;gBACN;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDpE,OAAA,CAACK,QAAQ;gBACPmH,IAAI,EAAE,CAAE;gBACRlC,WAAW,EAAC;cAAqD;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZzE,OAAA,CAACzB,IAAI,CAAC0G,IAAI;cAAAb,QAAA,eACRpE,OAAA,CAACvB,MAAM;gBACL2H,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBC,OAAO,EAAEnF,YAAa;gBACtBoF,IAAI,eAAEvG,OAAA,CAACR,kBAAkB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7B+B,KAAK;gBAAApC,QAAA,EACN;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGPzE,OAAA;YAAKkE,KAAK,EAAE;cAAEuC,SAAS,EAAE;YAAO,CAAE;YAAArC,QAAA,gBAChCpE,OAAA,CAACC,KAAK;cAAC6G,KAAK,EAAE,CAAE;cAAA1C,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCzE,OAAA,CAACZ,IAAI;cACHkH,OAAO,EAAEjF,cAAe;cACxB0F,UAAU,EAAEhG,OAAQ;cACpB0G,UAAU,EAAGC,KAAK,iBAChB1H,OAAA,CAACZ,IAAI,CAAC6F,IAAI;gBACR0C,OAAO,EAAE,cACP3H,OAAA,CAACX,UAAU;kBACTsE,KAAK,EAAC,oEAAa;kBACnBiE,SAAS,EAAEA,CAAA,KAAMpE,iBAAiB,CAACkE,KAAK,CAACG,UAAU,CAAE;kBACrDC,MAAM,EAAC,cAAI;kBACXC,UAAU,EAAC,cAAI;kBAAA3D,QAAA,eAEfpE,OAAA,CAACvB,MAAM;oBACL2H,IAAI,EAAC,MAAM;oBACX4B,MAAM;oBACNzB,IAAI,eAAEvG,OAAA,CAACP,cAAc;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzByC,IAAI,EAAC,OAAO;oBAAA9C,QAAA,EACb;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAL,QAAA,eAEFpE,OAAA,CAACZ,IAAI,CAAC6F,IAAI,CAACgD,IAAI;kBACbtE,KAAK,eACH3D,OAAA,CAACf,KAAK;oBAAAmF,QAAA,gBACJpE,OAAA,CAACE,IAAI;sBAACgI,MAAM;sBAAA9D,QAAA,EAAEsD,KAAK,CAACG;oBAAU;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtCzE,OAAA,CAACd,GAAG;sBAACiJ,KAAK,EAAC,MAAM;sBAAA/D,QAAA,GAAEsD,KAAK,CAACtF,WAAW,EAAC,oBAAG;oBAAA;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CACR;kBACDiC,WAAW,eACT1G,OAAA;oBAAAoE,QAAA,eACEpE,OAAA,CAAC5B,GAAG;sBAACsG,MAAM,EAAE,EAAG;sBAAAN,QAAA,gBACdpE,OAAA,CAAC3B,GAAG;wBAAC+J,IAAI,EAAE,CAAE;wBAAAhE,QAAA,eACXpE,OAAA,CAACb,SAAS;0BACRwE,KAAK,EAAC,0BAAM;0BACZmC,KAAK,EAAE4B,KAAK,CAACpE,aAAc;0BAC3B+E,UAAU,EAAE;4BAAElE,QAAQ,EAAE;0BAAO;wBAAE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACNzE,OAAA,CAAC3B,GAAG;wBAAC+J,IAAI,EAAE,CAAE;wBAAAhE,QAAA,eACXpE,OAAA,CAACb,SAAS;0BACRwE,KAAK,EAAC,0BAAM;0BACZmC,KAAK,EAAE4B,KAAK,CAACY,WAAY;0BACzBD,UAAU,EAAE;4BAAElE,QAAQ,EAAE;0BAAO;wBAAE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACNzE,OAAA,CAAC3B,GAAG;wBAAC+J,IAAI,EAAE,CAAE;wBAAAhE,QAAA,eACXpE,OAAA,CAACE,IAAI;0BAACkG,IAAI,EAAC,WAAW;0BAAClC,KAAK,EAAE;4BAAEC,QAAQ,EAAE;0BAAO,CAAE;0BAAAC,QAAA,EAChDtE,KAAK,CAAC4H,KAAK,CAACa,UAAU,CAAC,CAAC/F,MAAM,CAAC,aAAa;wBAAC;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CACX;cACF+D,MAAM,EAAE;gBAAEC,SAAS,EAAE;cAAc;YAAE;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CAjaID,kBAAkB;EAAA,QACP/B,IAAI,CAACkC,OAAO,EACPlC,IAAI,CAACkC,OAAO;AAAA;AAAAiI,EAAA,GAF5BpI,kBAAkB;AAmaxB,eAAeA,kBAAkB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}