{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from \"./Input\";\nimport { getTitle } from \"../utils/commonUtil\";\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd,\n    onInputBlur = props.onInputBlur,\n    title = props.title;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]);\n\n  // Not show text when closed expect combobox mode\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n\n  // Get title of selection item\n  var selectionTitle = title === undefined ? getTitle(item) : title;\n  var placeholderNode = React.useMemo(function () {\n    if (item) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hasTextInput ? {\n        visibility: 'hidden'\n      } : undefined\n    }, placeholder);\n  }, [item, hasTextInput, placeholder, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    onBlur: onInputBlur,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: selectionTitle\n    // 当 Select 已经选中选项时，还需 selection 隐藏但留在原地占位\n    // https://github.com/ant-design/ant-design/issues/27688\n    // https://github.com/ant-design/ant-design/issues/41530\n    ,\n\n    style: hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined\n  }, item.label) : null, placeholderNode);\n};\nexport default SingleSelector;", "map": {"version": 3, "names": ["_slicedToArray", "React", "pickAttrs", "Input", "getTitle", "SingleSelector", "props", "inputElement", "prefixCls", "id", "inputRef", "disabled", "autoFocus", "autoComplete", "activeDescendantId", "mode", "open", "values", "placeholder", "tabIndex", "showSearch", "searchValue", "activeValue", "max<PERSON><PERSON><PERSON>", "onInputKeyDown", "onInputMouseDown", "onInputChange", "onInputPaste", "onInputCompositionStart", "onInputCompositionEnd", "onInputBlur", "title", "_React$useState", "useState", "_React$useState2", "inputChanged", "setInputChanged", "combobox", "inputEditable", "item", "inputValue", "useEffect", "hasTextInput", "selectionTitle", "undefined", "placeholderNode", "useMemo", "createElement", "className", "concat", "style", "visibility", "ref", "editable", "value", "onKeyDown", "onMouseDown", "onChange", "e", "onPaste", "onCompositionStart", "onCompositionEnd", "onBlur", "attrs", "label"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/rc-select/es/Selector/SingleSelector.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from \"./Input\";\nimport { getTitle } from \"../utils/commonUtil\";\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd,\n    onInputBlur = props.onInputBlur,\n    title = props.title;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]);\n\n  // Not show text when closed expect combobox mode\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n\n  // Get title of selection item\n  var selectionTitle = title === undefined ? getTitle(item) : title;\n  var placeholderNode = React.useMemo(function () {\n    if (item) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hasTextInput ? {\n        visibility: 'hidden'\n      } : undefined\n    }, placeholder);\n  }, [item, hasTextInput, placeholder, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    onBlur: onInputBlur,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: selectionTitle\n    // 当 Select 已经选中选项时，还需 selection 隐藏但留在原地占位\n    // https://github.com/ant-design/ant-design/issues/27688\n    // https://github.com/ant-design/ant-design/issues/41530\n    ,\n    style: hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined\n  }, item.label) : null, placeholderNode);\n};\nexport default SingleSelector;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;IACnCC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,WAAW,GAAGZ,KAAK,CAACY,WAAW;IAC/BC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,cAAc,GAAGlB,KAAK,CAACkB,cAAc;IACrCC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,aAAa,GAAGpB,KAAK,CAACoB,aAAa;IACnCC,YAAY,GAAGrB,KAAK,CAACqB,YAAY;IACjCC,uBAAuB,GAAGtB,KAAK,CAACsB,uBAAuB;IACvDC,qBAAqB,GAAGvB,KAAK,CAACuB,qBAAqB;IACnDC,WAAW,GAAGxB,KAAK,CAACwB,WAAW;IAC/BC,KAAK,GAAGzB,KAAK,CAACyB,KAAK;EACrB,IAAIC,eAAe,GAAG/B,KAAK,CAACgC,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGlC,cAAc,CAACgC,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,QAAQ,GAAGtB,IAAI,KAAK,UAAU;EAClC,IAAIuB,aAAa,GAAGD,QAAQ,IAAIjB,UAAU;EAC1C,IAAImB,IAAI,GAAGtB,MAAM,CAAC,CAAC,CAAC;EACpB,IAAIuB,UAAU,GAAGnB,WAAW,IAAI,EAAE;EAClC,IAAIgB,QAAQ,IAAIf,WAAW,IAAI,CAACa,YAAY,EAAE;IAC5CK,UAAU,GAAGlB,WAAW;EAC1B;EACArB,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,IAAIJ,QAAQ,EAAE;MACZD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEf,WAAW,CAAC,CAAC;;EAE3B;EACA,IAAIoB,YAAY,GAAG3B,IAAI,KAAK,UAAU,IAAI,CAACC,IAAI,IAAI,CAACI,UAAU,GAAG,KAAK,GAAG,CAAC,CAACoB,UAAU;;EAErF;EACA,IAAIG,cAAc,GAAGZ,KAAK,KAAKa,SAAS,GAAGxC,QAAQ,CAACmC,IAAI,CAAC,GAAGR,KAAK;EACjE,IAAIc,eAAe,GAAG5C,KAAK,CAAC6C,OAAO,CAAC,YAAY;IAC9C,IAAIP,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,OAAO,aAAatC,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACzC,SAAS,EAAE,wBAAwB,CAAC;MACzD0C,KAAK,EAAER,YAAY,GAAG;QACpBS,UAAU,EAAE;MACd,CAAC,GAAGP;IACN,CAAC,EAAE1B,WAAW,CAAC;EACjB,CAAC,EAAE,CAACqB,IAAI,EAAEG,YAAY,EAAExB,WAAW,EAAEV,SAAS,CAAC,CAAC;EAChD,OAAO,aAAaP,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACzC,SAAS,EAAE,iBAAiB;EACnD,CAAC,EAAE,aAAaP,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;IAC1CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACzC,SAAS,EAAE,mBAAmB;EACrD,CAAC,EAAE,aAAaP,KAAK,CAAC8C,aAAa,CAAC5C,KAAK,EAAE;IACzCiD,GAAG,EAAE1C,QAAQ;IACbF,SAAS,EAAEA,SAAS;IACpBC,EAAE,EAAEA,EAAE;IACNO,IAAI,EAAEA,IAAI;IACVT,YAAY,EAAEA,YAAY;IAC1BI,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BwC,QAAQ,EAAEf,aAAa;IACvBxB,kBAAkB,EAAEA,kBAAkB;IACtCwC,KAAK,EAAEd,UAAU;IACjBe,SAAS,EAAE/B,cAAc;IACzBgC,WAAW,EAAE/B,gBAAgB;IAC7BgC,QAAQ,EAAE,SAASA,QAAQA,CAACC,CAAC,EAAE;MAC7BtB,eAAe,CAAC,IAAI,CAAC;MACrBV,aAAa,CAACgC,CAAC,CAAC;IAClB,CAAC;IACDC,OAAO,EAAEhC,YAAY;IACrBiC,kBAAkB,EAAEhC,uBAAuB;IAC3CiC,gBAAgB,EAAEhC,qBAAqB;IACvCiC,MAAM,EAAEhC,WAAW;IACnBX,QAAQ,EAAEA,QAAQ;IAClB4C,KAAK,EAAE7D,SAAS,CAACI,KAAK,EAAE,IAAI,CAAC;IAC7BiB,SAAS,EAAEc,QAAQ,GAAGd,SAAS,GAAGqB;EACpC,CAAC,CAAC,CAAC,EAAE,CAACP,QAAQ,IAAIE,IAAI,GAAG,aAAatC,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE;IAChEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACzC,SAAS,EAAE,iBAAiB,CAAC;IAClDuB,KAAK,EAAEY;IACP;IACA;IACA;IAAA;;IAEAO,KAAK,EAAER,YAAY,GAAG;MACpBS,UAAU,EAAE;IACd,CAAC,GAAGP;EACN,CAAC,EAAEL,IAAI,CAACyB,KAAK,CAAC,GAAG,IAAI,EAAEnB,eAAe,CAAC;AACzC,CAAC;AACD,eAAexC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}