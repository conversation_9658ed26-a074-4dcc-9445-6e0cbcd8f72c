{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LightweightChart = ({\n  data,\n  title = 'K线图',\n  height = 500,\n  loading = false,\n  error = null\n}) => {\n  _s();\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [chartReady, setChartReady] = useState(false);\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 创建图表\n    chart.current = createChart(chartContainerRef.current, {\n      layout: {\n        background: {\n          type: ColorType.Solid,\n          color: '#ffffff'\n        },\n        textColor: '#333'\n      },\n      grid: {\n        vertLines: {\n          color: '#f0f0f0'\n        },\n        horzLines: {\n          color: '#f0f0f0'\n        }\n      },\n      crosshair: {\n        mode: 1 // Normal crosshair mode\n      },\n      rightPriceScale: {\n        borderColor: '#cccccc'\n      },\n      timeScale: {\n        borderColor: '#cccccc',\n        timeVisible: true,\n        secondsVisible: false\n      },\n      width: chartContainerRef.current.clientWidth,\n      height: height - 100 // 减去标题和工具栏高度\n    });\n\n    // 创建K线系列\n    candlestickSeries.current = chart.current.addCandlestickSeries({\n      upColor: '#ef4444',\n      // 红色上涨\n      downColor: '#22c55e',\n      // 绿色下跌\n      borderDownColor: '#22c55e',\n      borderUpColor: '#ef4444',\n      wickDownColor: '#22c55e',\n      wickUpColor: '#ef4444'\n    });\n\n    // 创建成交量系列\n    volumeSeries.current = chart.current.addHistogramSeries({\n      color: '#26a69a',\n      priceFormat: {\n        type: 'volume'\n      },\n      priceScaleId: 'volume',\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0\n      }\n    });\n\n    // 设置成交量价格刻度\n    chart.current.priceScale('volume').applyOptions({\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0\n      }\n    });\n    setChartReady(true);\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      if (chart.current && chartContainerRef.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n\n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘)\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? 'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)'\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u56FE\\u8868\\u52A0\\u8F7D\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: title,\n    style: {\n      width: '100%'\n    },\n    bodyStyle: {\n      padding: '12px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u52A0\\u8F7D\\u56FE\\u8868\\u6570\\u636E...\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chartContainerRef,\n        style: {\n          width: '100%',\n          height: height - 100,\n          position: 'relative'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), !loading && (!data || data.length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height - 100,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '16px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(LightweightChart, \"CDcVyzhgpJ+sUTNz6devG9+znsU=\");\n_c = LightweightChart;\nexport default LightweightChart;\nvar _c;\n$RefreshReg$(_c, \"LightweightChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "createChart", "Card", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LightweightChart", "data", "title", "height", "loading", "error", "_s", "chartContainerRef", "chart", "candlestickSeries", "volumeSeries", "chartReady", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "layout", "background", "type", "ColorType", "Solid", "color", "textColor", "grid", "vertLines", "horzLines", "crosshair", "mode", "rightPriceScale", "borderColor", "timeScale", "timeVisible", "secondsVisible", "width", "clientWidth", "addCandlestickSeries", "upColor", "downColor", "borderDownColor", "borderUpColor", "wickDownColor", "wickUpColor", "addHistogramSeries", "priceFormat", "priceScaleId", "scale<PERSON>argins", "top", "bottom", "priceScale", "applyOptions", "handleResize", "window", "addEventListener", "removeEventListener", "remove", "Array", "isArray", "length", "candlestickData", "volumeData", "for<PERSON>ach", "item", "time", "Date", "date", "日期", "getTime", "push", "open", "parseFloat", "开盘", "high", "最高", "low", "最低", "close", "收盘", "value", "volume", "成交量", "sort", "a", "b", "setData", "<PERSON><PERSON><PERSON><PERSON>", "console", "children", "message", "description", "showIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "bodyStyle", "padding", "spinning", "tip", "ref", "position", "display", "alignItems", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/src/components/LightweightChart.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { createChart } from 'lightweight-charts';\nimport { Card, Spin, Alert } from 'antd';\n\nconst LightweightChart = ({ \n  data, \n  title = 'K线图', \n  height = 500,\n  loading = false,\n  error = null \n}) => {\n  const chartContainerRef = useRef();\n  const chart = useRef();\n  const candlestickSeries = useRef();\n  const volumeSeries = useRef();\n  const [chartReady, setChartReady] = useState(false);\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartContainerRef.current) return;\n\n    // 创建图表\n    chart.current = createChart(chartContainerRef.current, {\n      layout: {\n        background: { type: ColorType.Solid, color: '#ffffff' },\n        textColor: '#333',\n      },\n      grid: {\n        vertLines: { color: '#f0f0f0' },\n        horzLines: { color: '#f0f0f0' },\n      },\n      crosshair: {\n        mode: 1, // Normal crosshair mode\n      },\n      rightPriceScale: {\n        borderColor: '#cccccc',\n      },\n      timeScale: {\n        borderColor: '#cccccc',\n        timeVisible: true,\n        secondsVisible: false,\n      },\n      width: chartContainerRef.current.clientWidth,\n      height: height - 100, // 减去标题和工具栏高度\n    });\n\n    // 创建K线系列\n    candlestickSeries.current = chart.current.addCandlestickSeries({\n      upColor: '#ef4444', // 红色上涨\n      downColor: '#22c55e', // 绿色下跌\n      borderDownColor: '#22c55e',\n      borderUpColor: '#ef4444',\n      wickDownColor: '#22c55e',\n      wickUpColor: '#ef4444',\n    });\n\n    // 创建成交量系列\n    volumeSeries.current = chart.current.addHistogramSeries({\n      color: '#26a69a',\n      priceFormat: {\n        type: 'volume',\n      },\n      priceScaleId: 'volume',\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0,\n      },\n    });\n\n    // 设置成交量价格刻度\n    chart.current.priceScale('volume').applyOptions({\n      scaleMargins: {\n        top: 0.7,\n        bottom: 0,\n      },\n    });\n\n    setChartReady(true);\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      if (chart.current && chartContainerRef.current) {\n        chart.current.applyOptions({\n          width: chartContainerRef.current.clientWidth,\n        });\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (chart.current) {\n        chart.current.remove();\n      }\n    };\n  }, [height]);\n\n  // 更新数据\n  useEffect(() => {\n    if (!chartReady || !data || !Array.isArray(data) || data.length === 0) {\n      return;\n    }\n\n    try {\n      // 转换数据格式\n      const candlestickData = [];\n      const volumeData = [];\n\n      data.forEach(item => {\n        const time = new Date(item.date || item.日期).getTime() / 1000;\n        \n        // K线数据\n        candlestickData.push({\n          time: time,\n          open: parseFloat(item.open || item.开盘),\n          high: parseFloat(item.high || item.最高),\n          low: parseFloat(item.low || item.最低),\n          close: parseFloat(item.close || item.收盘),\n        });\n\n        // 成交量数据\n        volumeData.push({\n          time: time,\n          value: parseFloat(item.volume || item.成交量),\n          color: (item.close || item.收盘) >= (item.open || item.开盘) ? \n            'rgba(239, 68, 68, 0.5)' : 'rgba(34, 197, 94, 0.5)',\n        });\n      });\n\n      // 按时间排序\n      candlestickData.sort((a, b) => a.time - b.time);\n      volumeData.sort((a, b) => a.time - b.time);\n\n      // 设置数据\n      if (candlestickSeries.current) {\n        candlestickSeries.current.setData(candlestickData);\n      }\n      \n      if (volumeSeries.current) {\n        volumeSeries.current.setData(volumeData);\n      }\n\n      // 自适应视图\n      if (chart.current) {\n        chart.current.timeScale().fitContent();\n      }\n\n    } catch (error) {\n      console.error('图表数据处理错误:', error);\n    }\n  }, [data, chartReady]);\n\n  if (error) {\n    return (\n      <Card title={title}>\n        <Alert\n          message=\"图表加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n        />\n      </Card>\n    );\n  }\n\n  return (\n    <Card \n      title={title}\n      style={{ width: '100%' }}\n      bodyStyle={{ padding: '12px' }}\n    >\n      <Spin spinning={loading} tip=\"加载图表数据...\">\n        <div\n          ref={chartContainerRef}\n          style={{\n            width: '100%',\n            height: height - 100,\n            position: 'relative',\n          }}\n        />\n        {!loading && (!data || data.length === 0) && (\n          <div\n            style={{\n              height: height - 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#999',\n              fontSize: '16px',\n            }}\n          >\n            暂无数据\n          </div>\n        )}\n      </Spin>\n    </Card>\n  );\n};\n\nexport default LightweightChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,IAAI;EACJC,KAAK,GAAG,KAAK;EACbC,MAAM,GAAG,GAAG;EACZC,OAAO,GAAG,KAAK;EACfC,KAAK,GAAG;AACV,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,iBAAiB,GAAGf,MAAM,CAAC,CAAC;EAClC,MAAMgB,KAAK,GAAGhB,MAAM,CAAC,CAAC;EACtB,MAAMiB,iBAAiB,GAAGjB,MAAM,CAAC,CAAC;EAClC,MAAMkB,YAAY,GAAGlB,MAAM,CAAC,CAAC;EAC7B,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,iBAAiB,CAACM,OAAO,EAAE;;IAEhC;IACAL,KAAK,CAACK,OAAO,GAAGnB,WAAW,CAACa,iBAAiB,CAACM,OAAO,EAAE;MACrDC,MAAM,EAAE;QACNC,UAAU,EAAE;UAAEC,IAAI,EAAEC,SAAS,CAACC,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC;QACvDC,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJC,SAAS,EAAE;UAAEH,KAAK,EAAE;QAAU,CAAC;QAC/BI,SAAS,EAAE;UAAEJ,KAAK,EAAE;QAAU;MAChC,CAAC;MACDK,SAAS,EAAE;QACTC,IAAI,EAAE,CAAC,CAAE;MACX,CAAC;MACDC,eAAe,EAAE;QACfC,WAAW,EAAE;MACf,CAAC;MACDC,SAAS,EAAE;QACTD,WAAW,EAAE,SAAS;QACtBE,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE;MAClB,CAAC;MACDC,KAAK,EAAExB,iBAAiB,CAACM,OAAO,CAACmB,WAAW;MAC5C7B,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAE;IACxB,CAAC,CAAC;;IAEF;IACAM,iBAAiB,CAACI,OAAO,GAAGL,KAAK,CAACK,OAAO,CAACoB,oBAAoB,CAAC;MAC7DC,OAAO,EAAE,SAAS;MAAE;MACpBC,SAAS,EAAE,SAAS;MAAE;MACtBC,eAAe,EAAE,SAAS;MAC1BC,aAAa,EAAE,SAAS;MACxBC,aAAa,EAAE,SAAS;MACxBC,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA7B,YAAY,CAACG,OAAO,GAAGL,KAAK,CAACK,OAAO,CAAC2B,kBAAkB,CAAC;MACtDrB,KAAK,EAAE,SAAS;MAChBsB,WAAW,EAAE;QACXzB,IAAI,EAAE;MACR,CAAC;MACD0B,YAAY,EAAE,QAAQ;MACtBC,YAAY,EAAE;QACZC,GAAG,EAAE,GAAG;QACRC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;;IAEF;IACArC,KAAK,CAACK,OAAO,CAACiC,UAAU,CAAC,QAAQ,CAAC,CAACC,YAAY,CAAC;MAC9CJ,YAAY,EAAE;QACZC,GAAG,EAAE,GAAG;QACRC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IAEFjC,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMoC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIxC,KAAK,CAACK,OAAO,IAAIN,iBAAiB,CAACM,OAAO,EAAE;QAC9CL,KAAK,CAACK,OAAO,CAACkC,YAAY,CAAC;UACzBhB,KAAK,EAAExB,iBAAiB,CAACM,OAAO,CAACmB;QACnC,CAAC,CAAC;MACJ;IACF,CAAC;IAEDiB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MAClD,IAAIxC,KAAK,CAACK,OAAO,EAAE;QACjBL,KAAK,CAACK,OAAO,CAACuC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;;EAEZ;EACAZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACoB,UAAU,IAAI,CAACV,IAAI,IAAI,CAACoD,KAAK,CAACC,OAAO,CAACrD,IAAI,CAAC,IAAIA,IAAI,CAACsD,MAAM,KAAK,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,UAAU,GAAG,EAAE;MAErBxD,IAAI,CAACyD,OAAO,CAACC,IAAI,IAAI;QACnB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACI,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI;;QAE5D;QACAR,eAAe,CAACS,IAAI,CAAC;UACnBL,IAAI,EAAEA,IAAI;UACVM,IAAI,EAAEC,UAAU,CAACR,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC;UACtCC,IAAI,EAAEF,UAAU,CAACR,IAAI,CAACU,IAAI,IAAIV,IAAI,CAACW,EAAE,CAAC;UACtCC,GAAG,EAAEJ,UAAU,CAACR,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,EAAE,CAAC;UACpCC,KAAK,EAAEN,UAAU,CAACR,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE;QACzC,CAAC,CAAC;;QAEF;QACAjB,UAAU,CAACQ,IAAI,CAAC;UACdL,IAAI,EAAEA,IAAI;UACVe,KAAK,EAAER,UAAU,CAACR,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACkB,GAAG,CAAC;UAC1C1D,KAAK,EAAE,CAACwC,IAAI,CAACc,KAAK,IAAId,IAAI,CAACe,EAAE,MAAMf,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACS,EAAE,CAAC,GACtD,wBAAwB,GAAG;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACAZ,eAAe,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;MAC/CH,UAAU,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,IAAI,GAAGoB,CAAC,CAACpB,IAAI,CAAC;;MAE1C;MACA,IAAInD,iBAAiB,CAACI,OAAO,EAAE;QAC7BJ,iBAAiB,CAACI,OAAO,CAACoE,OAAO,CAACzB,eAAe,CAAC;MACpD;MAEA,IAAI9C,YAAY,CAACG,OAAO,EAAE;QACxBH,YAAY,CAACG,OAAO,CAACoE,OAAO,CAACxB,UAAU,CAAC;MAC1C;;MAEA;MACA,IAAIjD,KAAK,CAACK,OAAO,EAAE;QACjBL,KAAK,CAACK,OAAO,CAACe,SAAS,CAAC,CAAC,CAACsD,UAAU,CAAC,CAAC;MACxC;IAEF,CAAC,CAAC,OAAO7E,KAAK,EAAE;MACd8E,OAAO,CAAC9E,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEU,UAAU,CAAC,CAAC;EAEtB,IAAIN,KAAK,EAAE;IACT,oBACEN,OAAA,CAACJ,IAAI;MAACO,KAAK,EAAEA,KAAM;MAAAkF,QAAA,eACjBrF,OAAA,CAACF,KAAK;QACJwF,OAAO,EAAC,sCAAQ;QAChBC,WAAW,EAAEjF,KAAM;QACnBW,IAAI,EAAC,OAAO;QACZuE,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX;EAEA,oBACE5F,OAAA,CAACJ,IAAI;IACHO,KAAK,EAAEA,KAAM;IACb0F,KAAK,EAAE;MAAE7D,KAAK,EAAE;IAAO,CAAE;IACzB8D,SAAS,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,eAE/BrF,OAAA,CAACH,IAAI;MAACmG,QAAQ,EAAE3F,OAAQ;MAAC4F,GAAG,EAAC,yCAAW;MAAAZ,QAAA,gBACtCrF,OAAA;QACEkG,GAAG,EAAE1F,iBAAkB;QACvBqF,KAAK,EAAE;UACL7D,KAAK,EAAE,MAAM;UACb5B,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpB+F,QAAQ,EAAE;QACZ;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD,CAACvF,OAAO,KAAK,CAACH,IAAI,IAAIA,IAAI,CAACsD,MAAM,KAAK,CAAC,CAAC,iBACvCxD,OAAA;QACE6F,KAAK,EAAE;UACLzF,MAAM,EAAEA,MAAM,GAAG,GAAG;UACpBgG,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBlF,KAAK,EAAE,MAAM;UACbmF,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACrF,EAAA,CAlMIN,gBAAgB;AAAAuG,EAAA,GAAhBvG,gBAAgB;AAoMtB,eAAeA,gBAAgB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}