{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport WarningOutlinedSvg from \"@ant-design/icons-svg/es/asn/WarningOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar WarningOutlined = function WarningOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: WarningOutlinedSvg\n  }));\n};\n\n/**![warning](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ2NCA3MjBhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0xNi0zMDR2MTg0YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNDE2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHptNDc1LjcgNDQwbC00MTYtNzIwYy02LjItMTAuNy0xNi45LTE2LTI3LjctMTZzLTIxLjYgNS4zLTI3LjcgMTZsLTQxNiA3MjBDNTYgODc3LjQgNzEuNCA5MDQgOTYgOTA0aDgzMmMyNC42IDAgNDAtMjYuNiAyNy43LTQ4em0tNzgzLjUtMjcuOUw1MTIgMjM5LjlsMzM5LjggNTg4LjJIMTcyLjJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(WarningOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'WarningOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "WarningOutlinedSvg", "AntdIcon", "WarningOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/node_modules/@ant-design/icons/es/icons/WarningOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport WarningOutlinedSvg from \"@ant-design/icons-svg/es/asn/WarningOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar WarningOutlined = function WarningOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: WarningOutlinedSvg\n  }));\n};\n\n/**![warning](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ2NCA3MjBhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0xNi0zMDR2MTg0YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LThWNDE2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHptNDc1LjcgNDQwbC00MTYtNzIwYy02LjItMTAuNy0xNi45LTE2LTI3LjctMTZzLTIxLjYgNS4zLTI3LjcgMTZsLTQxNiA3MjBDNTYgODc3LjQgNzEuNCA5MDQgOTYgOTA0aDgzMmMyNC42IDAgNDAtMjYuNiAyNy43LTQ4em0tNzgzLjUtMjcuOUw1MTIgMjM5LjlsMzM5LjggNTg4LjJIMTcyLjJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(WarningOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'WarningOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}