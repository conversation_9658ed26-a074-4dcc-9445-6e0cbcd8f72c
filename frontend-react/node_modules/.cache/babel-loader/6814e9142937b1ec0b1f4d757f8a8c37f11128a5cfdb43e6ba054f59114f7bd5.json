{"ast": null, "code": "\"use strict\";\n\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\nvar getDotCount = function getDotCount(spec) {\n  var dots;\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots = Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) + 1;\n  }\n  return dots;\n};\nexport var Dots = /*#__PURE__*/function (_React$PureComponent) {\n  function Dots() {\n    _classCallCheck(this, Dots);\n    return _callSuper(this, Dots, arguments);\n  }\n  _inherits(Dots, _React$PureComponent);\n  return _createClass(Dots, [{\n    key: \"clickHandler\",\n    value: function clickHandler(options, e) {\n      // In Autoplay the focus stays on clicked button even after transition\n      // to next slide. That only goes away by click somewhere outside\n      e.preventDefault();\n      this.props.clickHandler(options);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave,\n        infinite = _this$props.infinite,\n        slidesToScroll = _this$props.slidesToScroll,\n        slidesToShow = _this$props.slidesToShow,\n        slideCount = _this$props.slideCount,\n        currentSlide = _this$props.currentSlide;\n      var dotCount = getDotCount({\n        slideCount: slideCount,\n        slidesToScroll: slidesToScroll,\n        slidesToShow: slidesToShow,\n        infinite: infinite\n      });\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      var dots = [];\n      for (var i = 0; i < dotCount; i++) {\n        var _rightBound = (i + 1) * slidesToScroll - 1;\n        var rightBound = infinite ? _rightBound : clamp(_rightBound, 0, slideCount - 1);\n        var _leftBound = rightBound - (slidesToScroll - 1);\n        var leftBound = infinite ? _leftBound : clamp(_leftBound, 0, slideCount - 1);\n        var className = classnames({\n          \"slick-active\": infinite ? currentSlide >= leftBound && currentSlide <= rightBound : currentSlide === leftBound\n        });\n        var dotOptions = {\n          message: \"dots\",\n          index: i,\n          slidesToScroll: slidesToScroll,\n          currentSlide: currentSlide\n        };\n        var onClick = this.clickHandler.bind(this, dotOptions);\n        dots = dots.concat(/*#__PURE__*/React.createElement(\"li\", {\n          key: i,\n          className: className\n        }, /*#__PURE__*/React.cloneElement(this.props.customPaging(i), {\n          onClick: onClick\n        })));\n      }\n      return /*#__PURE__*/React.cloneElement(this.props.appendDots(dots), _objectSpread({\n        className: this.props.dotsClass\n      }, mouseEvents));\n    }\n  }]);\n}(React.PureComponent);", "map": {"version": 3, "names": ["_objectSpread", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_isNativeReflectConstruct", "_getPrototypeOf", "_inherits", "_callSuper", "t", "o", "e", "Reflect", "construct", "constructor", "apply", "React", "classnames", "clamp", "getDotCount", "spec", "dots", "infinite", "Math", "ceil", "slideCount", "slidesToScroll", "slidesToShow", "Dots", "_React$PureComponent", "arguments", "key", "value", "clickHandler", "options", "preventDefault", "props", "render", "_this$props", "onMouseEnter", "onMouseOver", "onMouseLeave", "currentSlide", "dotCount", "mouseEvents", "i", "_rightBound", "rightBound", "_leftBound", "leftBound", "className", "dotOptions", "message", "index", "onClick", "bind", "concat", "createElement", "cloneElement", "customPaging", "appendDots", "dotsClass", "PureComponent"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/@ant-design/react-slick/es/dots.js"], "sourcesContent": ["\"use strict\";\n\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\nvar getDotCount = function getDotCount(spec) {\n  var dots;\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots = Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) + 1;\n  }\n  return dots;\n};\nexport var Dots = /*#__PURE__*/function (_React$PureComponent) {\n  function Dots() {\n    _classCallCheck(this, Dots);\n    return _callSuper(this, Dots, arguments);\n  }\n  _inherits(Dots, _React$PureComponent);\n  return _createClass(Dots, [{\n    key: \"clickHandler\",\n    value: function clickHandler(options, e) {\n      // In Autoplay the focus stays on clicked button even after transition\n      // to next slide. That only goes away by click somewhere outside\n      e.preventDefault();\n      this.props.clickHandler(options);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave,\n        infinite = _this$props.infinite,\n        slidesToScroll = _this$props.slidesToScroll,\n        slidesToShow = _this$props.slidesToShow,\n        slideCount = _this$props.slideCount,\n        currentSlide = _this$props.currentSlide;\n      var dotCount = getDotCount({\n        slideCount: slideCount,\n        slidesToScroll: slidesToScroll,\n        slidesToShow: slidesToShow,\n        infinite: infinite\n      });\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      var dots = [];\n      for (var i = 0; i < dotCount; i++) {\n        var _rightBound = (i + 1) * slidesToScroll - 1;\n        var rightBound = infinite ? _rightBound : clamp(_rightBound, 0, slideCount - 1);\n        var _leftBound = rightBound - (slidesToScroll - 1);\n        var leftBound = infinite ? _leftBound : clamp(_leftBound, 0, slideCount - 1);\n        var className = classnames({\n          \"slick-active\": infinite ? currentSlide >= leftBound && currentSlide <= rightBound : currentSlide === leftBound\n        });\n        var dotOptions = {\n          message: \"dots\",\n          index: i,\n          slidesToScroll: slidesToScroll,\n          currentSlide: currentSlide\n        };\n        var onClick = this.clickHandler.bind(this, dotOptions);\n        dots = dots.concat( /*#__PURE__*/React.createElement(\"li\", {\n          key: i,\n          className: className\n        }, /*#__PURE__*/React.cloneElement(this.props.customPaging(i), {\n          onClick: onClick\n        })));\n      }\n      return /*#__PURE__*/React.cloneElement(this.props.appendDots(dots), _objectSpread({\n        className: this.props.dotsClass\n      }, mouseEvents));\n    }\n  }]);\n}(React.PureComponent);"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,GAAGJ,eAAe,CAACI,CAAC,CAAC,EAAEN,0BAA0B,CAACK,CAAC,EAAEJ,yBAAyB,CAAC,CAAC,GAAGO,OAAO,CAACC,SAAS,CAACH,CAAC,EAAEC,CAAC,IAAI,EAAE,EAAEL,eAAe,CAACG,CAAC,CAAC,CAACK,WAAW,CAAC,GAAGJ,CAAC,CAACK,KAAK,CAACN,CAAC,EAAEE,CAAC,CAAC,CAAC;AAAE;AAC1M,OAAOK,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,0BAA0B;AAChD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI;EACR,IAAID,IAAI,CAACE,QAAQ,EAAE;IACjBD,IAAI,GAAGE,IAAI,CAACC,IAAI,CAACJ,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACM,cAAc,CAAC;EACzD,CAAC,MAAM;IACLL,IAAI,GAAGE,IAAI,CAACC,IAAI,CAAC,CAACJ,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACO,YAAY,IAAIP,IAAI,CAACM,cAAc,CAAC,GAAG,CAAC;EACnF;EACA,OAAOL,IAAI;AACb,CAAC;AACD,OAAO,IAAIO,IAAI,GAAG,aAAa,UAAUC,oBAAoB,EAAE;EAC7D,SAASD,IAAIA,CAAA,EAAG;IACd1B,eAAe,CAAC,IAAI,EAAE0B,IAAI,CAAC;IAC3B,OAAOpB,UAAU,CAAC,IAAI,EAAEoB,IAAI,EAAEE,SAAS,CAAC;EAC1C;EACAvB,SAAS,CAACqB,IAAI,EAAEC,oBAAoB,CAAC;EACrC,OAAO1B,YAAY,CAACyB,IAAI,EAAE,CAAC;IACzBG,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASC,YAAYA,CAACC,OAAO,EAAEvB,CAAC,EAAE;MACvC;MACA;MACAA,CAAC,CAACwB,cAAc,CAAC,CAAC;MAClB,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC;IAClC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASK,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACF,KAAK;QAC1BG,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,YAAY,GAAGH,WAAW,CAACG,YAAY;QACvCnB,QAAQ,GAAGgB,WAAW,CAAChB,QAAQ;QAC/BI,cAAc,GAAGY,WAAW,CAACZ,cAAc;QAC3CC,YAAY,GAAGW,WAAW,CAACX,YAAY;QACvCF,UAAU,GAAGa,WAAW,CAACb,UAAU;QACnCiB,YAAY,GAAGJ,WAAW,CAACI,YAAY;MACzC,IAAIC,QAAQ,GAAGxB,WAAW,CAAC;QACzBM,UAAU,EAAEA,UAAU;QACtBC,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA,YAAY;QAC1BL,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF,IAAIsB,WAAW,GAAG;QAChBL,YAAY,EAAEA,YAAY;QAC1BC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA;MAChB,CAAC;MACD,IAAIpB,IAAI,GAAG,EAAE;MACb,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;QACjC,IAAIC,WAAW,GAAG,CAACD,CAAC,GAAG,CAAC,IAAInB,cAAc,GAAG,CAAC;QAC9C,IAAIqB,UAAU,GAAGzB,QAAQ,GAAGwB,WAAW,GAAG5B,KAAK,CAAC4B,WAAW,EAAE,CAAC,EAAErB,UAAU,GAAG,CAAC,CAAC;QAC/E,IAAIuB,UAAU,GAAGD,UAAU,IAAIrB,cAAc,GAAG,CAAC,CAAC;QAClD,IAAIuB,SAAS,GAAG3B,QAAQ,GAAG0B,UAAU,GAAG9B,KAAK,CAAC8B,UAAU,EAAE,CAAC,EAAEvB,UAAU,GAAG,CAAC,CAAC;QAC5E,IAAIyB,SAAS,GAAGjC,UAAU,CAAC;UACzB,cAAc,EAAEK,QAAQ,GAAGoB,YAAY,IAAIO,SAAS,IAAIP,YAAY,IAAIK,UAAU,GAAGL,YAAY,KAAKO;QACxG,CAAC,CAAC;QACF,IAAIE,UAAU,GAAG;UACfC,OAAO,EAAE,MAAM;UACfC,KAAK,EAAER,CAAC;UACRnB,cAAc,EAAEA,cAAc;UAC9BgB,YAAY,EAAEA;QAChB,CAAC;QACD,IAAIY,OAAO,GAAG,IAAI,CAACrB,YAAY,CAACsB,IAAI,CAAC,IAAI,EAAEJ,UAAU,CAAC;QACtD9B,IAAI,GAAGA,IAAI,CAACmC,MAAM,CAAE,aAAaxC,KAAK,CAACyC,aAAa,CAAC,IAAI,EAAE;UACzD1B,GAAG,EAAEc,CAAC;UACNK,SAAS,EAAEA;QACb,CAAC,EAAE,aAAalC,KAAK,CAAC0C,YAAY,CAAC,IAAI,CAACtB,KAAK,CAACuB,YAAY,CAACd,CAAC,CAAC,EAAE;UAC7DS,OAAO,EAAEA;QACX,CAAC,CAAC,CAAC,CAAC;MACN;MACA,OAAO,aAAatC,KAAK,CAAC0C,YAAY,CAAC,IAAI,CAACtB,KAAK,CAACwB,UAAU,CAACvC,IAAI,CAAC,EAAEpB,aAAa,CAAC;QAChFiD,SAAS,EAAE,IAAI,CAACd,KAAK,CAACyB;MACxB,CAAC,EAAEjB,WAAW,CAAC,CAAC;IAClB;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC5B,KAAK,CAAC8C,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}