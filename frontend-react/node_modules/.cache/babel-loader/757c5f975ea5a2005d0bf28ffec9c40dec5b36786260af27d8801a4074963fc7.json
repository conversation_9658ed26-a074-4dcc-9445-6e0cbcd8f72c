{"ast": null, "code": "import * as React from 'react';\nimport useForceUpdate from './useForceUpdate';\nexport default function useSyncState(initialValue) {\n  const ref = React.useRef(initialValue);\n  const forceUpdate = useForceUpdate();\n  return [() => ref.current, newValue => {\n    ref.current = newValue;\n    // re-render\n    forceUpdate();\n  }];\n}", "map": {"version": 3, "names": ["React", "useForceUpdate", "useSyncState", "initialValue", "ref", "useRef", "forceUpdate", "current", "newValue"], "sources": ["/Users/<USER>/Documents/stock_data_project/frontend-react/node_modules/antd/es/_util/hooks/useSyncState.js"], "sourcesContent": ["import * as React from 'react';\nimport useForceUpdate from './useForceUpdate';\nexport default function useSyncState(initialValue) {\n  const ref = React.useRef(initialValue);\n  const forceUpdate = useForceUpdate();\n  return [() => ref.current, newValue => {\n    ref.current = newValue;\n    // re-render\n    forceUpdate();\n  }];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAE;EACjD,MAAMC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAACF,YAAY,CAAC;EACtC,MAAMG,WAAW,GAAGL,cAAc,CAAC,CAAC;EACpC,OAAO,CAAC,MAAMG,GAAG,CAACG,OAAO,EAAEC,QAAQ,IAAI;IACrCJ,GAAG,CAACG,OAAO,GAAGC,QAAQ;IACtB;IACAF,WAAW,CAAC,CAAC;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}