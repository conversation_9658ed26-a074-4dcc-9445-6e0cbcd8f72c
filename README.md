# 股票数据分析产品 v1.0

## 项目概述
基于Python + AkShare + MySQL + FastAPI + Streamlit的股票K线走势相似性检索系统。

## 功能特性
- 📥 股票数据拉取与管理（支持A股日线数据）
- 📐 K线走势特征建模（向量化表示）
- 🔍 全局走势检索（基于FAISS的相似性搜索）
- 📊 可视化展示（K线图表与匹配结果）

## 技术栈
- **数据源**: AkShare
- **数据处理**: Pandas, NumPy
- **特征工程**: Scikit-learn
- **检索引擎**: FAISS
- **后端API**: FastAPI
- **前端界面**: Streamlit
- **数据存储**: MySQL

## 项目结构
```
stock_data_project/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── main.py             # FastAPI应用入口
│   └── requirements.txt    # 后端依赖
├── frontend/               # 前端界面
│   ├── pages/              # Streamlit页面
│   ├── components/         # 组件
│   ├── app.py              # Streamlit应用入口
│   └── requirements.txt    # 前端依赖
├── shared/                 # 共享模块
│   ├── config/             # 配置文件
│   └── utils/              # 共享工具
├── scripts/                # 脚本文件
├── data/                   # 数据文件
├── logs/                   # 日志文件
└── docker-compose.yml      # 容器编排
```

## 快速开始

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r backend/requirements.txt
pip install -r frontend/requirements.txt
```

### 2. 数据库配置
- MySQL服务器: localhost:3306
- 数据库: stock
- 用户名: root
- 密码: SD0916sd!

### 3. 启动服务
```bash
# 启动后端API服务
cd backend && python main.py

# 启动前端界面
cd frontend && streamlit run app.py
```

## API文档
启动后端服务后访问: http://localhost:8000/docs

## 开发指南
详见各模块的README文件。
