import{s as c,bm as f,i as S,r as a,j as i,C as g,c8 as m,aG as T}from"./index.BTGIlECR.js";const $=c(f,{shouldForwardProp:S,target:"e12gfcky0"})(({theme:e})=>({fontSize:e.fontSizes.sm,width:e.sizes.spinnerSize,height:e.sizes.spinnerSize,borderWidth:e.sizes.spinnerThickness,justifyContents:"center",padding:e.spacing.none,margin:e.spacing.none,borderColor:e.colors.borderColor,borderTopColor:e.colors.secondary,flexGrow:0,flexShrink:0})),x=c("div",{target:"e12gfcky1"})(({theme:e,cache:t})=>({...t?{paddingBottom:e.spacing.lg,background:`linear-gradient(to bottom, ${e.colors.bgColor} 0%, ${e.colors.bgColor} 80%, transparent 100%)`}:null})),y=c("div",{target:"e12gfcky2"})(({theme:e})=>({opacity:.6,fontSize:e.fontSizes.sm})),h=c("div",{target:"e12gfcky3"})(({theme:e})=>({display:"flex",gap:e.spacing.sm,alignItems:"center",width:"100%"})),b=e=>{const t=Math.floor(e/3600),n=Math.floor(e%3600/60),s=e%60;if(t===0&&n===0)return`(${s.toFixed(1)} seconds)`;if(t===0){const d=`${n} minute${n===1?"":"s"}`,p=s===0?"":`, ${s.toFixed(1)} seconds`;return`(${d}${p})`}const l=`${t} hour${t===1?"":"s"}`,r=n===0?"":`, ${n} minute${n===1?"":"s"}`,o=s===0?"":`, ${s.toFixed(1)} seconds`;return`(${l}${r}${o})`};function w({element:e}){const{cache:t,showTime:n}=e,[s,l]=a.useState(0),r=a.useRef(null);return a.useEffect(()=>{if(!n)return;r.current=Date.now();const o=()=>{if(r.current!==null){const u=(Date.now()-r.current)/1e3;l(u)}};o();const d=setInterval(o,100);return()=>clearInterval(d)},[n]),i(x,{className:m({stSpinner:!0,stCacheSpinner:t}),"data-testid":"stSpinner",cache:t,children:g(h,{children:[i($,{}),i(T,{source:e.text,allowHTML:!1}),n&&i(y,{children:b(s)})]})})}const C=a.memo(w);export{C as default};
