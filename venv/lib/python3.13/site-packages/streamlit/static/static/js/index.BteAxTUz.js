import{r as l,b4 as a,j as e,b5 as d,bp as u,b3 as c,b6 as B,b7 as b}from"./index.BTGIlECR.js";function m(i){const{disabled:o,element:t,widgetMgr:s,fragmentId:r}=i;let n=a.SECONDARY;return t.type==="primary"?n=a.PRIMARY:t.type==="tertiary"&&(n=a.TERTIARY),e(b,{className:"stButton","data-testid":"stButton",children:e(d,{help:t.help,containerWidth:t.useContainerWidth,children:e(u,{kind:n,size:c.SMALL,disabled:o,containerWidth:t.useContainerWidth,onClick:()=>s.setTriggerValue(t,{fromUi:!0},r),children:e(B,{icon:t.icon,label:t.label})})})})}const h=l.memo(m);export{h as default};
