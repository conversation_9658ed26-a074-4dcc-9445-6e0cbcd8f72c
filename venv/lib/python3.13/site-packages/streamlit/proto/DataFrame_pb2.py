# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/DataFrame.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import Common_pb2 as streamlit_dot_proto_dot_Common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/DataFrame.proto\x1a\x1cstreamlit/proto/Common.proto\"m\n\tDataFrame\x12\x14\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x06.Table\x12\x15\n\x05index\x18\x02 \x01(\x0b\x32\x06.Index\x12\x17\n\x07\x63olumns\x18\x03 \x01(\x0b\x32\x06.Index\x12\x1a\n\x05style\x18\x04 \x01(\x0b\x32\x0b.TableStyle\"\x9f\x02\n\x05Index\x12\"\n\x0bplain_index\x18\x01 \x01(\x0b\x32\x0b.PlainIndexH\x00\x12\"\n\x0brange_index\x18\x02 \x01(\x0b\x32\x0b.RangeIndexH\x00\x12\"\n\x0bmulti_index\x18\x04 \x01(\x0b\x32\x0b.MultiIndexH\x00\x12(\n\x0e\x64\x61tetime_index\x18\x06 \x01(\x0b\x32\x0e.DatetimeIndexH\x00\x12*\n\x0ftimedelta_index\x18\x07 \x01(\x0b\x32\x0f.TimedeltaIndexH\x00\x12#\n\x0cint_64_index\x18\t \x01(\x0b\x32\x0b.Int64IndexH\x00\x12\'\n\x0e\x66loat_64_index\x18\x0b \x01(\x0b\x32\r.Float64IndexH\x00\x42\x06\n\x04type\"%\n\nPlainIndex\x12\x17\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\t.AnyArray\")\n\nRangeIndex\x12\r\n\x05start\x18\x01 \x01(\x03\x12\x0c\n\x04stop\x18\x02 \x01(\x03\"A\n\nMultiIndex\x12\x16\n\x06levels\x18\x01 \x03(\x0b\x32\x06.Index\x12\x1b\n\x06labels\x18\x02 \x03(\x0b\x32\x0b.Int32Array\"+\n\rDatetimeIndex\x12\x1a\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x0c.StringArray\"+\n\x0eTimedeltaIndex\x12\x19\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x0b.Int64Array\"\'\n\nInt64Index\x12\x19\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x0b.Int64Array\"*\n\x0c\x46loat64Index\x12\x1a\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x0c.DoubleArray\"+\n\x08\x43SSStyle\x12\x10\n\x08property\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"U\n\tCellStyle\x12\x16\n\x03\x63ss\x18\x01 \x03(\x0b\x32\t.CSSStyle\x12\x15\n\rdisplay_value\x18\x02 \x01(\t\x12\x19\n\x11has_display_value\x18\x03 \x01(\x08\",\n\x0e\x43\x65llStyleArray\x12\x1a\n\x06styles\x18\x01 \x03(\x0b\x32\n.CellStyle\"\xb9\x01\n\x08\x41nyArray\x12\x1f\n\x07strings\x18\x01 \x01(\x0b\x32\x0c.StringArrayH\x00\x12\x1f\n\x07\x64oubles\x18\x02 \x01(\x0b\x32\x0c.DoubleArrayH\x00\x12\x1d\n\x06int64s\x18\x03 \x01(\x0b\x32\x0b.Int64ArrayH\x00\x12!\n\tdatetimes\x18\x04 \x01(\x0b\x32\x0c.StringArrayH\x00\x12!\n\ntimedeltas\x18\x05 \x01(\x0b\x32\x0b.Int64ArrayH\x00\x42\x06\n\x04type\" \n\x05Table\x12\x17\n\x04\x63ols\x18\x01 \x03(\x0b\x32\t.AnyArray\"+\n\nTableStyle\x12\x1d\n\x04\x63ols\x18\x01 \x03(\x0b\x32\x0f.CellStyleArrayB.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x44\x61taFrameProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.DataFrame_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016DataFrameProto'
  _globals['_DATAFRAME']._serialized_start=65
  _globals['_DATAFRAME']._serialized_end=174
  _globals['_INDEX']._serialized_start=177
  _globals['_INDEX']._serialized_end=464
  _globals['_PLAININDEX']._serialized_start=466
  _globals['_PLAININDEX']._serialized_end=503
  _globals['_RANGEINDEX']._serialized_start=505
  _globals['_RANGEINDEX']._serialized_end=546
  _globals['_MULTIINDEX']._serialized_start=548
  _globals['_MULTIINDEX']._serialized_end=613
  _globals['_DATETIMEINDEX']._serialized_start=615
  _globals['_DATETIMEINDEX']._serialized_end=658
  _globals['_TIMEDELTAINDEX']._serialized_start=660
  _globals['_TIMEDELTAINDEX']._serialized_end=703
  _globals['_INT64INDEX']._serialized_start=705
  _globals['_INT64INDEX']._serialized_end=744
  _globals['_FLOAT64INDEX']._serialized_start=746
  _globals['_FLOAT64INDEX']._serialized_end=788
  _globals['_CSSSTYLE']._serialized_start=790
  _globals['_CSSSTYLE']._serialized_end=833
  _globals['_CELLSTYLE']._serialized_start=835
  _globals['_CELLSTYLE']._serialized_end=920
  _globals['_CELLSTYLEARRAY']._serialized_start=922
  _globals['_CELLSTYLEARRAY']._serialized_end=966
  _globals['_ANYARRAY']._serialized_start=969
  _globals['_ANYARRAY']._serialized_end=1154
  _globals['_TABLE']._serialized_start=1156
  _globals['_TABLE']._serialized_end=1188
  _globals['_TABLESTYLE']._serialized_start=1190
  _globals['_TABLESTYLE']._serialized_end=1233
# @@protoc_insertion_point(module_scope)
