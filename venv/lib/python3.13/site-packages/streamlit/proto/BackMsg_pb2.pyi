"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import streamlit.proto.ClientState_pb2
import streamlit.proto.Common_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class BackMsg(google.protobuf.message.Message):
    """A message from the browser to the server."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLEAR_CACHE_FIELD_NUMBER: builtins.int
    SET_RUN_ON_SAVE_FIELD_NUMBER: builtins.int
    STOP_SCRIPT_FIELD_NUMBER: builtins.int
    RERUN_SCRIPT_FIELD_NUMBER: builtins.int
    LOAD_GIT_INFO_FIELD_NUMBER: builtins.int
    DEBUG_DISCONNECT_WEBSOCKET_FIELD_NUMBER: builtins.int
    DEBUG_SHUTDOWN_RUNTIME_FIELD_NUMBER: builtins.int
    FILE_URLS_REQUEST_FIELD_NUMBER: builtins.int
    APP_HEARTBEAT_FIELD_NUMBER: builtins.int
    DEBUG_LAST_BACKMSG_ID_FIELD_NUMBER: builtins.int
    clear_cache: builtins.bool
    """DEPRECATED. Asks the server to run the script with this object
    ReRun rerun = 4;

    Requests that the app's @st_cache be cleared
    """
    set_run_on_save: builtins.bool
    """Requests that the runOnSave behavior for this app be set
    to the given value
    """
    stop_script: builtins.bool
    """Requests that the script's execution be stopped"""
    load_git_info: builtins.bool
    debug_disconnect_websocket: builtins.bool
    """Test and dev-mode only field used to ask the server to disconnect the
    client's websocket connection. This message is IGNORED unless the
    runtime is configured with global.developmentMode = True.
    """
    debug_shutdown_runtime: builtins.bool
    """Test and dev-mode only field used to ask the server to shut down the
    runtime. This message is IGNORED unless the runtime is configured with
    global.developmentMode = True.
    """
    app_heartbeat: builtins.bool
    """Sends an app heartbeat message through the websocket"""
    debug_last_backmsg_id: builtins.str
    """An ID used to associate this BackMsg with the corresponding ForwardMsgs
    that are sent to the client due to it. As its name suggests, this field
    should only be used for testing.
    """
    @property
    def rerun_script(self) -> streamlit.proto.ClientState_pb2.ClientState:
        """DEPRECATED. Set to true to ask the server to close the connection
        bool close_connection = 10;
        """

    @property
    def file_urls_request(self) -> streamlit.proto.Common_pb2.FileURLsRequest:
        """Requests that the server generate URLs for getting/uploading/deleting
        files for the `st.file_uploader` widget
        """

    def __init__(
        self,
        *,
        clear_cache: builtins.bool = ...,
        set_run_on_save: builtins.bool = ...,
        stop_script: builtins.bool = ...,
        rerun_script: streamlit.proto.ClientState_pb2.ClientState | None = ...,
        load_git_info: builtins.bool = ...,
        debug_disconnect_websocket: builtins.bool = ...,
        debug_shutdown_runtime: builtins.bool = ...,
        file_urls_request: streamlit.proto.Common_pb2.FileURLsRequest | None = ...,
        app_heartbeat: builtins.bool = ...,
        debug_last_backmsg_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["app_heartbeat", b"app_heartbeat", "clear_cache", b"clear_cache", "debug_disconnect_websocket", b"debug_disconnect_websocket", "debug_shutdown_runtime", b"debug_shutdown_runtime", "file_urls_request", b"file_urls_request", "load_git_info", b"load_git_info", "rerun_script", b"rerun_script", "set_run_on_save", b"set_run_on_save", "stop_script", b"stop_script", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["app_heartbeat", b"app_heartbeat", "clear_cache", b"clear_cache", "debug_disconnect_websocket", b"debug_disconnect_websocket", "debug_last_backmsg_id", b"debug_last_backmsg_id", "debug_shutdown_runtime", b"debug_shutdown_runtime", "file_urls_request", b"file_urls_request", "load_git_info", b"load_git_info", "rerun_script", b"rerun_script", "set_run_on_save", b"set_run_on_save", "stop_script", b"stop_script", "type", b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["type", b"type"]) -> typing.Literal["clear_cache", "set_run_on_save", "stop_script", "rerun_script", "load_git_info", "debug_disconnect_websocket", "debug_shutdown_runtime", "file_urls_request", "app_heartbeat"] | None: ...

global___BackMsg = BackMsg
