"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.Common_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class DataFrame(google.protobuf.message.Message):
    """DEPRECATED: This proto message is deprecated and unused. Use Arrow.proto instead.
    Represents a pandas DataFrame.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    INDEX_FIELD_NUMBER: builtins.int
    COLUMNS_FIELD_NUMBER: builtins.int
    STYLE_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___Table:
        """The data in the array."""

    @property
    def index(self) -> global___Index:
        """List of row names. (Multiple implies a multi-index.)"""

    @property
    def columns(self) -> global___Index:
        """List of column names. (Multiple implies a multi-index.)"""

    @property
    def style(self) -> global___TableStyle:
        """Cell style and formatting data. Optional."""

    def __init__(
        self,
        *,
        data: global___Table | None = ...,
        index: global___Index | None = ...,
        columns: global___Index | None = ...,
        style: global___TableStyle | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["columns", b"columns", "data", b"data", "index", b"index", "style", b"style"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["columns", b"columns", "data", b"data", "index", b"index", "style", b"style"]) -> None: ...

global___DataFrame = DataFrame

@typing.final
class Index(google.protobuf.message.Message):
    """An index in the dataFrame"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAIN_INDEX_FIELD_NUMBER: builtins.int
    RANGE_INDEX_FIELD_NUMBER: builtins.int
    MULTI_INDEX_FIELD_NUMBER: builtins.int
    DATETIME_INDEX_FIELD_NUMBER: builtins.int
    TIMEDELTA_INDEX_FIELD_NUMBER: builtins.int
    INT_64_INDEX_FIELD_NUMBER: builtins.int
    FLOAT_64_INDEX_FIELD_NUMBER: builtins.int
    @property
    def plain_index(self) -> global___PlainIndex: ...
    @property
    def range_index(self) -> global___RangeIndex: ...
    @property
    def multi_index(self) -> global___MultiIndex:
        """CategoricalIndex categorical_index = 3;"""

    @property
    def datetime_index(self) -> global___DatetimeIndex:
        """IntervalIndex interval_index = 5;"""

    @property
    def timedelta_index(self) -> global___TimedeltaIndex: ...
    @property
    def int_64_index(self) -> global___Int64Index:
        """PeriodIndex period_index = 8;"""

    @property
    def float_64_index(self) -> global___Float64Index:
        """UInt64Index uint_64_index = 10;"""

    def __init__(
        self,
        *,
        plain_index: global___PlainIndex | None = ...,
        range_index: global___RangeIndex | None = ...,
        multi_index: global___MultiIndex | None = ...,
        datetime_index: global___DatetimeIndex | None = ...,
        timedelta_index: global___TimedeltaIndex | None = ...,
        int_64_index: global___Int64Index | None = ...,
        float_64_index: global___Float64Index | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["datetime_index", b"datetime_index", "float_64_index", b"float_64_index", "int_64_index", b"int_64_index", "multi_index", b"multi_index", "plain_index", b"plain_index", "range_index", b"range_index", "timedelta_index", b"timedelta_index", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["datetime_index", b"datetime_index", "float_64_index", b"float_64_index", "int_64_index", b"int_64_index", "multi_index", b"multi_index", "plain_index", b"plain_index", "range_index", b"range_index", "timedelta_index", b"timedelta_index", "type", b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["type", b"type"]) -> typing.Literal["plain_index", "range_index", "multi_index", "datetime_index", "timedelta_index", "int_64_index", "float_64_index"] | None: ...

global___Index = Index

@typing.final
class PlainIndex(google.protobuf.message.Message):
    """Basic, 1D index."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___AnyArray: ...
    def __init__(
        self,
        *,
        data: global___AnyArray | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___PlainIndex = PlainIndex

@typing.final
class RangeIndex(google.protobuf.message.Message):
    """Range index. See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.RangeIndex.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    START_FIELD_NUMBER: builtins.int
    STOP_FIELD_NUMBER: builtins.int
    start: builtins.int
    stop: builtins.int
    def __init__(
        self,
        *,
        start: builtins.int = ...,
        stop: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["start", b"start", "stop", b"stop"]) -> None: ...

global___RangeIndex = RangeIndex

@typing.final
class MultiIndex(google.protobuf.message.Message):
    """A multi-level, or hierarchical, Index. See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.MultiIndex.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LEVELS_FIELD_NUMBER: builtins.int
    LABELS_FIELD_NUMBER: builtins.int
    @property
    def levels(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Index]: ...
    @property
    def labels(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[streamlit.proto.Common_pb2.Int32Array]: ...
    def __init__(
        self,
        *,
        levels: collections.abc.Iterable[global___Index] | None = ...,
        labels: collections.abc.Iterable[streamlit.proto.Common_pb2.Int32Array] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["labels", b"labels", "levels", b"levels"]) -> None: ...

global___MultiIndex = MultiIndex

@typing.final
class DatetimeIndex(google.protobuf.message.Message):
    """A date represented internally as nano second epoch int64. See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.DatetimeIndex.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> streamlit.proto.Common_pb2.StringArray: ...
    def __init__(
        self,
        *,
        data: streamlit.proto.Common_pb2.StringArray | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___DatetimeIndex = DatetimeIndex

@typing.final
class TimedeltaIndex(google.protobuf.message.Message):
    """A time interval represented internally as nano second epoch int64. See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.TimedeltaIndex.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> streamlit.proto.Common_pb2.Int64Array: ...
    def __init__(
        self,
        *,
        data: streamlit.proto.Common_pb2.Int64Array | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___TimedeltaIndex = TimedeltaIndex

@typing.final
class Int64Index(google.protobuf.message.Message):
    """See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.Int64Index.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> streamlit.proto.Common_pb2.Int64Array: ...
    def __init__(
        self,
        *,
        data: streamlit.proto.Common_pb2.Int64Array | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___Int64Index = Int64Index

@typing.final
class Float64Index(google.protobuf.message.Message):
    """See:
    https://pandas.pydata.org/pandas-docs/stable/generated/pandas.Int64Index.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> streamlit.proto.Common_pb2.DoubleArray: ...
    def __init__(
        self,
        *,
        data: streamlit.proto.Common_pb2.DoubleArray | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___Float64Index = Float64Index

@typing.final
class CSSStyle(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROPERTY_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    property: builtins.str
    value: builtins.str
    def __init__(
        self,
        *,
        property: builtins.str = ...,
        value: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["property", b"property", "value", b"value"]) -> None: ...

global___CSSStyle = CSSStyle

@typing.final
class CellStyle(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CSS_FIELD_NUMBER: builtins.int
    DISPLAY_VALUE_FIELD_NUMBER: builtins.int
    HAS_DISPLAY_VALUE_FIELD_NUMBER: builtins.int
    display_value: builtins.str
    """e.g. '21.96%'"""
    has_display_value: builtins.bool
    """The default value for a string field in proto3 is '', so we need
    this extra bool to indicate the presence of a user-specified
    display_value, which itself could be ''.
    """
    @property
    def css(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CSSStyle]: ...
    def __init__(
        self,
        *,
        css: collections.abc.Iterable[global___CSSStyle] | None = ...,
        display_value: builtins.str = ...,
        has_display_value: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["css", b"css", "display_value", b"display_value", "has_display_value", b"has_display_value"]) -> None: ...

global___CellStyle = CellStyle

@typing.final
class CellStyleArray(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STYLES_FIELD_NUMBER: builtins.int
    @property
    def styles(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CellStyle]: ...
    def __init__(
        self,
        *,
        styles: collections.abc.Iterable[global___CellStyle] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["styles", b"styles"]) -> None: ...

global___CellStyleArray = CellStyleArray

@typing.final
class AnyArray(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STRINGS_FIELD_NUMBER: builtins.int
    DOUBLES_FIELD_NUMBER: builtins.int
    INT64S_FIELD_NUMBER: builtins.int
    DATETIMES_FIELD_NUMBER: builtins.int
    TIMEDELTAS_FIELD_NUMBER: builtins.int
    @property
    def strings(self) -> streamlit.proto.Common_pb2.StringArray: ...
    @property
    def doubles(self) -> streamlit.proto.Common_pb2.DoubleArray: ...
    @property
    def int64s(self) -> streamlit.proto.Common_pb2.Int64Array: ...
    @property
    def datetimes(self) -> streamlit.proto.Common_pb2.StringArray: ...
    @property
    def timedeltas(self) -> streamlit.proto.Common_pb2.Int64Array: ...
    def __init__(
        self,
        *,
        strings: streamlit.proto.Common_pb2.StringArray | None = ...,
        doubles: streamlit.proto.Common_pb2.DoubleArray | None = ...,
        int64s: streamlit.proto.Common_pb2.Int64Array | None = ...,
        datetimes: streamlit.proto.Common_pb2.StringArray | None = ...,
        timedeltas: streamlit.proto.Common_pb2.Int64Array | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["datetimes", b"datetimes", "doubles", b"doubles", "int64s", b"int64s", "strings", b"strings", "timedeltas", b"timedeltas", "type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["datetimes", b"datetimes", "doubles", b"doubles", "int64s", b"int64s", "strings", b"strings", "timedeltas", b"timedeltas", "type", b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["type", b"type"]) -> typing.Literal["strings", "doubles", "int64s", "datetimes", "timedeltas"] | None: ...

global___AnyArray = AnyArray

@typing.final
class Table(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AnyArray]: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___AnyArray] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols"]) -> None: ...

global___Table = Table

@typing.final
class TableStyle(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CellStyleArray]: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___CellStyleArray] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols"]) -> None: ...

global___TableStyle = TableStyle
