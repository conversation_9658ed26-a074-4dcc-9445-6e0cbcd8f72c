# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/PageConfig.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n streamlit/proto/PageConfig.proto\"\xd4\x03\n\nPageConfig\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0f\n\x07\x66\x61vicon\x18\x02 \x01(\t\x12\"\n\x06layout\x18\x03 \x01(\x0e\x32\x12.PageConfig.Layout\x12\x37\n\x15initial_sidebar_state\x18\x04 \x01(\x0e\x32\x18.PageConfig.SidebarState\x12)\n\nmenu_items\x18\x05 \x01(\x0b\x32\x15.PageConfig.MenuItems\x1a\x9f\x01\n\tMenuItems\x12\x14\n\x0cget_help_url\x18\x01 \x01(\t\x12\x15\n\rhide_get_help\x18\x02 \x01(\x08\x12\x18\n\x10report_a_bug_url\x18\x03 \x01(\t\x12\x19\n\x11hide_report_a_bug\x18\x04 \x01(\x08\x12\x18\n\x10\x61\x62out_section_md\x18\x05 \x01(\t\x12\x16\n\x0e\x63lear_about_md\x18\x06 \x01(\x08\"2\n\x06Layout\x12\x0c\n\x08\x43\x45NTERED\x10\x00\x12\x08\n\x04WIDE\x10\x01\x12\x10\n\x0cLAYOUT_UNSET\x10\x02\"H\n\x0cSidebarState\x12\x08\n\x04\x41UTO\x10\x00\x12\x0c\n\x08\x45XPANDED\x10\x01\x12\r\n\tCOLLAPSED\x10\x02\x12\x11\n\rSIDEBAR_UNSET\x10\x03\x42/\n\x1c\x63om.snowflake.apps.streamlitB\x0fPageConfigProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.PageConfig_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\017PageConfigProto'
  _globals['_PAGECONFIG']._serialized_start=37
  _globals['_PAGECONFIG']._serialized_end=505
  _globals['_PAGECONFIG_MENUITEMS']._serialized_start=220
  _globals['_PAGECONFIG_MENUITEMS']._serialized_end=379
  _globals['_PAGECONFIG_LAYOUT']._serialized_start=381
  _globals['_PAGECONFIG_LAYOUT']._serialized_end=431
  _globals['_PAGECONFIG_SIDEBARSTATE']._serialized_start=433
  _globals['_PAGECONFIG_SIDEBARSTATE']._serialized_end=505
# @@protoc_insertion_point(module_scope)
