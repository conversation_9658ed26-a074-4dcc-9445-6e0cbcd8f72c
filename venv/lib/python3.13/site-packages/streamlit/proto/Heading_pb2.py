# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Heading.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dstreamlit/proto/Heading.proto\"h\n\x07Heading\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06\x61nchor\x18\x02 \x01(\t\x12\x0c\n\x04\x62ody\x18\x03 \x01(\t\x12\x0c\n\x04help\x18\x04 \x01(\t\x12\x13\n\x0bhide_anchor\x18\x05 \x01(\x08\x12\x0f\n\x07\x64ivider\x18\x06 \x01(\tB,\n\x1c\x63om.snowflake.apps.streamlitB\x0cHeadingProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Heading_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\014HeadingProto'
  _globals['_HEADING']._serialized_start=33
  _globals['_HEADING']._serialized_end=137
# @@protoc_insertion_point(module_scope)
