{"version": 3, "sources": ["index.css"], "names": [], "mappings": "AAAA,EACE,qBACF,CAEA,KACE,QAAS,CACT,mJAE8B,CAC9B,kCAAmC,CACnC,iCAAkC,CAClC,sBACF", "file": "main.d984dddf.chunk.css", "sourcesContent": ["* {\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\",\r\n    \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\",\r\n    \"Helvetica Neue\", sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: transparent;\r\n}"]}