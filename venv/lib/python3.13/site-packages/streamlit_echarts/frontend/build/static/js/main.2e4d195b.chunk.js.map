{"version": 3, "sources": ["utils.ts", "EchartsChart.tsx", "index.tsx"], "names": ["deepMap", "obj", "iterator", "context", "transform", "result", "val", "key", "isObject", "call", "withStreamlitConnection", "props", "themeProp", "echartsElementRef", "useRef", "echartsInstanceRef", "evalStringToFunction", "s", "match", "RegExp", "exec", "funcStr", "Function", "args", "options", "theme", "onEvents", "height", "width", "renderer", "map", "cleanTheme", "echarts", "mapName", "geoJson", "<PERSON><PERSON><PERSON><PERSON>", "cleanOptions", "cleanOnEvents", "Object", "keys", "eventFunction", "useCallback", "params", "Streamlit", "setComponentValue", "useEffect", "current", "getEchartsInstance", "ref", "option", "notMerge", "lazyUpdate", "style", "onChartReady", "setFrameHeight", "opts", "ReactDOM", "render", "StrictMode", "document", "getElementById"], "mappings": "4RAoBeA,MARf,SAASA,EAAQC,EAAUC,EAAoBC,GAC7C,OAAOC,oBAAUH,GAAK,SAAUI,EAAaC,EAAKC,GAChDF,EAAOE,GAAOC,mBAASF,GACnBN,EAAQM,EAAKJ,EAAUC,GACvBD,EAASO,KAAKN,EAASG,EAAKC,EAAKN,OCoH1BS,eAjGM,SAACC,GACpB,IAIuBC,EAJjBC,EAAoBC,iBAAqB,MACzCC,EAAqBD,mBAgBrBE,EAAuB,SAACC,GAC5B,IAGIC,EAHU,IAAIC,OAAJ,UAhBO,eAgBP,mCAhBO,iBAmBDC,KAAKH,GACzB,GAAIC,EAAO,CACT,IAAMG,EAAUH,EAAM,GACtB,OAAO,IAAII,SAAS,UAAYD,EAAzB,GAEP,OAAOJ,GA3BmC,EAiD9BN,EAAMY,KAPpBC,EA1C4C,EA0C5CA,QACAC,EA3C4C,EA2C5CA,MACAC,EA5C4C,EA4C5CA,SACAC,EA7C4C,EA6C5CA,OACAC,EA9C4C,EA8C5CA,MACAC,EA/C4C,EA+C5CA,SACAC,EAhD4C,EAgD5CA,IAEIC,GA7CiBnB,EA6CUa,EA3C3BjB,mBAASI,IACXoB,gBAFsB,eAEiBpB,GAElCJ,mBAASI,GAJQ,eAIuBA,GA0C7CJ,mBAASsB,IACXE,cAAoBF,EAAIG,QAASH,EAAII,QAASJ,EAAIK,cAIpD,IAAMC,EAnBGpC,EAmBwCwB,EAnB3BR,EAAsB,IAoBtCqB,EAAqB,GAoB3B,OAnBAC,OAAOC,KAAKb,GAAUI,KAAI,SAACvB,GACzB,IAAMiC,EAAgBd,EAASnB,GAC/B8B,EAAc9B,GAAOkC,uBACnB,SAACC,GACC,IAAMzB,EAAID,EAAqBwB,EAArBxB,CAAoC0B,GAC9CC,IAAUC,kBAAkB3B,KAE9B,CAACuB,OAILK,qBAAU,WACJ,OAAShC,EAAkBiC,UAI/B/B,EAAmB+B,QAAUjC,EAAkBiC,QAAQC,yBAIvD,oCACE,kBAAC,IAAD,CACEC,IAAKnC,EACLoC,OAAQb,EACRc,UAAU,EACVC,YAAY,EACZC,MAAO,CAAEzB,OAAQA,EAAQC,MAAOA,GAChCH,MAAOM,EACPsB,aAAc,WACZV,IAAUW,kBAEZ5B,SAAUW,EACVkB,KAAM,CAAE1B,SAAUA,S,OCxH1B2B,IAASC,OACP,kBAAC,IAAMC,WAAP,KACE,kBAAC,EAAD,OAEFC,SAASC,eAAe,W", "file": "static/js/main.2e4d195b.chunk.js", "sourcesContent": ["/**\r\n * https://stackoverflow.com/questions/25333918/js-deep-map-function\r\n */\r\nimport { isObject, transform } from \"lodash\"\r\n\r\n/**\r\n * Run function through every nested value of an object\r\n * @param obj object\r\n * @param iterator in our case, very certainly evaluate string to function\r\n * @param context initial value\r\n * @returns object with all value passed through function\r\n */\r\nfunction deepMap(obj: any, iterator: Function, context: any) {\r\n  return transform(obj, function (result: any, val, key) {\r\n    result[key] = isObject(val)\r\n      ? deepMap(val, iterator, context)\r\n      : iterator.call(context, val, key, obj)\r\n  })\r\n}\r\n\r\nexport default deepMap\r\n", "import React, { useCallback, useEffect, useRef } from \"react\"\r\nimport {\r\n  ComponentProps,\r\n  Streamlit,\r\n  withStreamlitConnection,\r\n} from \"streamlit-component-lib\"\r\nimport { isObject, mapValues } from \"lodash\"\r\n\r\nimport * as echarts from \"echarts\"\r\nimport \"echarts-gl\"\r\nimport \"echarts-liquidfill\"\r\nimport \"echarts-wordcloud\"\r\nimport ReactEcharts, { EChartsOption } from \"echarts-for-react\"\r\n\r\nimport deepMap from \"./utils\"\r\n\r\ninterface Map {\r\n  mapName: string\r\n  geoJson: object\r\n  specialAreas: object\r\n}\r\n\r\n/**\r\n * Arguments Streamlit receives from the Python side\r\n */\r\ninterface PythonArgs {\r\n  options: EChartsOption\r\n  theme: string | object\r\n  onEvents: any\r\n  height: string\r\n  width: string\r\n  renderer: \"canvas\" | \"svg\"\r\n  map: Map\r\n}\r\n\r\nconst EchartsChart = (props: ComponentProps) => {\r\n  const echartsElementRef = useRef<ReactEcharts>(null)\r\n  const echartsInstanceRef = useRef()\r\n  const JS_PLACEHOLDER = \"--x_x--0_0--\"\r\n\r\n  const registerTheme = (themeProp: string | object) => {\r\n    const customThemeName = \"custom_theme\"\r\n    if (isObject(themeProp)) {\r\n      echarts.registerTheme(customThemeName, themeProp)\r\n    }\r\n    return isObject(themeProp) ? customThemeName : themeProp\r\n  }\r\n\r\n  /**\r\n   * If string can be evaluated as a Function, return activated function. Else return string.\r\n   * @param s string to evaluate to function\r\n   * @returns Function if can be evaluated as one, else input string\r\n   */\r\n  const evalStringToFunction = (s: string) => {\r\n    let funcReg = new RegExp(\r\n      `${JS_PLACEHOLDER}\\\\s*(function\\\\s*.*)\\\\s*${JS_PLACEHOLDER}`\r\n    )\r\n    let match = funcReg.exec(s)\r\n    if (match) {\r\n      const funcStr = match[1]\r\n      return new Function(\"return \" + funcStr)()\r\n    } else {\r\n      return s\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deep map all values in an object to evaluate all strings as functions\r\n   * We use this to look in all nested values of options for Pyecharts Javascript placeholder\r\n   * @param obj object to deep map on\r\n   * @returns object with all functions in values evaluated\r\n   */\r\n  const evalStringToFunctionDeepMap = (obj: object) => {\r\n    return deepMap(obj, evalStringToFunction, {})\r\n  }\r\n\r\n  const {\r\n    options,\r\n    theme,\r\n    onEvents,\r\n    height,\r\n    width,\r\n    renderer,\r\n    map,\r\n  }: PythonArgs = props.args\r\n  const cleanTheme = registerTheme(theme)\r\n\r\n  if (isObject(map)) {\r\n    echarts.registerMap(map.mapName, map.geoJson, map.specialAreas)\r\n  }\r\n\r\n  // no need for memo, react-echarts uses fast-deep-equal to compare option/event change and update on change\r\n  const cleanOptions = evalStringToFunctionDeepMap(options)\r\n  const cleanOnEvents: any = {}\r\n  Object.keys(onEvents).map((key: string) => {\r\n    const eventFunction = onEvents[key]\r\n    cleanOnEvents[key] = useCallback(\r\n      (params: any) => {\r\n        const s = evalStringToFunction(eventFunction)(params)\r\n        Streamlit.setComponentValue(s)\r\n      },\r\n      [eventFunction]\r\n    )\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (null === echartsElementRef.current) {\r\n      return\r\n    }\r\n\r\n    echartsInstanceRef.current = echartsElementRef.current.getEchartsInstance()\r\n  })\r\n\r\n  return (\r\n    <>\r\n      <ReactEcharts\r\n        ref={echartsElementRef}\r\n        option={cleanOptions}\r\n        notMerge={true}\r\n        lazyUpdate={true}\r\n        style={{ height: height, width: width }}\r\n        theme={cleanTheme}\r\n        onChartReady={() => {\r\n          Streamlit.setFrameHeight()\r\n        }}\r\n        onEvents={cleanOnEvents}\r\n        opts={{ renderer: renderer }}\r\n      />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default withStreamlitConnection(EchartsChart)\r\n", "import React from \"react\"\r\nimport ReactDOM from \"react-dom\"\r\nimport EchartsChart from \"./EchartsChart\"\r\n\r\nimport \"./index.css\"\r\n\r\nReactDOM.render(\r\n  <React.StrictMode>\r\n    <EchartsChart />\r\n  </React.StrictMode>,\r\n  document.getElementById(\"root\")\r\n)\r\n"], "sourceRoot": ""}