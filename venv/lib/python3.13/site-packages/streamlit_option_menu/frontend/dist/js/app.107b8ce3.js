(function(e){function t(t){for(var r,s,i=t[0],a=t[1],l=t[2],b=0,j=[];b<i.length;b++)s=i[b],Object.prototype.hasOwnProperty.call(o,s)&&o[s]&&j.push(o[s][0]),o[s]=0;for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r]);u&&u(t);while(j.length)j.shift()();return c.push.apply(c,l||[]),n()}function n(){for(var e,t=0;t<c.length;t++){for(var n=c[t],r=!0,i=1;i<n.length;i++){var a=n[i];0!==o[a]&&(r=!1)}r&&(c.splice(t--,1),e=s(s.s=n[0]))}return e}var r={},o={app:0},c=[];function s(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.m=e,s.c=r,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)s.d(n,r,function(t){return e[t]}.bind(null,r));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="";var i=window["webpackJsonp"]=window["webpackJsonp"]||[],a=i.push.bind(i);i.push=t,i=i.slice();for(var l=0;l<i.length;l++)t(i[l]);var u=a;c.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"04d9":function(e,t,n){"use strict";n("7746")},"09d0":function(e,t,n){"use strict";n("f6f8")},"44dc":function(e,t,n){"use strict";n("a61a")},7746:function(e,t,n){},a61a:function(e,t,n){},cd49:function(e,t,n){"use strict";n.r(t);var r=n("7a23");const o={id:"app"};function c(e,t,n,c,s,i){const a=Object(r["s"])("MyComponent"),l=Object(r["s"])("WithStreamlitConnection");return Object(r["o"])(),Object(r["d"])("div",o,[Object(r["g"])(l,null,{default:Object(r["v"])(({args:e})=>[Object(r["g"])(a,{args:e},null,8,["args"])]),_:1})])}const s={class:"menu"},i=["onClick"];function a(e,t,n,o,c,a){return Object(r["o"])(),Object(r["d"])("div",s,[Object(r["e"])("div",{class:Object(r["i"])(["container-xxl d-flex flex-column flex-shrink-0",{"p-3":!o.isHorizontal,"p-h":o.isHorizontal,"nav-justified":o.isHorizontal}]),style:Object(r["j"])(o.styleObjectToString(o.styles["container"]))},[o.menuTitle?(Object(r["o"])(),Object(r["d"])(r["a"],{key:0},[Object(r["e"])("a",{href:"#",class:"menu-title align-items-center mb-md-0 me-md-auto text-decoration-none",style:Object(r["j"])(o.styleObjectToString(o.styles["menu-title"]))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",o.menuIcon]),style:Object(r["j"])(o.styleObjectToString(o.styles["menu-icon"]))},null,6),Object(r["f"])(" "+Object(r["t"])(o.menuTitle),1)],4),t[0]||(t[0]=Object(r["e"])("hr",null,null,-1))],64)):Object(r["c"])("",!0),Object(r["e"])("ul",{class:Object(r["i"])(["nav nav-pills mb-auto",{"flex-column":!o.isHorizontal,"nav-justified":o.isHorizontal}]),style:Object(r["j"])(o.styleObjectToString(o.styles["nav"]))},[(Object(r["o"])(!0),Object(r["d"])(r["a"],null,Object(r["q"])(n.args.options,(e,t)=>(Object(r["o"])(),Object(r["d"])("li",{class:"nav-item",key:e,style:Object(r["j"])(o.styleObjectToString(o.styles["nav-item"]))},["---"===e?(Object(r["o"])(),Object(r["d"])("hr",{key:0,class:Object(r["i"])({vr:o.isHorizontal}),style:Object(r["j"])(o.styleObjectToString(o.styles["separator"]))},null,6)):(Object(r["o"])(),Object(r["d"])("a",{key:1,href:"javascript:void(0);",class:Object(r["i"])(["nav-link",{active:t==o.selectedIndex,"nav-link-horizontal":o.isHorizontal}]),onClick:n=>o.onClicked(t,e),"aria-current":"page",style:Object(r["j"])(o.styleObjectToString(o.styles["nav-link"])+o.styleObjectToString(o.styles["nav-link-selected"],t==o.selectedIndex))},[Object(r["e"])("i",{class:Object(r["i"])(["icon",o.icons[t]]),style:Object(r["j"])(o.styleObjectToString(o.styles["icon"]))},null,6),Object(r["f"])(" "+Object(r["t"])(e),1)],14,i))],4))),128))],6)],6)])}var l=n("d092");function u(){Object(r["l"])(()=>{l["a"].setFrameHeight()}),Object(r["n"])(()=>{l["a"].setFrameHeight()})}
/**
 * @license
 * Copyright 2018-2020 Streamlit Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */n("ab8b"),n("cd74");function b(e){return"bi-"!==e.slice(0,3)?"bi-"+e:e}var j={name:"MyComponent",props:["args"],setup(e){u();const t=Object(r["p"])(e.args.menuTitle),n="horizontal"==e.args.orientation,o=Object(r["p"])(e.args.menuIcon||"bi-menu-up");o.value=b(o.value);const c=Object(r["p"])(e.args.icons||[]);for(let r=0;r<e.args.options.length;r++)c.value[r]||(c.value[r]="bi-caret-right"),c.value[r]=b(c.value[r]);const s=Object(r["p"])(e.args.defaultIndex),i=()=>{for(let t=0;t<e.args.options.length;t++)c.value[t]||(c.value[t]="bi-caret-right"),c.value[t]=b(c.value[t])};i();const a=(e,t)=>{s.value=e,l["a"].setComponentValue(t)},j=(e,t)=>{if("undefined"===typeof t&&(t=!0),!t)return"";let n="";for(const r in e)n+=`${r}:${e[r]};`;return n},d=Object(r["p"])(e.args.styles||{}),O=t=>{console.log("chosen index is: ",t),t>=0&&t<e.args.options.length?a(t,e.args.options[t]):console.warn("Invalid index for triggerMenuClick")};return Object(r["u"])(()=>e.args.icons,()=>{c.value=e.args.icons||[],i()}),Object(r["u"])(()=>e.args.manualSelect,(t,n)=>{void 0!==t&&null!==t&&t!==n&&a(t,e.args.options[t])}),{triggerMenuClick:O,selectedIndex:s,menuTitle:t,menuIcon:o,icons:c,styles:d,onClicked:a,styleObjectToString:j,isHorizontal:n}}},d=(n("09d0"),n("6b0d")),O=n.n(d);const p=O()(j,[["render",a],["__scopeId","data-v-5af006b8"]]);var f=p;const v={key:0},g={class:"err__msg"};function y(e,t,n,o,c,s){return Object(r["o"])(),Object(r["d"])("div",null,[""!=e.componentError?(Object(r["o"])(),Object(r["d"])("div",v,[t[0]||(t[0]=Object(r["e"])("h1",{class:"err__title"},"Component Error",-1)),Object(r["e"])("div",g,"Message: "+Object(r["t"])(e.componentError),1)])):null!=e.renderData?Object(r["r"])(e.$slots,"default",{key:1,args:e.renderData.args,disabled:e.renderData.disabled},void 0,!0):Object(r["c"])("",!0)])}var m=Object(r["h"])({name:"WithStreamlitConnection",setup(){const e=Object(r["p"])(void 0),t=Object(r["p"])(""),n=n=>{const r=n;e.value=r.detail,t.value=""};return Object(r["l"])(()=>{l["a"].events.addEventListener(l["a"].RENDER_EVENT,n),l["a"].setComponentReady()}),Object(r["n"])(()=>{""!=t.value&&l["a"].setFrameHeight()}),Object(r["m"])(()=>{l["a"].events.removeEventListener(l["a"].RENDER_EVENT,n)}),Object(r["k"])(e=>{t.value=String(e)}),{renderData:e,componentError:t}}});n("44dc");const h=O()(m,[["render",y],["__scopeId","data-v-bef81972"]]);var k=h,S=Object(r["h"])({name:"App",components:{MyComponent:f,WithStreamlitConnection:k}});n("04d9");const x=O()(S,[["render",c]]);var T=x;Object(r["b"])(T).mount("#app")},f6f8:function(e,t,n){}});
//# sourceMappingURL=app.107b8ce3.js.map