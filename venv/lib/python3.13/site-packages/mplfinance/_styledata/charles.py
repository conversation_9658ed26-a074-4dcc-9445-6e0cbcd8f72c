style = dict(style_name    = 'charles',
             base_mpl_style= 'fast', 
             marketcolors  = {'candle'  : {'up':'#006340', 'down':'#a02128'},
                              'edge'    : {'up':'#006340', 'down':'#a02128'},
                              'wick'    : {'up':'#006340', 'down':'#a02128'},
                              'ohlc'    : {'up':'#006340', 'down':'#a02128'},
                              'volume'  : {'up':'#007a00', 'down':'#d50d18'},
                              'vcdopcod': True, # Volume Color Depends On Price Change On Day
                              'alpha'   : 1.0,
                             },
             mavcolors     = ['#ef5714','#ef5714','#9f4878','#9f4878'],
             y_on_right    = True,
             gridcolor     = '#a0a0a0',
             gridstyle     = '--',
             facecolor     = 'w',
             rc            = [ ('axes.edgecolor'  , 'white'   ),
                               ('axes.linewidth'  ,  1.5      ),
                               ('axes.labelsize'  , 'large'   ),
                               ('axes.labelweight', 'semibold'),
                               ('axes.grid'       ,  True     ),
                               ('axes.grid.axis'  ,  'y'      ),
                               ('grid.linewidth'  ,  0.4      ),
                               ('lines.linewidth' ,  2.0      ),
                               ('font.weight'     , 'medium'  ),
                               ('font.size'       ,  10.0     ),
                               ('figure.titlesize', 'x-large' ),
                               ('figure.titleweight','semibold'),
                             ],
             base_mpf_style= 'charles'
            )
