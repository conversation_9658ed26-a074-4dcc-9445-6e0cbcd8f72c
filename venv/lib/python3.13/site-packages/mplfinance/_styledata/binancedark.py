style = dict(style_name    = 'binancedark',
             base_mpl_style= 'dark_background',
             marketcolors  = {'candle'   : {'up': '#3dc985', 'down': '#ef4f60'},  
                              'edge'     : {'up': '#3dc985', 'down': '#ef4f60'},  
                              'wick'     : {'up': '#3dc985', 'down': '#ef4f60'},  
                              'ohlc'     : {'up': 'green', 'down': 'red'},
                              'volume'   : {'up': '#247252', 'down': '#82333f'},  
                              'vcedge'   : {'up': '#247252', 'down': '#82333f'},  
                              'vcdopcod' : False,
                              'alpha'    : 1.0,
                             },
             mavcolors     = ['#ffc201','#ff10ff','#cd0468','#1f77b4',
                              '#ff7f0e','#2ca02c','#40e0d0'],
             y_on_right    = True,
             gridcolor     = None,
             gridstyle     = '--',
             facecolor     =  None,
             rc            = [ ('axes.grid','True'),
                               ('axes.grid.axis' , 'y'),
                               ('axes.edgecolor'  , '#474d56' ),
                               ('axes.titlecolor','red'),
                               ('figure.titlesize', 'x-large' ),
                               ('figure.titleweight','semibold'),
                               ('figure.facecolor', '#0a0a0a' ),
                             ],
             base_mpf_style= 'binancedark'
            )
