{"layout": {"xaxis": {"title": {"standoff": 15}}, "yaxis": {"title": {"standoff": 15}}, "font": {"size": 18}}, "data": {"scatter": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scatter"}], "scattergl": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scattergl"}], "scatter3d": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scatter3d"}], "scatterpolar": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scatterpolar"}], "scatterpolargl": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scatterpolargl"}], "scatterternary": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scatterternary"}], "scattergeo": [{"line": {"width": 3}, "marker": {"size": 9}, "type": "scattergeo"}], "table": [{"cells": {"height": 30}, "header": {"height": 36}, "type": "table"}], "pie": [{"automargin": true, "type": "pie"}]}}