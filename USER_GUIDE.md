# 📖 股票数据分析系统 - 用户使用指南

## 🚀 快速开始

### 1. 启动系统
```bash
# 方式一：一键启动（推荐）
./start.sh

# 方式二：分步启动
./scripts/start_backend.sh    # 启动后端API
./scripts/start_frontend.sh   # 启动前端界面
```

### 2. 访问界面
- **前端界面**: http://localhost:8501
- **API文档**: http://localhost:8000/docs

## 📋 功能使用指南

### 🏠 主界面导航

系统提供4个主要功能页面：
1. **数据管理** - 股票数据的查看和管理
2. **特征工程** - K线特征提取和索引构建
3. **相似性搜索** - 走势相似性分析
4. **系统状态** - 系统监控和统计

### 📥 数据管理页面

#### 股票列表标签
- **查看股票**: 浏览系统中的股票列表
- **市场筛选**: 按SH/SZ市场筛选股票
- **刷新列表**: 更新股票信息

#### 数据拉取标签
- **单只股票拉取**: 
  - 输入股票代码（如：sh600519）
  - 选择日期范围
  - 点击"拉取股票数据"
- **批量操作**: 拉取所有股票列表

#### 数据查看标签
- **K线图表**: 查看股票的K线走势图
- **数据表格**: 查看详细的历史数据
- **日期筛选**: 指定查看的时间范围

### 📐 特征工程页面

#### 特征提取标签
- **股票代码**: 输入要分析的股票代码
- **窗口大小**: 设置K线片段长度（3-20天）
- **特征标准化**: 是否对特征进行标准化处理
- **日期范围**: 指定特征提取的时间范围

**操作步骤**：
1. 输入股票代码（如：sh600519）
2. 调整窗口大小（推荐5天）
3. 选择日期范围
4. 点击"提取特征"
5. 查看提取结果

#### 索引管理标签
- **构建新索引**:
  - 设置索引名称
  - 选择窗口大小
  - 输入股票代码列表（每行一个）
  - 点击"构建索引"

- **现有索引**:
  - 查看已构建的索引信息
  - 删除不需要的索引

### 🔍 相似性搜索页面

#### 走势搜索标签
**查询参数设置**：
- **查询股票代码**: 要分析的股票
- **查询日期范围**: 指定K线片段的时间范围
- **搜索窗口大小**: 匹配的K线长度
- **返回结果数量**: 显示多少个相似结果
- **使用索引**: 选择要使用的FAISS索引

**操作步骤**：
1. 输入查询股票代码（如：sh600519）
2. 选择查询的日期范围（建议10-30天的片段）
3. 设置搜索参数
4. 点击"开始搜索"
5. 查看搜索结果和相似度评分

#### 搜索历史标签
- **历史记录**: 查看之前的搜索记录
- **结果回顾**: 重新查看历史搜索结果

### ⚙️ 系统状态页面

#### API服务状态
- **健康检查**: 检查后端API服务状态
- **数据库连接**: 验证数据库连接

#### 数据统计
- **股票数量**: 系统中的股票总数
- **数据量统计**: 日线数据和特征向量数量
- **最新数据日期**: 数据的时效性

## 💡 使用技巧

### 🎯 最佳实践

#### 特征提取
- **窗口大小选择**: 
  - 短期模式：3-5天（适合短线分析）
  - 中期模式：5-10天（适合中线分析）
  - 长期模式：10-20天（适合长线分析）

#### 相似性搜索
- **查询片段选择**: 选择有明显特征的K线片段
- **日期范围**: 建议选择10-30天的连续交易日
- **结果数量**: 通常5-10个结果足够分析

#### 索引管理
- **索引命名**: 使用有意义的名称（如：bank_stocks_5d）
- **股票组合**: 按行业或主题组织股票
- **定期更新**: 随着数据增加，重建索引

### 🔧 故障排除

#### 常见问题

**1. 前端无法访问**
- 检查后端服务是否启动
- 确认端口8000和8501未被占用
- 查看终端错误信息

**2. 数据拉取失败**
- 检查网络连接
- 确认股票代码格式正确
- 尝试使用示例数据

**3. 搜索结果为空**
- 确认索引已构建
- 检查查询日期范围是否有数据
- 验证股票代码是否存在

**4. 特征提取失败**
- 确认股票有足够的历史数据
- 检查日期范围设置
- 调整窗口大小

#### 解决方案

**重启服务**：
```bash
# 停止所有服务（Ctrl+C）
# 重新启动
./start.sh
```

**检查系统状态**：
```bash
python scripts/test_system.py
```

**重建示例数据**：
```bash
python scripts/add_sample_data.py
```

## 📊 示例操作流程

### 完整分析流程

#### 步骤1：数据准备
1. 访问"数据管理"页面
2. 查看股票列表，确认有数据
3. 如需要，拉取更多股票数据

#### 步骤2：特征提取
1. 访问"特征工程"页面
2. 选择目标股票（如：sh600519）
3. 设置窗口大小为5天
4. 选择最近3个月的数据
5. 点击"提取特征"

#### 步骤3：构建索引
1. 在"索引管理"标签
2. 输入索引名称：my_analysis
3. 添加股票代码列表：
   ```
   sh600519
   sz000001
   sh600036
   ```
4. 点击"构建索引"

#### 步骤4：相似性搜索
1. 访问"相似性搜索"页面
2. 输入查询股票：sh600519
3. 选择查询日期：最近20天的某个片段
4. 设置返回结果：10个
5. 选择索引：my_analysis
6. 点击"开始搜索"

#### 步骤5：结果分析
1. 查看相似度评分
2. 对比K线图形
3. 分析相似走势的特点
4. 保存有价值的发现

## 🎓 高级功能

### API直接调用
```python
import requests

# 获取股票数据
response = requests.get("http://localhost:8000/api/v1/data/stocks/sh600519")

# 提取特征
data = {
    "stock_code": "sh600519",
    "window_size": 5,
    "start_date": "2024-01-01",
    "end_date": "2024-02-01"
}
response = requests.post("http://localhost:8000/api/v1/features/extract", json=data)
```

### 自定义分析
- 修改特征提取算法
- 调整相似度计算方法
- 扩展数据源接口

## 📞 技术支持

### 系统要求
- Python 3.8+
- MySQL 8.0+
- 8GB+ RAM（推荐）
- 网络连接（数据拉取）

### 性能优化
- 定期清理历史数据
- 优化索引大小
- 监控内存使用

### 数据备份
- 定期备份MySQL数据库
- 保存重要的FAISS索引文件
- 备份配置文件

---

**🎉 祝你使用愉快！如有问题，请查看API文档或系统日志。**
