#!/usr/bin/env python3
"""
系统功能测试脚本
测试股票数据分析系统的各项功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import requests
import time
import json
from datetime import datetime, date, timedelta

API_BASE_URL = "http://localhost:8000/api/v1"

def test_api_health():
    """测试API健康状态"""
    print("🔍 测试API健康状态...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "healthy":
                print("✅ API服务健康")
                return True
            else:
                print(f"❌ API服务不健康: {result}")
                return False
        else:
            print(f"❌ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接API服务: {e}")
        return False

def test_data_stats():
    """测试数据统计"""
    print("\n📊 测试数据统计...")
    try:
        response = requests.get(f"{API_BASE_URL}/data/stats")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                stats = result["stats"]
                print(f"✅ 数据统计获取成功:")
                print(f"   - 股票数量: {stats.get('stock_count', 0)}")
                print(f"   - 日线数据量: {stats.get('daily_data_count', 0)}")
                print(f"   - 特征向量数量: {stats.get('feature_count', 0)}")
                print(f"   - 最新数据日期: {stats.get('latest_date', 'N/A')}")
                return True
            else:
                print("❌ 数据统计获取失败")
                return False
        else:
            print(f"❌ 数据统计API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据统计测试异常: {e}")
        return False

def test_stock_list():
    """测试股票列表"""
    print("\n📋 测试股票列表...")
    try:
        response = requests.get(f"{API_BASE_URL}/data/stocks")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                stocks = result["data"]
                print(f"✅ 股票列表获取成功，共 {len(stocks)} 只股票:")
                for stock in stocks[:3]:  # 只显示前3只
                    print(f"   - {stock['code']} ({stock['name']}) - {stock['market']}")
                return stocks
            else:
                print("❌ 股票列表获取失败")
                return []
        else:
            print(f"❌ 股票列表API请求失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 股票列表测试异常: {e}")
        return []

def test_feature_extraction():
    """测试特征提取"""
    print("\n📐 测试特征提取...")
    try:
        # 使用贵州茅台进行测试
        data = {
            "stock_code": "sh600519",
            "window_size": 5,
            "start_date": (date.today() - timedelta(days=60)).isoformat(),
            "end_date": date.today().isoformat(),
            "normalize": True
        }
        
        response = requests.post(f"{API_BASE_URL}/features/extract", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 特征提取成功:")
            print(f"   - 股票代码: {result.get('stock_code')}")
            print(f"   - 窗口大小: {result.get('window_size')}")
            print(f"   - 特征数量: {result.get('feature_count')}")
            print(f"   - 特征维度: {result.get('feature_dimension')}")
            return True
        else:
            print(f"❌ 特征提取失败: {response.status_code}")
            if response.text:
                print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 特征提取测试异常: {e}")
        return False

def test_index_building():
    """测试索引构建"""
    print("\n⚙️ 测试FAISS索引构建...")
    try:
        # 使用几只示例股票构建索引
        stock_codes = ["sh600519", "sz000001", "sh600036"]
        params = "?window_size=5&index_name=test_index"
        
        response = requests.post(f"{API_BASE_URL}/features/build-index{params}", json=stock_codes)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ 索引构建成功:")
                print(f"   - 索引名称: {result.get('index_name')}")
                print(f"   - 向量数量: {result.get('total_vectors')}")
                print(f"   - 特征维度: {result.get('feature_dimension')}")
                print(f"   - 股票数量: {result.get('stock_count')}")
                return True
            else:
                print("❌ 索引构建失败")
                return False
        else:
            print(f"❌ 索引构建API请求失败: {response.status_code}")
            if response.text:
                print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 索引构建测试异常: {e}")
        return False

def test_similarity_search():
    """测试相似性搜索"""
    print("\n🔍 测试相似性搜索...")
    try:
        # 搜索贵州茅台的相似走势
        data = {
            "query_stock_code": "sh600519",
            "query_start_date": (date.today() - timedelta(days=30)).isoformat(),
            "query_end_date": (date.today() - timedelta(days=20)).isoformat(),
            "window_size": 5,
            "top_k": 3,
            "index_name": "test_index"
        }
        
        response = requests.post(f"{API_BASE_URL}/search/similar", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 相似性搜索成功:")
            print(f"   - 查询ID: {result.get('query_id')}")
            print(f"   - 结果数量: {result.get('total_results')}")
            
            # 显示前几个结果
            results = result.get('results', [])
            for i, match in enumerate(results[:3]):
                print(f"   - #{i+1} {match.get('stock_code')} (相似度: {match.get('similarity_score', 0):.4f})")
            
            return True
        else:
            print(f"❌ 相似性搜索失败: {response.status_code}")
            if response.text:
                print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 相似性搜索测试异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🧪 股票数据分析系统功能测试")
    print("=" * 50)
    
    # 测试结果统计
    tests = []
    
    # 1. API健康检查
    tests.append(("API健康检查", test_api_health()))
    
    # 2. 数据统计
    tests.append(("数据统计", test_data_stats()))
    
    # 3. 股票列表
    stocks = test_stock_list()
    tests.append(("股票列表", len(stocks) > 0))
    
    # 4. 特征提取
    tests.append(("特征提取", test_feature_extraction()))
    
    # 5. 索引构建
    tests.append(("索引构建", test_index_building()))
    
    # 6. 相似性搜索
    tests.append(("相似性搜索", test_similarity_search()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！系统运行正常。")
        print("\n💡 你现在可以:")
        print("   1. 访问前端界面: http://localhost:8501")
        print("   2. 查看API文档: http://localhost:8000/docs")
        print("   3. 使用系统进行股票走势分析")
    else:
        print("⚠️  部分功能测试失败，请检查系统状态。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
