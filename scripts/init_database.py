#!/usr/bin/env python3
"""
数据库初始化脚本
创建股票数据分析系统所需的所有数据库表
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import create_engine, text
from shared.config.db import get_database_url

def create_tables():
    """创建所有必需的数据库表"""
    
    engine = create_engine(get_database_url())
    
    # 1. 股票基础信息表
    stock_info_sql = """
    CREATE TABLE IF NOT EXISTS stock_info (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
        name VARCHAR(100) NOT NULL COMMENT '股票名称',
        market VARCHAR(10) NOT NULL COMMENT '市场(SH/SZ)',
        industry VARCHAR(100) COMMENT '行业分类',
        list_date DATE COMMENT '上市日期',
        status ENUM('active', 'delisted', 'suspended') DEFAULT 'active' COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_code (code),
        INDEX idx_market (market),
        INDEX idx_industry (industry)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基础信息表';
    """
    
    # 2. 股票日线数据表（优化现有表结构）
    stock_daily_sql = """
    CREATE TABLE IF NOT EXISTS stock_daily (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) NOT NULL COMMENT '股票代码',
        日期 DATE NOT NULL COMMENT '交易日期',
        开盘 DECIMAL(10,3) NOT NULL COMMENT '开盘价',
        收盘 DECIMAL(10,3) NOT NULL COMMENT '收盘价',
        最高 DECIMAL(10,3) NOT NULL COMMENT '最高价',
        最低 DECIMAL(10,3) NOT NULL COMMENT '最低价',
        成交量 BIGINT NOT NULL COMMENT '成交量',
        成交额 DECIMAL(15,2) COMMENT '成交额',
        振幅 DECIMAL(8,4) COMMENT '振幅',
        涨跌幅 DECIMAL(8,4) COMMENT '涨跌幅',
        涨跌额 DECIMAL(10,3) COMMENT '涨跌额',
        换手率 DECIMAL(8,4) COMMENT '换手率',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY uk_code_date (code, 日期),
        INDEX idx_code (code),
        INDEX idx_date (日期),
        INDEX idx_code_date (code, 日期)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线数据表';
    """
    
    # 3. K线特征向量表
    kline_features_sql = """
    CREATE TABLE IF NOT EXISTS kline_features (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) NOT NULL COMMENT '股票代码',
        start_date DATE NOT NULL COMMENT '片段起始日期',
        end_date DATE NOT NULL COMMENT '片段结束日期',
        window_size INT NOT NULL COMMENT '窗口大小',
        feature_vector JSON NOT NULL COMMENT '特征向量(JSON格式)',
        feature_hash VARCHAR(64) NOT NULL COMMENT '特征向量哈希值',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY uk_code_start_window (code, start_date, window_size),
        INDEX idx_code (code),
        INDEX idx_start_date (start_date),
        INDEX idx_window_size (window_size),
        INDEX idx_feature_hash (feature_hash)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K线特征向量表';
    """
    
    # 4. FAISS索引元数据表
    faiss_index_sql = """
    CREATE TABLE IF NOT EXISTS faiss_index_meta (
        id INT AUTO_INCREMENT PRIMARY KEY,
        index_name VARCHAR(100) NOT NULL UNIQUE COMMENT '索引名称',
        window_size INT NOT NULL COMMENT '窗口大小',
        feature_dim INT NOT NULL COMMENT '特征维度',
        total_vectors INT NOT NULL COMMENT '向量总数',
        index_type VARCHAR(50) NOT NULL COMMENT '索引类型',
        index_path VARCHAR(500) NOT NULL COMMENT '索引文件路径',
        build_time TIMESTAMP NOT NULL COMMENT '构建时间',
        status ENUM('building', 'ready', 'error') DEFAULT 'building' COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_window_size (window_size),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='FAISS索引元数据表';
    """
    
    # 5. 查询历史表
    query_history_sql = """
    CREATE TABLE IF NOT EXISTS query_history (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        query_id VARCHAR(36) NOT NULL UNIQUE COMMENT '查询ID',
        query_code VARCHAR(20) NOT NULL COMMENT '查询股票代码',
        query_start_date DATE NOT NULL COMMENT '查询起始日期',
        query_end_date DATE NOT NULL COMMENT '查询结束日期',
        window_size INT NOT NULL COMMENT '窗口大小',
        top_k INT NOT NULL COMMENT '返回结果数量',
        query_vector JSON NOT NULL COMMENT '查询向量',
        results JSON NOT NULL COMMENT '查询结果',
        query_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
        INDEX idx_query_code (query_code),
        INDEX idx_query_time (query_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询历史表';
    """
    
    # 6. 系统配置表
    system_config_sql = """
    CREATE TABLE IF NOT EXISTS system_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
        config_value TEXT NOT NULL COMMENT '配置值',
        config_type ENUM('string', 'int', 'float', 'bool', 'json') DEFAULT 'string' COMMENT '配置类型',
        description TEXT COMMENT '配置描述',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
    """
    
    # 执行建表语句
    tables = [
        ("stock_info", stock_info_sql),
        ("stock_daily", stock_daily_sql),
        ("kline_features", kline_features_sql),
        ("faiss_index_meta", faiss_index_sql),
        ("query_history", query_history_sql),
        ("system_config", system_config_sql)
    ]
    
    with engine.connect() as conn:
        for table_name, sql in tables:
            try:
                conn.execute(text(sql))
                conn.commit()
                print(f"✅ 表 {table_name} 创建成功")
            except Exception as e:
                print(f"❌ 表 {table_name} 创建失败: {e}")
    
    print("\n🎉 数据库初始化完成！")

if __name__ == "__main__":
    create_tables()
