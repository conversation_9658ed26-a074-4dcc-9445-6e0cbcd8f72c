#!/usr/bin/env python3
"""
最终演示脚本 - 展示股票数据分析系统的完整功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import requests
import time
import json
from datetime import date, timedelta

API_BASE_URL = "http://localhost:8000/api/v1"

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def demo_system_overview():
    """演示系统概览"""
    print_header("股票数据分析系统 v1.0 - 完整功能演示")
    
    print("🎉 欢迎使用股票数据分析系统！")
    print("\n📊 系统功能:")
    print("   1. 股票数据拉取与管理")
    print("   2. K线特征建模")
    print("   3. 相似性搜索引擎")
    print("   4. Web服务架构")
    
    print("\n🌐 访问地址:")
    print("   - 前端界面: http://localhost:8501")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 健康检查: http://localhost:8000/health")

def demo_health_check():
    """演示健康检查"""
    print_step(1, "系统健康检查")
    
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API服务状态: {result.get('status')}")
            print(f"✅ 数据库连接: {result.get('database')}")
        else:
            print("❌ 健康检查失败")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    return True

def demo_data_stats():
    """演示数据统计"""
    print_step(2, "数据统计信息")
    
    try:
        response = requests.get(f"{API_BASE_URL}/data/stats")
        if response.status_code == 200:
            result = response.json()
            stats = result["stats"]
            
            print(f"📈 股票数量: {stats.get('stock_count', 0):,}")
            print(f"📊 日线数据量: {stats.get('daily_data_count', 0):,}")
            print(f"🧮 特征向量数量: {stats.get('feature_count', 0):,}")
            print(f"📅 最新数据日期: {stats.get('latest_date', 'N/A')}")
            
            return stats
        else:
            print("❌ 获取统计信息失败")
            return None
    except Exception as e:
        print(f"❌ 统计信息异常: {e}")
        return None

def demo_stock_list():
    """演示股票列表"""
    print_step(3, "股票列表管理")
    
    try:
        response = requests.get(f"{API_BASE_URL}/data/stocks")
        if response.status_code == 200:
            result = response.json()
            stocks = result["data"]
            
            print(f"📋 系统中共有 {len(stocks)} 只股票:")
            for i, stock in enumerate(stocks, 1):
                print(f"   {i}. {stock['code']} - {stock['name']} ({stock['market']})")
            
            return stocks
        else:
            print("❌ 获取股票列表失败")
            return []
    except Exception as e:
        print(f"❌ 股票列表异常: {e}")
        return []

def demo_feature_extraction():
    """演示特征提取"""
    print_step(4, "K线特征提取")
    
    print("🔍 正在提取贵州茅台(sh600519)的K线特征...")
    
    try:
        data = {
            "stock_code": "sh600519",
            "window_size": 5,
            "start_date": "2024-01-01",
            "end_date": "2024-03-01",
            "normalize": True
        }
        
        response = requests.post(f"{API_BASE_URL}/features/extract", json=data)
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 特征提取成功!")
            print(f"   📊 股票代码: {result.get('stock_code')}")
            print(f"   📏 窗口大小: {result.get('window_size')} 天")
            print(f"   🔢 特征数量: {result.get('feature_count')} 个")
            print(f"   📐 特征维度: {result.get('feature_dimension')} 维")
            
            # 显示特征示例
            features = result.get('features', [])
            if features:
                print(f"\n📝 特征示例 (前3个):")
                for i, feature in enumerate(features[:3]):
                    print(f"   {i+1}. 日期: {feature.get('start_date')}, 哈希: {feature.get('feature_hash')[:16]}...")
            
            return result
        else:
            print(f"❌ 特征提取失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 特征提取异常: {e}")
        return None

def demo_index_building():
    """演示索引构建"""
    print_step(5, "FAISS索引构建")
    
    print("⚙️ 正在为多只股票构建FAISS索引...")
    
    try:
        stock_codes = ["sh600519", "sz000001", "sh600036"]
        params = "?window_size=5&index_name=demo_index"
        
        print(f"📋 包含股票: {', '.join(stock_codes)}")
        
        response = requests.post(f"{API_BASE_URL}/features/build-index{params}", json=stock_codes)
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 索引构建成功!")
            print(f"   🏷️  索引名称: {result.get('index_name')}")
            print(f"   🔢 向量数量: {result.get('total_vectors'):,}")
            print(f"   📐 特征维度: {result.get('feature_dimension')}")
            print(f"   📈 股票数量: {result.get('stock_count')}")
            
            return result
        else:
            print(f"❌ 索引构建失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 索引构建异常: {e}")
        return None

def demo_similarity_search():
    """演示相似性搜索"""
    print_step(6, "相似性搜索")
    
    print("🔍 正在搜索与贵州茅台相似的K线走势...")
    
    try:
        search_data = {
            "query_stock_code": "sh600519",
            "query_start_date": "2024-01-15",
            "query_end_date": "2024-01-25",
            "window_size": 5,
            "top_k": 5,
            "index_name": "demo_index"
        }
        
        print(f"📊 查询参数:")
        print(f"   - 查询股票: {search_data['query_stock_code']}")
        print(f"   - 查询日期: {search_data['query_start_date']} 至 {search_data['query_end_date']}")
        print(f"   - 窗口大小: {search_data['window_size']} 天")
        print(f"   - 返回数量: {search_data['top_k']} 个")
        
        response = requests.post(f"{API_BASE_URL}/search/similar", json=search_data)
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n✅ 搜索完成!")
            print(f"   🆔 查询ID: {result.get('query_id')}")
            print(f"   📊 结果数量: {result.get('total_results')}")
            
            results = result.get('results', [])
            if results:
                print(f"\n🎯 相似走势排行榜:")
                for i, match in enumerate(results, 1):
                    similarity = match.get('similarity_score', 0)
                    distance = match.get('distance', 0)
                    print(f"   {i}. {match.get('stock_code')} - 相似度: {similarity:.4f} (距离: {distance:.4f})")
                    print(f"      匹配日期: {match.get('start_date')} 至 {match.get('end_date')}")
            
            return result
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return None

def demo_summary():
    """演示总结"""
    print_header("演示总结")
    
    print("🎉 恭喜！股票数据分析系统演示完成！")
    
    print("\n✅ 已演示功能:")
    print("   1. ✅ 系统健康检查")
    print("   2. ✅ 数据统计信息")
    print("   3. ✅ 股票列表管理")
    print("   4. ✅ K线特征提取")
    print("   5. ✅ FAISS索引构建")
    print("   6. ✅ 相似性搜索")
    
    print("\n🚀 下一步操作:")
    print("   1. 访问前端界面: http://localhost:8501")
    print("   2. 查看API文档: http://localhost:8000/docs")
    print("   3. 探索更多功能和参数")
    print("   4. 根据需要扩展系统功能")
    
    print("\n📚 相关文档:")
    print("   - 📄 FINAL_REPORT.md - 项目完成报告")
    print("   - 📖 USER_GUIDE.md - 用户使用指南")
    print("   - 📋 PROJECT_SUMMARY.md - 项目技术总结")
    
    print("\n🎯 系统特色:")
    print("   - 🚀 高性能: 毫秒级向量检索")
    print("   - 🎨 用户友好: 直观的Web界面")
    print("   - 🔧 可扩展: 模块化架构设计")
    print("   - 📊 完整: 端到端的分析流程")

def main():
    """主演示流程"""
    try:
        # 系统概览
        demo_system_overview()
        time.sleep(2)
        
        # 健康检查
        if not demo_health_check():
            print("❌ 系统健康检查失败，请检查服务状态")
            return
        time.sleep(1)
        
        # 数据统计
        stats = demo_data_stats()
        if not stats:
            print("❌ 无法获取数据统计")
            return
        time.sleep(1)
        
        # 股票列表
        stocks = demo_stock_list()
        if not stocks:
            print("❌ 无法获取股票列表")
            return
        time.sleep(1)
        
        # 特征提取
        features = demo_feature_extraction()
        if not features:
            print("❌ 特征提取失败")
            return
        time.sleep(1)
        
        # 索引构建
        index_result = demo_index_building()
        if not index_result:
            print("❌ 索引构建失败")
            return
        time.sleep(1)
        
        # 相似性搜索
        search_result = demo_similarity_search()
        if not search_result:
            print("❌ 相似性搜索失败")
            return
        time.sleep(1)
        
        # 演示总结
        demo_summary()
        
        print(f"\n🎉 演示成功完成！系统运行正常。")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
