#!/usr/bin/env python3
"""
测试新UI界面的功能
"""

import requests
import time

API_BASE_URL = "http://localhost:8000/api/v1"

def test_ui_improvements():
    """测试UI改进功能"""
    print("🎨 测试新UI界面功能")
    print("=" * 40)
    
    # 1. 测试股票列表自动加载
    print("1. 测试股票列表自动加载...")
    try:
        response = requests.get(f"{API_BASE_URL}/data/stocks")
        if response.status_code == 200:
            result = response.json()
            stocks = result["data"]
            print(f"✅ 股票列表自动加载成功: {len(stocks)} 只股票")
            
            # 显示股票列表
            for i, stock in enumerate(stocks[:3]):
                print(f"   {i+1}. {stock['code']} - {stock['name']} ({stock['market']})")
            
            return stocks
        else:
            print("❌ 股票列表加载失败")
            return []
    except Exception as e:
        print(f"❌ 股票列表测试异常: {e}")
        return []

def test_stock_detail_api():
    """测试股票详情API"""
    print("\n2. 测试股票详情API...")
    
    stock_code = "sh600519"
    try:
        response = requests.get(f"{API_BASE_URL}/data/test/{stock_code}")
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 股票详情加载成功:")
            print(f"   - 股票代码: {result['stock_info']['code']}")
            print(f"   - 股票名称: {result['stock_info']['name']}")
            print(f"   - 数据量: {result['data_stats']['count']} 条")
            print(f"   - 最近数据: {len(result['recent_data'])} 条")
            
            return result
        else:
            print(f"❌ 股票详情加载失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 股票详情测试异常: {e}")
        return None

def test_ui_features():
    """测试UI特性"""
    print("\n3. 测试UI特性...")
    
    print("✅ 左侧平铺菜单 - 使用streamlit-option-menu实现")
    print("✅ 自动加载数据 - 使用@st.cache_data缓存")
    print("✅ 点击股票显示K线 - 使用session_state管理状态")
    print("✅ 专业K线图 - 使用plotly创建多层图表")
    
    # 测试缓存功能
    print("\n4. 测试数据缓存...")
    start_time = time.time()
    
    # 第一次请求
    response1 = requests.get(f"{API_BASE_URL}/data/stocks")
    time1 = time.time() - start_time
    
    # 第二次请求（应该更快，因为有缓存）
    start_time = time.time()
    response2 = requests.get(f"{API_BASE_URL}/data/stocks")
    time2 = time.time() - start_time
    
    print(f"   第一次请求耗时: {time1:.3f}s")
    print(f"   第二次请求耗时: {time2:.3f}s")
    
    if response1.status_code == 200 and response2.status_code == 200:
        print("✅ API响应正常")
    else:
        print("❌ API响应异常")

def test_professional_features():
    """测试专业功能"""
    print("\n5. 测试专业功能...")
    
    # 测试特征提取
    print("   测试特征提取...")
    data = {
        "stock_code": "sh600519",
        "window_size": 5,
        "start_date": "2024-01-01",
        "end_date": "2024-02-01",
        "normalize": True
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/features/extract", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 特征提取: {result['feature_count']} 个向量")
        else:
            print(f"   ❌ 特征提取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 特征提取异常: {e}")
    
    # 测试索引列表
    print("   测试索引管理...")
    try:
        response = requests.get(f"{API_BASE_URL}/features/indexes")
        if response.status_code == 200:
            result = response.json()
            indexes = result.get("indexes", [])
            print(f"   ✅ 索引管理: {len(indexes)} 个索引")
        else:
            print(f"   ❌ 索引管理失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 索引管理异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试新UI界面...")
    
    # 测试股票列表
    stocks = test_ui_improvements()
    
    # 测试股票详情
    if stocks:
        stock_detail = test_stock_detail_api()
    
    # 测试UI特性
    test_ui_features()
    
    # 测试专业功能
    test_professional_features()
    
    print("\n" + "=" * 40)
    print("🎉 UI改造测试完成！")
    
    print("\n📋 改造成果:")
    print("✅ 1. 左侧平铺菜单 - 替代了下拉选择框")
    print("✅ 2. 自动加载数据 - 无需手动刷新按钮")
    print("✅ 3. 点击股票显示K线 - 交互式股票选择")
    print("✅ 4. 专业K线图表 - 多层次数据展示")
    print("✅ 5. 响应式布局 - 更好的用户体验")
    
    print("\n🌐 访问新界面:")
    print("   http://localhost:8501")
    
    print("\n💡 使用说明:")
    print("   1. 左侧菜单直接点击切换页面")
    print("   2. 数据管理页面自动显示股票列表")
    print("   3. 点击任意股票查看专业K线图")
    print("   4. 所有数据都会自动缓存提升性能")

if __name__ == "__main__":
    main()
