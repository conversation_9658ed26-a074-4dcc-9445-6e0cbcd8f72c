-- 股票数据分析系统数据库表结构

-- 1. 股票基础信息表
CREATE TABLE IF NOT EXISTS stock_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
    name VARCHAR(100) NOT NULL COMMENT '股票名称',
    market VARCHAR(10) NOT NULL COMMENT '市场(SH/SZ)',
    industry VARCHAR(100) COMMENT '行业分类',
    list_date DATE COMMENT '上市日期',
    status ENUM('active', 'delisted', 'suspended') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_market (market),
    INDEX idx_industry (industry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基础信息表';

-- 2. K线特征向量表
CREATE TABLE IF NOT EXISTS kline_features (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL COMMENT '股票代码',
    start_date DATE NOT NULL COMMENT '片段起始日期',
    end_date DATE NOT NULL COMMENT '片段结束日期',
    window_size INT NOT NULL COMMENT '窗口大小',
    feature_vector JSON NOT NULL COMMENT '特征向量(JSON格式)',
    feature_hash VARCHAR(64) NOT NULL COMMENT '特征向量哈希值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_code_start_window (code, start_date, window_size),
    INDEX idx_code (code),
    INDEX idx_start_date (start_date),
    INDEX idx_window_size (window_size),
    INDEX idx_feature_hash (feature_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='K线特征向量表';

-- 3. FAISS索引元数据表
CREATE TABLE IF NOT EXISTS faiss_index_meta (
    id INT AUTO_INCREMENT PRIMARY KEY,
    index_name VARCHAR(100) NOT NULL UNIQUE COMMENT '索引名称',
    window_size INT NOT NULL COMMENT '窗口大小',
    feature_dim INT NOT NULL COMMENT '特征维度',
    total_vectors INT NOT NULL COMMENT '向量总数',
    index_type VARCHAR(50) NOT NULL COMMENT '索引类型',
    index_path VARCHAR(500) NOT NULL COMMENT '索引文件路径',
    build_time TIMESTAMP NOT NULL COMMENT '构建时间',
    status ENUM('building', 'ready', 'error') DEFAULT 'building' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_window_size (window_size),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='FAISS索引元数据表';

-- 4. 查询历史表
CREATE TABLE IF NOT EXISTS query_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    query_id VARCHAR(36) NOT NULL UNIQUE COMMENT '查询ID',
    query_code VARCHAR(20) NOT NULL COMMENT '查询股票代码',
    query_start_date DATE NOT NULL COMMENT '查询起始日期',
    query_end_date DATE NOT NULL COMMENT '查询结束日期',
    window_size INT NOT NULL COMMENT '窗口大小',
    top_k INT NOT NULL COMMENT '返回结果数量',
    query_vector JSON NOT NULL COMMENT '查询向量',
    results JSON NOT NULL COMMENT '查询结果',
    query_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
    INDEX idx_query_code (query_code),
    INDEX idx_query_time (query_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询历史表';

-- 5. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'bool', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置
INSERT IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('default_window_size', '5', 'int', '默认K线窗口大小'),
('default_top_k', '10', 'int', '默认返回相似结果数量'),
('feature_normalization', 'standard', 'string', '特征标准化方法'),
('index_rebuild_interval', '24', 'int', '索引重建间隔(小时)');
