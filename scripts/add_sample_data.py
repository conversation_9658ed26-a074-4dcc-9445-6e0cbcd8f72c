#!/usr/bin/env python3
"""
添加示例数据脚本
为系统添加一些示例股票数据，确保功能可以正常演示
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from sqlalchemy import text
from shared.config.db import engine

def create_sample_stock_data():
    """创建示例股票数据"""
    
    # 示例股票列表
    sample_stocks = [
        {'code': 'sh600519', 'name': '贵州茅台', 'market': 'SH', 'industry': '食品饮料'},
        {'code': 'sz000001', 'name': '平安银行', 'market': 'SZ', 'industry': '银行'},
        {'code': 'sh600036', 'name': '招商银行', 'market': 'SH', 'industry': '银行'},
        {'code': 'sz000002', 'name': '万科A', 'market': 'SZ', 'industry': '房地产'},
        {'code': 'sh600000', 'name': '浦发银行', 'market': 'SH', 'industry': '银行'},
    ]
    
    print("📊 创建示例股票基础信息...")
    
    # 插入股票基础信息
    with engine.connect() as conn:
        for stock in sample_stocks:
            sql = """
                INSERT INTO stock_info (code, name, market, industry, status)
                VALUES (:code, :name, :market, :industry, 'active')
                ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                market = VALUES(market),
                industry = VALUES(industry),
                updated_at = CURRENT_TIMESTAMP
            """
            conn.execute(text(sql), stock)
        conn.commit()
    
    print(f"✅ 已添加 {len(sample_stocks)} 只股票的基础信息")
    
    # 为每只股票生成历史数据
    print("📈 生成示例历史数据...")
    
    end_date = date.today()
    start_date = end_date - timedelta(days=365)  # 一年的数据
    
    for stock in sample_stocks:
        print(f"  生成 {stock['code']} ({stock['name']}) 的数据...")
        
        # 生成模拟的股票数据
        daily_data = generate_stock_data(
            stock['code'], 
            start_date, 
            end_date,
            base_price=get_base_price(stock['code'])
        )
        
        # 插入到数据库
        daily_data.to_sql('stock_daily', engine, if_exists='append', index=False)
    
    print("✅ 示例历史数据生成完成")

def get_base_price(stock_code):
    """根据股票代码获取基础价格"""
    base_prices = {
        'sh600519': 1800,  # 贵州茅台
        'sz000001': 15,    # 平安银行
        'sh600036': 45,    # 招商银行
        'sz000002': 25,    # 万科A
        'sh600000': 12,    # 浦发银行
    }
    return base_prices.get(stock_code, 20)

def generate_stock_data(stock_code, start_date, end_date, base_price=20):
    """生成模拟的股票数据"""
    
    # 生成日期序列（只包含工作日）
    dates = pd.bdate_range(start=start_date, end=end_date)
    
    # 初始化数据
    data = []
    current_price = base_price
    
    np.random.seed(hash(stock_code) % 2**32)  # 为每只股票设置不同的随机种子
    
    for i, date in enumerate(dates):
        # 生成随机波动
        daily_return = np.random.normal(0.001, 0.03)  # 平均0.1%日收益，3%波动率
        
        # 计算价格
        prev_close = current_price
        current_price = prev_close * (1 + daily_return)
        
        # 生成开盘价（基于前收盘价的小幅波动）
        open_price = prev_close * (1 + np.random.normal(0, 0.01))
        
        # 生成最高价和最低价
        high_low_range = abs(np.random.normal(0.02, 0.01))  # 2%的平均振幅
        high_price = max(open_price, current_price) * (1 + high_low_range/2)
        low_price = min(open_price, current_price) * (1 - high_low_range/2)
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, current_price)
        low_price = min(low_price, open_price, current_price)
        
        # 生成成交量（基于价格波动）
        volume_base = 1000000  # 100万股基础成交量
        volume_multiplier = 1 + abs(daily_return) * 10  # 波动越大成交量越大
        volume = int(volume_base * volume_multiplier * np.random.uniform(0.5, 2.0))
        
        # 计算其他指标
        change_pct = (current_price - prev_close) / prev_close * 100
        change_amount = current_price - prev_close
        amplitude = (high_price - low_price) / prev_close * 100
        turnover_rate = volume / 100000000 * 100  # 假设总股本1亿股
        
        # 成交额
        avg_price = (high_price + low_price + open_price + current_price) / 4
        amount = volume * avg_price
        
        data.append({
            'code': stock_code,
            '日期': date.date(),
            '开盘': round(open_price, 2),
            '收盘': round(current_price, 2),
            '最高': round(high_price, 2),
            '最低': round(low_price, 2),
            '成交量': volume,
            '成交额': round(amount, 2),
            '振幅': round(amplitude, 2),
            '涨跌幅': round(change_pct, 2),
            '涨跌额': round(change_amount, 2),
            '换手率': round(turnover_rate, 2)
        })
    
    return pd.DataFrame(data)

def check_data_status():
    """检查数据状态"""
    print("\n📊 数据状态检查:")
    
    with engine.connect() as conn:
        # 检查股票信息
        stock_count = conn.execute(text("SELECT COUNT(*) FROM stock_info")).scalar()
        print(f"  股票信息: {stock_count} 只")
        
        # 检查日线数据
        daily_count = conn.execute(text("SELECT COUNT(*) FROM stock_daily")).scalar()
        print(f"  日线数据: {daily_count} 条")
        
        # 检查日期范围
        date_range = conn.execute(text("""
            SELECT MIN(日期) as min_date, MAX(日期) as max_date 
            FROM stock_daily
        """)).fetchone()
        
        if date_range and date_range[0]:
            print(f"  数据日期范围: {date_range[0]} 至 {date_range[1]}")
        
        # 检查每只股票的数据量
        stock_data_count = conn.execute(text("""
            SELECT si.code, si.name, COUNT(sd.日期) as data_count
            FROM stock_info si
            LEFT JOIN stock_daily sd ON si.code = sd.code
            GROUP BY si.code, si.name
            ORDER BY si.code
        """)).fetchall()
        
        print("  各股票数据量:")
        for row in stock_data_count:
            print(f"    {row[0]} ({row[1]}): {row[2]} 条")

def main():
    """主函数"""
    print("🚀 开始添加示例数据...")
    
    try:
        # 创建示例数据
        create_sample_stock_data()
        
        # 检查数据状态
        check_data_status()
        
        print("\n🎉 示例数据添加完成！")
        print("\n💡 现在你可以:")
        print("  1. 在前端界面查看股票列表")
        print("  2. 查看股票K线图")
        print("  3. 提取特征向量")
        print("  4. 构建FAISS索引")
        print("  5. 进行相似性搜索")
        
    except Exception as e:
        print(f"❌ 添加示例数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
