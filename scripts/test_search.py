#!/usr/bin/env python3
"""
测试相似性搜索功能
"""

import requests
import json
from datetime import date, timedelta

API_BASE_URL = "http://localhost:8000/api/v1"

def test_search_step_by_step():
    """分步测试搜索功能"""
    print("🔍 分步测试相似性搜索功能")
    print("=" * 40)
    
    # 1. 检查索引是否存在
    print("1. 检查FAISS索引...")
    try:
        response = requests.get(f"{API_BASE_URL}/features/indexes")
        if response.status_code == 200:
            result = response.json()
            indexes = result.get("indexes", [])
            print(f"✅ 找到 {len(indexes)} 个索引")
            for idx in indexes:
                print(f"   - {idx.get('index_name')}: {idx.get('total_vectors')} 个向量")
        else:
            print("❌ 获取索引列表失败")
            return False
    except Exception as e:
        print(f"❌ 索引检查异常: {e}")
        return False
    
    # 2. 如果没有索引，先构建一个
    if not indexes:
        print("\n2. 构建测试索引...")
        try:
            stock_codes = ["sh600519", "sz000001"]
            params = "?window_size=5&index_name=simple_test"
            response = requests.post(f"{API_BASE_URL}/features/build-index{params}", json=stock_codes)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 索引构建成功: {result.get('total_vectors')} 个向量")
                index_name = "simple_test"
            else:
                print(f"❌ 索引构建失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 索引构建异常: {e}")
            return False
    else:
        index_name = indexes[0].get('index_name', 'test_index')
        print(f"✅ 使用现有索引: {index_name}")
    
    # 3. 测试相似性搜索
    print(f"\n3. 测试相似性搜索 (使用索引: {index_name})...")
    try:
        # 使用简单的日期格式
        search_data = {
            "query_stock_code": "sh600519",
            "query_start_date": "2024-01-01",
            "query_end_date": "2024-01-10",
            "window_size": 5,
            "top_k": 3,
            "index_name": index_name
        }
        
        print(f"   查询参数: {search_data}")
        
        response = requests.post(f"{API_BASE_URL}/search/similar", json=search_data)
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功!")
            print(f"   - 查询ID: {result.get('query_id')}")
            print(f"   - 结果数量: {result.get('total_results')}")
            
            results = result.get('results', [])
            for i, match in enumerate(results[:3]):
                print(f"   - #{i+1} {match.get('stock_code')} (相似度: {match.get('similarity_score', 0):.4f})")
            
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索测试异常: {e}")
        return False

def main():
    """主函数"""
    success = test_search_step_by_step()
    
    if success:
        print("\n🎉 相似性搜索功能测试通过!")
        print("\n💡 现在可以在前端界面使用完整的搜索功能了")
    else:
        print("\n⚠️  相似性搜索功能需要进一步调试")
    
    return success

if __name__ == "__main__":
    main()
