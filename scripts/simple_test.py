#!/usr/bin/env python3
"""
简化的系统测试脚本
"""

import requests
import json

API_BASE_URL = "http://localhost:8000/api/v1"

def test_basic_functions():
    """测试基本功能"""
    print("🧪 简化系统测试")
    print("=" * 30)
    
    # 1. 健康检查
    print("1. 健康检查...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ API服务正常")
        else:
            print("❌ API服务异常")
    except:
        print("❌ 无法连接API")
    
    # 2. 数据统计
    print("\n2. 数据统计...")
    try:
        response = requests.get(f"{API_BASE_URL}/data/stats")
        if response.status_code == 200:
            result = response.json()
            stats = result["stats"]
            print(f"✅ 股票数量: {stats['stock_count']}")
            print(f"✅ 数据量: {stats['daily_data_count']}")
        else:
            print("❌ 数据统计失败")
    except Exception as e:
        print(f"❌ 数据统计异常: {e}")
    
    # 3. 股票列表
    print("\n3. 股票列表...")
    try:
        response = requests.get(f"{API_BASE_URL}/data/stocks")
        if response.status_code == 200:
            result = response.json()
            stocks = result["data"]
            print(f"✅ 获取到 {len(stocks)} 只股票")
            for stock in stocks[:3]:
                print(f"   - {stock['code']} ({stock['name']})")
        else:
            print("❌ 股票列表获取失败")
    except Exception as e:
        print(f"❌ 股票列表异常: {e}")
    
    # 4. 特征提取
    print("\n4. 特征提取...")
    try:
        data = {
            "stock_code": "sh600519",
            "window_size": 5,
            "start_date": "2024-01-01",
            "end_date": "2024-02-01",
            "normalize": True
        }
        response = requests.post(f"{API_BASE_URL}/features/extract", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 特征提取成功: {result['feature_count']} 个向量")
        else:
            print(f"❌ 特征提取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 特征提取异常: {e}")
    
    print("\n🎉 基本功能测试完成！")
    print("\n💡 系统已就绪，可以使用前端界面:")
    print("   http://localhost:8501")

if __name__ == "__main__":
    test_basic_functions()
